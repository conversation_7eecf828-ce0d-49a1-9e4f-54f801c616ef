{"catalog": {"all": "All", "apps": "Apps", "decaCloudTools": "DECA Cloud", "buildInTools": "Build-in tools", "aiTools": "AI Tools", "popularApps": "Popular Apps", "selectedAppTool": "Selected App/Tool", "search": {"placeholder": "Search apps and tools"}}, "error": {"required": "This field is required.", "pattern": "The value must match the pattern {pattern}."}, "button": {"add": "Add", "cancel": "Cancel", "close": "Close", "confirm": "Confirm", "save": "Save", "delete": "Delete", "edit": "Edit", "duplicate": "Duplicate", "export": "Export", "reconnect": "Reconnect", "saveAsTemplate": "Save as template", "test": "Test", "continue": "Continue"}, "form": {"requiredFields": "To continue, finish required fields", "chooseCredential": "To continue, choose a credential", "chooseAction": "To continue, choose an action", "chooseTrigger": "To continue, choose a trigger"}, "function": {"docs": "docs.jsonata.org", "all": "All", "string": "String", "number": "Number", "array": "Array", "object": "Object", "boolean": "Boolean", "date": "Date", "higher-order": "Higher-Order", "syntax": "Syntax", "example": "Example", "Casts the arg parameter to a string using JSONata casting rules": "Casts the arg parameter to a string using JSONata casting rules", "Returns the number of characters in the string str": "Returns the number of characters in the string str", "Returns a string containing characters from str starting at position start": "Returns a string containing characters from str starting at position start", "Returns the substring before the first occurrence of chars in str": "Returns the substring before the first occurrence of chars in str", "Returns the substring after the first occurrence of chars in str": "Returns the substring after the first occurrence of chars in str", "Returns a string with all characters converted to uppercase": "Returns a string with all characters converted to uppercase", "Returns a string with all characters converted to lowercase": "Returns a string with all characters converted to lowercase", "Normalizes and trims all whitespace characters in str": "Normalizes and trims all whitespace characters in str", "Returns a copy of str with extra padding to reach the specified width": "Returns a copy of str with extra padding to reach the specified width", "Returns true if str is matched by pattern, otherwise false": "Returns true if str is matched by pattern, otherwise false", "Splits str into an array of substrings using separator": "Splits str into an array of substrings using separator", "Joins an array of strings into a single concatenated string": "Joins an array of strings into a single concatenated string", "Applies str to pattern regex and returns array of match objects": "Applies str to pattern regex and returns array of match objects", "Finds occurrences of pattern in str and replaces them with replacement": "Finds occurrences of pattern in str and replaces them with replacement", "Converts an ASCII string to a base 64 representation": "Converts an ASCII string to a base 64 representation", "Converts base 64 encoded bytes to a string using UTF-8": "Converts base 64 encoded bytes to a string using UTF-8", "Encodes a URL component by replacing special characters with escape sequences": "Encodes a URL component by replacing special characters with escape sequences", "Encodes a URL by replacing special characters with escape sequences": "Encodes a URL by replacing special characters with escape sequences", "Decodes a URL component previously created by encodeUrlComponent": "Decodes a URL component previously created by encodeUrlComponent", "Decodes a URL previously created by encodeUrl": "Decodes a URL previously created by encodeUrl", "Casts the arg parameter to a number using JSONata casting rules": "Casts the arg parameter to a number using JSONata casting rules", "Returns the absolute value of the number parameter": "Returns the absolute value of the number parameter", "Returns the value of number rounded down to the nearest integer": "Returns the value of number rounded down to the nearest integer", "Returns the value of number rounded up to the nearest integer": "Returns the value of number rounded up to the nearest integer", "Returns the value of number rounded to the specified decimal places": "Returns the value of number rounded to the specified decimal places", "Returns the value of base raised to the power of exponent": "Returns the value of base raised to the power of exponent", "Returns the square root of the number parameter": "Returns the square root of the number parameter", "Returns a pseudo random number between 0 and 1": "Returns a pseudo random number between 0 and 1", "Formats a number to a decimal representation as specified by picture string": "Formats a number to a decimal representation as specified by picture string", "Formats a number to an integer in the specified number base": "Formats a number to an integer in the specified number base", "Formats a number to an integer representation as specified by picture string": "Formats a number to an integer representation as specified by picture string", "Parses a string to an integer using the format specified by picture string": "Parses a string to an integer using the format specified by picture string", "Returns the arithmetic sum of an array of numbers": "Returns the arithmetic sum of an array of numbers", "Returns the maximum number in an array of numbers": "Returns the maximum number in an array of numbers", "Returns the minimum number in an array of numbers": "Returns the minimum number in an array of numbers", "Returns the mean value of an array of numbers": "Returns the mean value of an array of numbers", "Returns the number of items in the array parameter": "Returns the number of items in the array parameter", "Returns an array containing values from array1 followed by values from array2": "Returns an array containing values from array1 followed by values from array2", "Returns an array containing all values from array parameter, but sorted into order": "Returns an array containing all values from array parameter, but sorted into order", "Returns an array containing all values from array parameter, but in reverse order": "Returns an array containing all values from array parameter, but in reverse order", "Returns an array containing all values from array parameter, but shuffled into random order": "Returns an array containing all values from array parameter, but shuffled into random order", "Returns an array containing all values from array parameter, but with duplicates removed": "Returns an array containing all values from array parameter, but with duplicates removed", "Returns a convolved (zipped) array containing grouped arrays of values from multiple arrays": "Returns a convolved (zipped) array containing grouped arrays of values from multiple arrays", "Returns an array containing the keys in the object": "Returns an array containing the keys in the object", "Returns the value associated with key in object": "Returns the value associated with key in object", "Splits an object containing key/value pairs into an array of objects": "Splits an object containing key/value pairs into an array of objects", "Merges an array of objects into a single object containing all key/value pairs": "Merges an array of objects into a single object containing all key/value pairs", "Returns an object containing only key/value pairs that satisfy the predicate function": "Returns an object containing only key/value pairs that satisfy the predicate function", "Returns an array containing values returned by function when applied to each key/value pair": "Returns an array containing values returned by function when applied to each key/value pair", "Casts the argument to a Boolean using JSONata casting rules": "Casts the argument to a Boolean using JSONata casting rules", "Returns Boolean NOT on the argument (arg is first cast to boolean)": "Returns Boolean NOT on the argument (arg is first cast to boolean)", "Returns true if the arg expression evaluates to a value, false if it does not match anything": "Returns true if the arg expression evaluates to a value, false if it does not match anything", "Generates a UTC timestamp in ISO 8601 compatible format": "Generates a UTC timestamp in ISO 8601 compatible format", "Returns the number of milliseconds since the Unix Epoch (1 January, 1970 UTC)": "Returns the number of milliseconds since the Unix Epoch (1 January, 1970 UTC)", "Converts milliseconds since Unix Epoch to a formatted string representation": "Converts milliseconds since Unix Epoch to a formatted string representation", "Converts a timestamp string to milliseconds since the Unix Epoch": "Converts a timestamp string to milliseconds since the Unix Epoch", "Returns an array containing the results of applying function to each value in array": "Returns an array containing the results of applying function to each value in array", "Returns an array containing only values that satisfy the function predicate": "Returns an array containing only values that satisfy the function predicate", "Returns the one and only one value that satisfies the function predicate": "Returns the one and only one value that satisfies the function predicate", "Returns an aggregated value by applying function successively to each value in array": "Returns an aggregated value by applying function successively to each value in array"}, "combobox": {"dataPoints": "Data Points", "functions": "Functions", "more": "more", "less": "less", "placeholder": "Type or use / to insert variable"}, "keyvalue": {"emptyKey": "Enter a key", "duplicateKey": "Duplicate key"}}