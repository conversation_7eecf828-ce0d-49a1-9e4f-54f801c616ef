{"catalog": {"all": "すべて", "apps": "アプリ", "decaCloudTools": "DECA Cloud", "buildInTools": "ビルトインツール", "aiTools": "AI Tools", "popularApps": "人気のアプリ", "selectedAppTool": "選択されたアプリ/ツール", "search": {"placeholder": "アプリやツールを検索"}}, "error": {"required": "必須項目です", "pattern": "値は{pattern}のパターンに一致する必要があります"}, "button": {"add": "追加", "cancel": "キャンセル", "close": "閉じる", "confirm": "確認", "save": "保存", "delete": "削除", "edit": "編集", "duplicate": "複製", "export": "エクスポート", "reconnect": "再接続", "saveAsTemplate": "テンプレートとして保存", "test": "テスト", "continue": "続ける"}, "form": {"requiredFields": "続行するには、必須項目を入力してください", "chooseCredential": "続行するには、認証情報を選択してください", "chooseAction": "続行するには、アクションを選択してください", "chooseTrigger": "続行するには、トリガーを選択してください"}, "function": {"docs": "docs.jsonata.org", "all": "すべて", "string": "文字列", "number": "数値", "array": "配列", "object": "オブジェクト", "boolean": "ブール値", "date": "日付", "higher-order": "高階関数", "syntax": "構文", "example": "例", "Casts the arg parameter to a string using JSONata casting rules": "argパラメータをJSONataキャストルールを使用して文字列にキャストします", "Returns the number of characters in the string str": "文字列strの文字数を返します", "Returns a string containing characters from str starting at position start": "位置startから始まるstrの文字を含む文字列を返します", "Returns the substring before the first occurrence of chars in str": "str内でcharsが最初に出現する前の部分文字列を返します", "Returns the substring after the first occurrence of chars in str": "str内でcharsが最初に出現した後の部分文字列を返します", "Returns a string with all characters converted to uppercase": "すべての文字を大文字に変換した文字列を返します", "Returns a string with all characters converted to lowercase": "すべての文字を小文字に変換した文字列を返します", "Normalizes and trims all whitespace characters in str": "str内のすべての空白文字を正規化してトリムします", "Returns a copy of str with extra padding to reach the specified width": "指定された幅に達するように追加のパディングを施したstrのコピーを返します", "Returns true if str is matched by pattern, otherwise false": "strがパターンにマッチする場合はtrue、そうでなければfalseを返します", "Splits str into an array of substrings using separator": "separatorを使用してstrを部分文字列の配列に分割します", "Joins an array of strings into a single concatenated string": "文字列の配列を単一の連結文字列に結合します", "Applies str to pattern regex and returns array of match objects": "strをパターン正規表現に適用し、マッチオブジェクトの配列を返します", "Finds occurrences of pattern in str and replaces them with replacement": "str内でパターンの出現を検索し、replacementで置換します", "Converts an ASCII string to a base 64 representation": "ASCII文字列をbase64表現に変換します", "Converts base 64 encoded bytes to a string using UTF-8": "base64エンコードされたバイトをUTF-8を使用して文字列に変換します", "Encodes a URL component by replacing special characters with escape sequences": "特殊文字をエスケープシーケンスに置き換えてURLコンポーネントをエンコードします", "Encodes a URL by replacing special characters with escape sequences": "特殊文字をエスケープシーケンスに置き換えてURLをエンコードします", "Decodes a URL component previously created by encodeUrlComponent": "encodeUrlComponentで作成されたURLコンポーネントをデコードします", "Decodes a URL previously created by encodeUrl": "encodeUrlで作成されたURLをデコードします", "Casts the arg parameter to a number using JSONata casting rules": "argパラメータをJSONataキャストルールを使用して数値にキャストします", "Returns the absolute value of the number parameter": "数値パラメータの絶対値を返します", "Returns the value of number rounded down to the nearest integer": "数値を最も近い整数に切り下げた値を返します", "Returns the value of number rounded up to the nearest integer": "数値を最も近い整数に切り上げた値を返します", "Returns the value of number rounded to the specified decimal places": "数値を指定された小数点以下の桁数に丸めた値を返します", "Returns the value of base raised to the power of exponent": "baseをexponent乗した値を返します", "Returns the square root of the number parameter": "数値パラメータの平方根を返します", "Returns a pseudo random number between 0 and 1": "0と1の間の疑似乱数を返します", "Formats a number to a decimal representation as specified by picture string": "数値をpicture文字列で指定された10進表現にフォーマットします", "Formats a number to an integer in the specified number base": "数値を指定された数値基数の整数にフォーマットします", "Formats a number to an integer representation as specified by picture string": "数値をpicture文字列で指定された整数表現にフォーマットします", "Parses a string to an integer using the format specified by picture string": "picture文字列で指定されたフォーマットを使用して文字列を整数にパースします", "Returns the arithmetic sum of an array of numbers": "数値の配列の算術和を返します", "Returns the maximum number in an array of numbers": "数値の配列の最大値を返します", "Returns the minimum number in an array of numbers": "数値の配列の最小値を返します", "Returns the mean value of an array of numbers": "数値の配列の平均値を返します", "Returns the number of items in the array parameter": "配列パラメータの項目数を返します", "Returns an array containing values from array1 followed by values from array2": "array1の値の後にarray2の値が続く配列を返します", "Returns an array containing all values from array parameter, but sorted into order": "配列パラメータのすべての値を含む配列を返しますが、順序にソートされています", "Returns an array containing all values from array parameter, but in reverse order": "配列パラメータのすべての値を含む配列を返しますが、逆順になっています", "Returns an array containing all values from array parameter, but shuffled into random order": "配列パラメータのすべての値を含む配列を返しますが、ランダムな順序にシャッフルされています", "Returns an array containing all values from array parameter, but with duplicates removed": "配列パラメータのすべての値を含む配列を返しますが、重複が削除されています", "Returns a convolved (zipped) array containing grouped arrays of values from multiple arrays": "複数の配列から値のグループ化された配列を含む畳み込み（ジップ）配列を返します", "Returns an array containing the keys in the object": "オブジェクト内のキーを含む配列を返します", "Returns the value associated with key in object": "オブジェクト内のキーに関連付けられた値を返します", "Splits an object containing key/value pairs into an array of objects": "キー/値のペアを含むオブジェクトをオブジェクトの配列に分割します", "Merges an array of objects into a single object containing all key/value pairs": "オブジェクトの配列をすべてのキー/値のペアを含む単一のオブジェクトにマージします", "Returns an object containing only key/value pairs that satisfy the predicate function": "述語関数を満たすキー/値のペアのみを含むオブジェクトを返します", "Returns an array containing values returned by function when applied to each key/value pair": "各キー/値のペアに適用されたときに関数によって返される値を含む配列を返します", "Casts the argument to a Boolean using JSONata casting rules": "引数をJSONataキャストルールを使用してブール値にキャストします", "Returns Boolean NOT on the argument (arg is first cast to boolean)": "引数に対してブールNOTを返します（argは最初にブール値にキャストされます）", "Returns true if the arg expression evaluates to a value, false if it does not match anything": "arg式が値に評価される場合はtrue、何にもマッチしない場合はfalseを返します", "Generates a UTC timestamp in ISO 8601 compatible format": "ISO 8601互換形式のUTCタイムスタンプを生成します", "Returns the number of milliseconds since the Unix Epoch (1 January, 1970 UTC)": "Unixエポック（1970年1月1日UTC）からのミリ秒数を返します", "Converts milliseconds since Unix Epoch to a formatted string representation": "Unixエポックからのミリ秒をフォーマットされた文字列表現に変換します", "Converts a timestamp string to milliseconds since the Unix Epoch": "タイムスタンプ文字列をUnixエポックからのミリ秒に変換します", "Returns an array containing the results of applying function to each value in array": "配列内の各値に関数を適用した結果を含む配列を返します", "Returns an array containing only values that satisfy the function predicate": "関数述語を満たす値のみを含む配列を返します", "Returns the one and only one value that satisfies the function predicate": "関数述語を満たす唯一の値を返します", "Returns an aggregated value by applying function successively to each value in array": "配列内の各値に関数を順次適用して集約値を返します"}, "combobox": {"dataPoints": "データポイント", "functions": "関数", "more": "もっと見る", "less": "閉じる", "placeholder": "入力するか / を使用して変数を挿入"}, "keyvalue": {"emptyKey": "キーを入力してください", "duplicateKey": "重複するキーがあります"}}