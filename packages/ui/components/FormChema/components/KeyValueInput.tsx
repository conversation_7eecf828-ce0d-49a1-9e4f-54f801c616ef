import { ActionIcon, Box, Group, Paper, Stack, Text, TextInput, rem } from '@mantine/core';
import { createStyles } from '@mantine/emotion';
import { IconCheck, IconPencil, IconX } from '@tabler/icons-react';
import { useTranslate } from '@tolgee/react';
import { debounce } from 'lodash';
import type React from 'react';
import { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import { getDefaultValue } from '../../../utils/schema';
import { withContainer } from '../../hoc/withContainer';
import { withReactHookForm } from '../../hoc/withReactHookForm';
import type { SchemaField as SchemaFieldType } from '../type';
import { generateKeyValueId, getUniqueKeyName } from '../utils/field';
import { parseJsonToKeyValueArray, parseKeyValueArrayToJson } from '../utils/safetyParseJson';
import type { ComboboxNode } from './ComboboxSelectDataPoint';
import { ActionIconRemove, AddNewValueButton } from './FieldUtils';
import { Label } from './Label';
import { SchemaFieldWrapper } from './SchemaFieldWrapper';

const useStyles = createStyles((theme) => ({
  paper: {
    position: 'relative',
    padding: theme.spacing.xs,
    boxShadow: 'none',
    backgroundColor: theme.colors.decaLight[0],
  },
  container: {
    '& label': {
      display: 'none',
    },
  },
}));

interface KeyHeaderProps {
  keyName: string;
  onSave: (newKeyName: string) => void;
  onCancel?: () => void;
}

export const KeyHeader = ({ keyName, onSave, onCancel }: KeyHeaderProps) => {
  const [isEditing, setIsEditing] = useState(false);
  const [newKeyName, setNewKeyName] = useState(keyName);

  const handleStartEditing = useCallback(() => {
    setIsEditing(true);
  }, []);

  const handleSaveKeyName = useCallback(() => {
    setIsEditing(false);
    onSave(newKeyName);
  }, [newKeyName, onSave]);

  const handleCancelEditing = useCallback(() => {
    setIsEditing(false);
  }, []);

  useEffect(() => {
    setNewKeyName(keyName);
  }, [keyName]);

  if (isEditing) {
    return (
      <>
        <TextInput
          autoFocus
          value={newKeyName}
          onChange={(e) => setNewKeyName(e.target.value)}
          onKeyDown={(e) => {
            switch (e.key) {
              case 'Escape': {
                e.preventDefault();
                onCancel?.();
                break;
              }
              case 'Enter': {
                if (newKeyName) {
                  handleSaveKeyName();
                }
                break;
              }
            }
          }}
          size='xs'
          w='100%'
          maw='50%'
        />
        <ActionIcon
          size='xs'
          variant='subtle'
          c='decaGrey.6'
          onClick={handleSaveKeyName}
          disabled={!newKeyName}
        >
          <IconCheck size={14} />
        </ActionIcon>
        <ActionIcon size='xs' variant='subtle' c='decaGrey.6' onClick={handleCancelEditing}>
          <IconX size={14} />
        </ActionIcon>
      </>
    );
  }

  return (
    <>
      <Text fz='sm' fw={500}>
        {keyName}
      </Text>
      <ActionIcon size='xs' variant='subtle' c='decaGrey.6' onClick={handleStartEditing}>
        <IconPencil size={14} />
      </ActionIcon>
    </>
  );
};

interface SchemaKeyValueInputProps {
  schema: SchemaFieldType;
  value?: Record<string, any>;
  haveDefault?: boolean;
  onChange?: (value: Record<string, any>) => void;
  isDefault?: boolean;
  withContainer?: boolean;
  showHeader?: boolean;
  error?: string;
  previousNodes?: ComboboxNode[];
}

const isDuplicate = (updated: any[]) => {
  return updated.some((o) => updated.filter((o2) => o2.key === o.key).length > 1);
};

export const SchemaKeyValueInput: React.FC<SchemaKeyValueInputProps> = ({
  schema,
  value,
  onChange,
  error,
  showHeader = true,
  previousNodes,
}) => {
  const { t } = useTranslate('common');
  const defaultValue = useRef(parseJsonToKeyValueArray(getDefaultValue(schema)));
  const [orderedFields, setOrderedFields] = useState(() =>
    parseJsonToKeyValueArray(value || defaultValue.current)
  );
  const [errorState, setErrorState] = useState<string | undefined>(error);

  const defaultSchema = useMemo(() => schema?.default, [schema]);

  const listKeyRef = useRef<string[]>([]);
  const listIdRef = useRef<string[]>([]);
  const isInternalUpdateRef = useRef(false);
  const { classes } = useStyles();

  // Create debounced function once
  const debouncedValidateAndSave = useRef(
    debounce(
      (
        updated: any[],
        newKeyName: string,
        onChangeCallback: ((value: any) => void) | undefined
      ) => {
        const isDuplicateKey = isDuplicate(updated);
        if (!newKeyName) {
          setErrorState('keyvalue.emptyKey');
          return;
        }
        if (isDuplicateKey) {
          setErrorState('keyvalue.duplicateKey');
          return;
        }
        setErrorState(undefined);
        onChangeCallback?.(parseKeyValueArrayToJson(updated));
      },
      300
    )
  );

  // Cleanup debounced function on unmount
  useEffect(() => {
    const debouncedFn = debouncedValidateAndSave.current;
    return () => {
      debouncedFn.cancel();
    };
  }, []);

  useEffect(() => {
    if (isInternalUpdateRef.current) {
      isInternalUpdateRef.current = false;
      return;
    }

    const currentValue = parseJsonToKeyValueArray(value || defaultValue.current);

    if (listKeyRef.current.length) {
      setOrderedFields((prev) => {
        let markedIndex: number[] = [];
        const newOrderedFields = listKeyRef.current.map((key, index) => {
          const item = currentValue.find((o) => o.key === key && !markedIndex.includes(index));
          if (!item) {
            return { ...prev[index], id: listIdRef.current[index] };
          }
          markedIndex.push(index);
          return { ...item, id: listIdRef.current[index] };
        });
        markedIndex = [];
        return newOrderedFields;
      });
    } else {
      const newListId = currentValue.map(() => generateKeyValueId());
      listIdRef.current = newListId;
      listKeyRef.current = currentValue
        .map((o) => o.key)
        .sort((key) => {
          if (defaultSchema?.[key]) {
            return -1;
          }
          return 0;
        });
      setOrderedFields(currentValue.map((o, index) => ({ ...o, id: newListId[index] })));
    }
  }, [defaultSchema, value]);

  const handleValueChange = useCallback(
    (id: string, newValue: any) => {
      const updated = orderedFields.map((o) => (o.id === id ? { ...o, value: newValue } : o));
      setOrderedFields(updated);
      const isDuplicateKey = isDuplicate(orderedFields);
      if (isDuplicateKey) {
        setErrorState('keyvalue.duplicateKey');
        return;
      }

      setErrorState(undefined);
      onChange?.(parseKeyValueArrayToJson(updated));
    },
    [onChange, orderedFields]
  );

  const handleAddField = useCallback(() => {
    const base = `newField_${orderedFields.length}`;
    const newKey = getUniqueKeyName(base, parseJsonToKeyValueArray(orderedFields));
    if (isDuplicate(orderedFields)) {
      setErrorState('keyvalue.duplicateKey');
    }

    const newId = generateKeyValueId();
    const updated = [...orderedFields, { id: newId, key: newKey, value: '' }];

    listIdRef.current = [...listIdRef.current, newId];
    listKeyRef.current = [...listKeyRef.current, newKey];
    setOrderedFields(updated);
    setErrorState(undefined);
    onChange?.(parseKeyValueArrayToJson(updated));
  }, [onChange, orderedFields]);

  const handleRemoveField = useCallback(
    (index: number) => {
      const rest = orderedFields.filter((_, i) => i !== index);
      const isDuplicateKey = isDuplicate(rest);
      if (!isDuplicateKey) {
        setErrorState(undefined);
      }
      listIdRef.current = listIdRef.current.filter((_, i) => i !== index);
      listKeyRef.current = listKeyRef.current.filter((_, i) => i !== index);
      onChange?.(parseKeyValueArrayToJson(rest));
      setOrderedFields(rest);
    },
    [onChange, orderedFields]
  );

  const handleChangeKeyName = useCallback(
    (id: string, newKeyName: string) => {
      isInternalUpdateRef.current = true;
      const updated = orderedFields.map((o) => (o.id === id ? { ...o, key: newKeyName } : o));
      listKeyRef.current = updated.map((o) => o.key);
      setOrderedFields(updated);
      // Use the debounced function for validation and saving
      debouncedValidateAndSave.current(updated, newKeyName, onChange);
    },
    [onChange, orderedFields]
  );

  const renderField = useCallback(
    (id: string, fieldSchema: any) => {
      const v = orderedFields.find((o) => o.id === id);
      if (!v || !id) return null;
      if (fieldSchema.type === 'keyvalue') {
        return (
          <SchemaKeyValueInput
            schema={fieldSchema}
            value={v?.value?.default || v?.value}
            onChange={(nv) => handleValueChange(id, nv)}
            isDefault
            withContainer={false}
            previousNodes={previousNodes}
          />
        );
      }
      return (
        <SchemaFieldWrapper
          propertyKey={v.key}
          propertySchema={fieldSchema}
          value={v?.value?.default || v?.value}
          onChange={(nv) => handleValueChange(id, nv)}
          error={error}
          previousNodes={previousNodes}
        />
      );
    },
    [orderedFields, error, previousNodes, handleValueChange]
  );

  return (
    <Stack gap={0}>
      {showHeader && <Label schema={schema} />}
      <Stack mt='xs' gap='sm' className={classes.container}>
        {orderedFields.map((row, index) => {
          const defaultSchemaByKey = defaultSchema?.[row.key];
          return defaultSchemaByKey ? (
            <Paper key={row.id} className={classes.paper}>
              <Group
                gap='xs'
                align='center'
                wrap='nowrap'
                h={rem(30)}
                mb={rem(6)}
                justify='space-between'
              >
                <Group gap='xs'>
                  <KeyHeader
                    keyName={row.key}
                    onSave={(newKeyName) => handleChangeKeyName(row.id, newKeyName)}
                    onCancel={() => handleRemoveField(index)}
                  />
                </Group>
                <ActionIconRemove onClick={() => handleRemoveField(index)} />
              </Group>
              <Box mih={rem(48)}>
                {renderField(row.id, defaultSchemaByKey || { type: 'text', name: row.key })}
              </Box>
            </Paper>
          ) : (
            <Group key={row.id} gap='xs' align='center' wrap='nowrap' h={rem(30)} mb={rem(6)}>
              <TextInput
                value={row.key}
                size='md'
                onChange={(e) => handleChangeKeyName(row.id, e.target.value)}
              />
              {renderField(row.id, defaultSchemaByKey || { type: 'text', name: row.key })}
              <ActionIconRemove onClick={() => handleRemoveField(index)} />
            </Group>
          );
        })}
        {errorState && <Text c='red'>{t(errorState, { ns: 'common' })}</Text>}
      </Stack>
      <AddNewValueButton onClick={handleAddField} />
    </Stack>
  );
};

export const SchemaKeyValueInputWithContainer = withContainer(SchemaKeyValueInput);
export const FormKeyValueInput = withReactHookForm(SchemaKeyValueInput);
export const FormKeyValueInputWithContainer = withReactHookForm(SchemaKeyValueInputWithContainer);
