import { ActionIcon, Button, Group, Text } from '@mantine/core';
import { IconPlus, IconTrash } from '@tabler/icons-react';
import { useTranslation } from 'react-i18next';
import type { ComponentConfig } from '../type';
import type { ComboboxNode } from './ComboboxSelectDataPoint';
import { Label } from './Label';

export const getLabel = (schema: Record<string, any>, rightSection?: React.ReactNode) => {
  if (!schema.displayName) {
    return undefined;
  }

  return <Label schema={schema} rightSection={rightSection} />;
};

export const ActionIconRemove = ({
  onClick,
  leftSection,
  rightSection,
}: { onClick: () => void; leftSection?: React.ReactNode; rightSection?: React.ReactNode }) => {
  return (
    <ActionIcon size='xs' color='decaRed.5' variant='transparent' onClick={onClick}>
      {leftSection}
      <IconTrash size={16} />
      {rightSection}
    </ActionIcon>
  );
};

export const renderFieldComponent = (
  propertyKey: string,
  propertySchema: any,
  value: any,
  onChange: (value: any) => void,
  error?: string,
  schemaConfigs?: Record<string, ComponentConfig<any>>,
  previousNodes?: ComboboxNode[],
  isVisible?: boolean,
  classNames?: string
) => {
  // Use the provided schemaConfigs or return null if not available
  if (!schemaConfigs) {
    return null;
  }

  const defaultValue = propertySchema?.default;
  const type = propertySchema?.type;

  const config = Object.values(schemaConfigs).find(
    (config: ComponentConfig<any>) => Array.isArray(config.types) && config.types.includes(type)
  );
  const FieldComponent = config?.Component;

  if (type === 'credential' && propertySchema.hidden) {
    return null;
  }

  if (!FieldComponent) {
    return (
      <Text c='blue' fw={500} size='lg' ta='center' key={propertyKey}>
        Unsupported schema type: {JSON.stringify(type)}
      </Text>
    );
  }
  if (isVisible === false) {
    return null;
  }

  return (
    <FieldComponent
      key={propertyKey}
      schema={{
        ...propertySchema,
        displayName: propertySchema.displayName || propertyKey,
        name: propertyKey,
      }}
      onChange={onChange}
      value={value ?? defaultValue ?? ''}
      error={error}
      previousNodes={previousNodes}
      classNames={classNames}
    />
  );
};

export const AddNewValueButton = ({
  onClick,
}: { onClick: (e: React.MouseEvent<HTMLButtonElement>) => void }) => {
  const { t } = useTranslation('common');

  return (
    <Group>
      <Button
        color='decaNavy.5'
        variant='transparent'
        onClick={onClick}
        leftSection={<IconPlus size={16} />}
        size='md'
        p={0}
      >
        {t('addNewValue')}
      </Button>
    </Group>
  );
};
