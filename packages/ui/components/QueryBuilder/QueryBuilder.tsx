import { Box, Flex, ScrollArea, rem } from '@mantine/core';
import { createStyles } from '@mantine/emotion';
import { QueryBuilderDnD } from '@react-querybuilder/dnd';
import {
  MantineValueEditor,
  MantineValueSelector,
  QueryBuilderMantine,
} from '@react-querybuilder/mantine';
import { IconTrash } from '@tabler/icons-react';
import type { TFunction } from 'i18next';
import { createContext, useCallback, useContext, useEffect, useMemo, useState } from 'react';
import * as ReactDnD from 'react-dnd';
import * as ReactDndHtml5Backend from 'react-dnd-html5-backend';
import { I18nextProvider, useTranslation } from 'react-i18next';
import ReactQueryBuilder, {
  type CombinatorSelectorProps,
  type Field,
  type FieldSelectorProps,
  type FlexibleOptionList,
  type FullCombinator,
  type OperatorSelectorProps,
  Rule,
  RuleGroup,
  type RuleGroupProps,
  type RuleGroupType,
  type RuleProps,
  type ValueEditorProps,
} from 'react-querybuilder';
import { uiI18nInstance } from '../../i18n';
import { DecaButton } from '../DecaButton';
import {
  AddConditionButton,
  AddGroupButton,
  RelativeDate,
  RemoveConditionButton,
  RemoveGroupButton,
} from './components';
import 'react-querybuilder/dist/query-builder.css';
import { DatePickerInput, type DateValue, type DatesRangeValue } from '@mantine/dates';
import dayjs from 'dayjs';
import 'dayjs/locale/en';
import 'dayjs/locale/ja';
import isDate from 'lodash/isDate';
import isString from 'lodash/isString';

// Safe date parsing utilities
const safeDateParse = (value: any): dayjs.Dayjs | null => {
  if (!value) return null;
  const parsed = dayjs(value);
  return parsed.isValid() ? parsed : null;
};

const safeToDate = (value: any): Date | null => {
  if (!value) return null;
  if (isDate(value) && !Number.isNaN(value.getTime())) return value;
  const parsed = safeDateParse(value);
  return parsed ? parsed.toDate() : null;
};

// Validation context for tracking datetime validation errors
interface ValidationContextType {
  addError: (ruleId: string, error: string) => void;
  removeError: (ruleId: string) => void;
  hasErrors: boolean;
}

const ValidationContext = createContext<ValidationContextType | null>(null);
import { CUSTOM_FILTER_OPERATORS } from '../../constants';
import { FieldTypes } from '../DecaTable/types';
import { type Option, Select } from '../Select';
import CustomTagsSelect, { type TagsOption } from './CustomTagsSelect';

const useStyles = createStyles<string, { t: TFunction<'query-builder'> }>((theme, { t }) => {
  const dividerStyles = {
    content: '""',
    position: 'absolute' as const,
    top: 0,
    left: 0,
    right: 0,
    borderTop: `1px solid ${theme.colors.gray[3]}`,
  };

  return {
    queryBuilder: {
      border: `1px solid ${theme.colors.decaLight[2]}`,
      boxShadow: theme.shadows.md,

      '.ruleGroup': {
        flexDirection: 'column-reverse',
        backgroundColor: 'transparent',
        border: 'none',
      },
      '.ruleGroup-body': {
        display: 'grid !important',
        gridTemplateColumns: 'auto auto',
        alignItems: 'start',

        '> .ruleGroup-and .ruleGroup-header .queryBuilder-dragHandle::after': {
          content: `"${t('label.allOfTheFollowingAreTrue')}"`,
        },
        '> .ruleGroup-or .ruleGroup-header .queryBuilder-dragHandle::after': {
          content: `"${t('label.anyOfTheFollowingAreTrue')}"`,
        },

        '>.rule:first-of-type, >.ruleGroup:first-of-type': {
          gridColumnStart: 2,
        },
        '>.ruleGroup': {
          backgroundColor: theme.colors.decaLight[1],
        },
        '.ruleGroup': {
          flexDirection: 'column',
        },
        '.ruleGroup-combinators': {
          width: '6rem',
        },
        '.ruleGroup-combinators :not(:first-of-type), .ruleGroup-combinators input': {
          textTransform: 'lowercase',
        },
        '.ruleGroup-header .queryBuilder-dragHandle:first-of-type': {
          display: 'flex',
          gap: '.5rem',
          width: '100%',
        },
      },
      '.queryBuilder > .ruleGroup > .ruleGroup-body::after': {
        content: `"${t('label.where')}"`,
        display: 'flex',
        alignItems: 'center',
        gridColumnStart: 1,
        gridRowStart: 1,
        paddingRight: rem(8),
        paddingLeft: rem(8),
        width: '6rem',
        height: '38px',
      },

      '.ruleGroup .ruleGroup': {
        backgroundColor: theme.colors.decaLight[1],
        borderRadius: theme.radius.sm,
        padding: rem(8),
      },

      '.relativeDateRuleContent': {
        gridColumnStart: 2,
        marginBottom: rem(10),
      },

      // Enable all combinators to ensure they have default styling
      '.ruleGroup-combinators .mantine-Select-input': {
        border: '1px solid #ced4da',
        backgroundColor: '#ffffff',
        boxShadow: 'none',
        cursor: 'pointer',
        color: theme.black,
        padding: theme.spacing.xs,
        pointerEvents: 'auto',
      },
      '.ruleGroup-combinators .mantine-Select-rightSection': {
        display: 'flex',
      },

      // Then disable specific ones (second+ in each group)
      '.betweenRules ~ .betweenRules .ruleGroup-combinators .mantine-Select-input': {
        border: 'none',
        backgroundColor: 'transparent',
        boxShadow: 'none',
        cursor: 'default',
        color: theme.colors.dark[7],
        padding: `0 ${rem(8)}`,
        pointerEvents: 'none',
      },
      '.betweenRules ~ .betweenRules .ruleGroup-combinators .mantine-Select-section': {
        display: 'none',
      },
    },
    queryBuilderEmpty: {
      '.queryBuilder > .ruleGroup > .ruleGroup-body::after': {
        content: 'none',
      },
    },
    clearAll: {
      marginTop: '-2.37rem',
    },
    fieldSelectorTag: {
      '> button[data-value="tags"]': {
        position: 'relative',
        '.mantine-Menu-itemLabel': {
          '&::before': dividerStyles,
        },
      },
    },
    operatorOptions: {
      '& .mantine-Select-option:last-of-type': {
        position: 'relative',
        '&::before': dividerStyles,
      },
    },
  };
});

interface QueryBuilderProps {
  fields: Field[];
  initialQuery?: RuleGroupType;
  onQueryChange: (query: RuleGroupType) => void;
  onValidationChange?: (hasErrors: boolean) => void;
  className?: string;
  disabled?: boolean;
}

const CustomValueSelector = (selectorProps: any) => (
  <MantineValueSelector
    {...selectorProps}
    checkIconPosition='right'
    comboboxProps={{ withinPortal: true }}
  />
);

const CustomValueEditor = (props: ValueEditorProps & { disabled?: boolean }) => {
  const { i18n, t } = useTranslation('query-builder');
  const validationContext = useContext(ValidationContext);

  // Create unique rule identifier for validation tracking
  const ruleId = `${props.path.join('.')}-${props.fieldData.name || 'unknown'}`;

  // Helper function to set validation error for empty required fields
  const setValidationError = useCallback(
    (hasError: boolean) => {
      if (validationContext) {
        if (hasError) {
          validationContext.addError(ruleId, 'Field is required');
        } else {
          validationContext.removeError(ruleId);
        }
      }
    },
    [validationContext, ruleId]
  );

  // Clear error when component unmounts
  useEffect(() => {
    return () => {
      validationContext?.removeError(ruleId);
    };
  }, [validationContext, ruleId]);

  // Safe initialization of date range values - without side effects
  const getInitialDateRangeValue = (): [Date | null, Date | null] => {
    if (!props.value) return [null, null];

    if (Array.isArray(props.value) && props.value.length === 2) {
      const date1 = safeToDate(props.value[0]);
      const date2 = safeToDate(props.value[1]);
      if (!date1 || !date2) {
        return [null, null];
      }
      return [date1, date2];
    }

    // Handle comma-separated string format
    if (isString(props.value) && props.value.includes(',')) {
      const parts = props.value.split(',');
      const date1 = safeToDate(parts[0]);
      const date2 = safeToDate(parts[1]);
      if (!date1 || !date2) {
        return [null, null];
      }
      return [date1, date2];
    }

    return [null, null];
  };

  const [value, setValue] = useState<[Date | null, Date | null]>(getInitialDateRangeValue());
  const isDateRange = props.operator.includes('between') || props.operator.includes('notBetween');

  // Basic validation for required datetime fields (only check if empty, not format)
  useEffect(() => {
    if (props.fieldData.inputType !== 'datetime-local') {
      setValidationError(false);
      return;
    }

    // For null/notNull/relativeDate operators, no validation is needed
    if (
      props.operator === 'null' ||
      props.operator === 'notNull' ||
      props.operator === 'relativeDate'
    ) {
      setValidationError(false);
      return;
    }

    // Check if the field is empty (required field validation)
    const isEmpty = !props.value || props.value === '';
    setValidationError(isEmpty);
  }, [props.value, props.fieldData.inputType, props.operator, setValidationError]);
  if (props.fieldData.inputType === 'datetime-local') {
    if (props.rule?.operator.includes(CUSTOM_FILTER_OPERATORS.relativeDate)) {
      return <></>;
    }
    // For null/notNull operators, show disabled input
    const isNullOperator = props.operator === 'null' || props.operator === 'notNull';
    if (isDateRange) {
      return (
        <Box>
          <DatePickerInput
            miw={rem(200)}
            popoverProps={{ withinPortal: true }}
            type='range'
            value={value}
            placeholder={t('selectValue')}
            onChange={(values: DatesRangeValue) => {
              setValue(values);

              if (!values || !values[0] || !values[1]) {
                props.handleOnChange(null);
                return;
              }

              const date1 = safeDateParse(values[0]);
              const date2 = safeDateParse(values[1]);

              if (!date1 || !date2) {
                props.handleOnChange(null);
                return;
              }

              props.handleOnChange(
                `${date1.format('YYYY-MM-DDTHH:mmZ')},${date2.format('YYYY-MM-DDTHH:mmZ')}`
              );
            }}
            locale={i18n.language.includes('ja') ? 'ja' : 'en'}
            monthLabelFormat='YYYY/MM'
            disabled={props.disabled || isNullOperator}
          />
        </Box>
      );
    }
    // Get initial single date value - without side effects
    const getInitialSingleDateValue = (): Date | null => {
      if (!props.value || (isString(props.value) && props.value.split(',').length > 1)) {
        return null;
      }

      const parsed = safeToDate(props.value);
      if (!parsed) {
        return null;
      }

      return parsed;
    };

    return (
      <Box>
        <DatePickerInput
          miw={rem(200)}
          popoverProps={{ withinPortal: true }}
          value={getInitialSingleDateValue()}
          placeholder={t('selectValue')}
          onChange={(value: DateValue) => {
            if (!value || !isDate(value)) {
              props.handleOnChange(null);
              return;
            }

            const parsed = safeDateParse(value);
            if (!parsed) {
              props.handleOnChange(null);
              return;
            }

            props.handleOnChange(parsed.format('YYYY-MM-DDTHH:mmZ'));
          }}
          locale={i18n.language.includes('ja') ? 'ja' : 'en'}
          monthLabelFormat='YYYY/MM'
          disabled={props.disabled || isNullOperator}
        />
      </Box>
    );
  }
  if (props.fieldData.inputType === FieldTypes.TAG) {
    return (
      <CustomTagsSelect
        value={
          !props.value ? [] : typeof props.value === 'string' ? props.value.split(',') : props.value
        }
        values={props.fieldData.values as TagsOption[]}
        onChange={props.handleOnChange}
      />
    );
  }

  if (props.fieldData.inputType === FieldTypes.SINGLE_SELECT) {
    return (
      <Select
        options={[{ value: 'test', label: 'Test' }]}
        search
        placeholder={t('selectValue')}
        searchPlaceholder={t('search')}
        onChange={props.handleOnChange}
        width={rem(176)}
      />
    );
  }

  return (
    <MantineValueEditor
      {...props}
      disabled={props.disabled}
      selectorComponent={CustomValueSelector}
    />
  );
};

const CustomFieldSelector = (props: FieldSelectorProps) => {
  const { t } = useTranslation('query-builder');
  const { classes, cx } = useStyles({ t });
  const isTagFilter = props.options.some((option) => option.inputType === FieldTypes.TAG);
  return (
    <Select
      defaultValue={props.value}
      options={props.options as Option[]}
      search
      placeholder={t('selectValue')}
      searchPlaceholder={t('search')}
      onChange={props.handleOnChange}
      width={rem(176)}
      optionsClassName={cx({ [classes.fieldSelectorTag]: isTagFilter })}
    />
  );
};

const CustomOperatorSelector = (props: OperatorSelectorProps) => {
  const { t } = useTranslation('query-builder');
  const { classes } = useStyles({ t });

  if (props.fieldData.inputType === 'datetime-local') {
    const options = [
      ...props.options.map((opt: any) => ({
        value: opt.name,
        label: opt.label,
      })),
      {
        value: 'relativeDate',
        label: t('operators.relativeDate'),
      },
    ];
    return (
      <MantineValueSelector
        className='operator-selector'
        {...props}
        options={options as any}
        checkIconPosition='right'
        comboboxProps={{ withinPortal: true, classNames: { options: classes.operatorOptions } }}
        placeholder={t('selectValue')}
        width={rem(176)}
      />
    );
  }
  return (
    <Select
      defaultValue={props.value}
      options={props.options as Option[]}
      search
      placeholder={t('selectValue')}
      searchPlaceholder={t('search')}
      onChange={props.handleOnChange}
      width={rem(176)}
    />
  );
};

const CustomCombinatorSelector = (props: CombinatorSelectorProps) => {
  return (
    <MantineValueSelector
      className='combinator-selector'
      {...props}
      checkIconPosition='right'
      comboboxProps={{ withinPortal: true }}
    />
  );
};

const CustomRule = (props: RuleProps) => {
  if (props.rule?.operator?.includes(CUSTOM_FILTER_OPERATORS.relativeDate)) {
    return <RelativeDate {...props} />;
  }
  return <Rule {...props} />;
};

const CustomRuleGroup = (props: RuleGroupProps) => {
  const { schema, ...rest } = props;
  const combinator = props.ruleGroup.combinator;

  return (
    <RuleGroup
      {...rest}
      schema={{
        ...schema,
        classNames: {
          ...schema.classNames,
          ruleGroup: `ruleGroup-${combinator}`,
        },
      }}
    />
  );
};

const QueryBuilderInternal = (props: QueryBuilderProps) => {
  const {
    fields,
    initialQuery,
    onQueryChange,
    onValidationChange,
    className,
    disabled = false,
  } = props;
  const { t } = useTranslation('query-builder');
  const { classes, cx } = useStyles({ t });
  const [query, setQuery] = useState<RuleGroupType>({
    combinator: 'and',
    rules: [],
    ...initialQuery,
  });

  const [validationErrors, setValidationErrors] = useState<Map<string, string>>(new Map());

  // Validation management functions
  const addValidationError = useCallback((ruleId: string, error: string) => {
    setValidationErrors((prev) => {
      // Only update if the error is actually different
      if (prev.get(ruleId) === error) return prev;
      const newErrors = new Map(prev);
      newErrors.set(ruleId, error);
      return newErrors;
    });
  }, []);

  const removeValidationError = useCallback((ruleId: string) => {
    setValidationErrors((prev) => {
      // Only update if the error actually exists
      if (!prev.has(ruleId)) return prev;
      const newErrors = new Map(prev);
      newErrors.delete(ruleId);
      return newErrors;
    });
  }, []);

  const hasValidationErrors = useMemo(() => validationErrors.size > 0, [validationErrors.size]);

  // Notify parent of validation state changes - debounced to prevent excessive calls
  useEffect(() => {
    const timeoutId = setTimeout(() => {
      onValidationChange?.(hasValidationErrors);
    }, 50); // Small debounce to batch rapid changes

    return () => clearTimeout(timeoutId);
  }, [hasValidationErrors, onValidationChange]);

  const validationContextValue = useMemo<ValidationContextType>(
    () => ({
      addError: addValidationError,
      removeError: removeValidationError,
      hasErrors: hasValidationErrors,
    }),
    [addValidationError, removeValidationError, hasValidationErrors]
  );

  const combinators = useMemo<FlexibleOptionList<FullCombinator>>(
    () => [
      { name: 'and', label: t('combinators.and') },
      { name: 'or', label: t('combinators.or') },
    ],
    [t]
  );

  const fieldsWithPlaceholder = useMemo(() => {
    return fields.map((field) => ({ ...field, placeholder: t('placeholder.default') }));
  }, [fields, t]);

  const _onQueryChange = useCallback(
    (query: RuleGroupType) => {
      setQuery(query);
      onQueryChange(query);
    },
    [onQueryChange]
  );

  const onClearAllClick = useCallback(() => {
    const resetQuery: RuleGroupType = { combinator: 'and', rules: [] };
    setQuery(resetQuery);
    onQueryChange(resetQuery);
  }, [onQueryChange]);

  return (
    <Box
      className={cx(classes.queryBuilder, className, {
        [classes.queryBuilderEmpty]: query.rules.length === 0,
      })}
      p={rem(8)}
    >
      <Box>
        <ValidationContext.Provider value={validationContextValue}>
          <QueryBuilderMantine>
            <QueryBuilderDnD dnd={{ ...ReactDnD, ...ReactDndHtml5Backend }}>
              <ReactQueryBuilder
                disabled={disabled}
                combinators={combinators}
                showCombinatorsBetweenRules
                fields={fieldsWithPlaceholder}
                query={query}
                onQueryChange={_onQueryChange}
                controlElements={{
                  addRuleAction: AddConditionButton,
                  addGroupAction: AddGroupButton,
                  removeRuleAction: RemoveConditionButton,
                  removeGroupAction: RemoveGroupButton,
                  valueEditor: CustomValueEditor,
                  fieldSelector: CustomFieldSelector,
                  operatorSelector: CustomOperatorSelector,
                  combinatorSelector: CustomCombinatorSelector,
                  rule: CustomRule,
                  ruleGroup: CustomRuleGroup,
                }}
              />
            </QueryBuilderDnD>
          </QueryBuilderMantine>
        </ValidationContext.Provider>
      </Box>
      <Box>
        <Flex justify='end' px={rem(8)}>
          <DecaButton
            className={classes.clearAll}
            variant='negative_text'
            size='sm'
            title={t('button.clearAll')}
            leftSection={<IconTrash size={16} />}
            onClick={onClearAllClick}
            disabled={disabled}
          >
            {t('button.clearAll')}
          </DecaButton>
        </Flex>
      </Box>
    </Box>
  );
};

const QueryBuilder = (props: QueryBuilderProps) => (
  <I18nextProvider i18n={uiI18nInstance}>
    <ScrollArea.Autosize mah={rem(500)}>
      <QueryBuilderInternal {...props} />
    </ScrollArea.Autosize>
  </I18nextProvider>
);

export default QueryBuilder;
