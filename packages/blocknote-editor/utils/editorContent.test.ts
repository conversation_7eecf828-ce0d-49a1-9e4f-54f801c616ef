import type { BlockNoteEditor } from '@blocknote/core';
import { beforeEach, describe, expect, it, vi } from 'vitest';
import {
  filterVariableNodesFromBlocks,
  filterVariableNodesFromContent,
  loadEditorContent,
  processBlocksForMarkdown,
  serializeToHTML,
  serializeToMarkdown,
} from './editorContent';

// Mock all the utility functions
vi.mock('./content', () => ({
  convertEmptyParagraphToSpaceContent: vi.fn(),
  convertWhiteSpaceToNbspInBlockNote: vi.fn(),
  normalizeBlockNoteHTML: vi.fn(),
  parseSpaceContentToEmptyParagraph: vi.fn(),
}));

vi.mock('./string', () => ({
  convertBreakLineToHTML: vi.fn(),
  finalizeMarkdownFromBlockNote: vi.fn(),
  normalizeMarkdownToBlockNote: vi.fn(),
  replaceWhiteSpaceToHTMLNbsp: vi.fn(),
  convertHTMLToText: vi.fn(),
}));

vi.mock('./table', () => ({
  processTableDataAttributes: vi.fn(),
}));

vi.mock('./variableParser', () => ({
  processMarkdownVariables: vi.fn(),
}));

vi.mock('./cssStyles', () => ({
  prependCSSStyles: vi.fn(),
  removeCSSStyles: vi.fn(),
}));

// Import the mocked functions for assertions
import {
  convertEmptyParagraphToSpaceContent,
  convertWhiteSpaceToNbspInBlockNote,
  normalizeBlockNoteHTML,
  parseSpaceContentToEmptyParagraph,
} from './content';
import { prependCSSStyles, removeCSSStyles } from './cssStyles';
import {
  convertBreakLineToHTML,
  convertHTMLToText,
  finalizeMarkdownFromBlockNote,
  normalizeMarkdownToBlockNote,
  replaceWhiteSpaceToHTMLNbsp,
} from './string';
import { processTableDataAttributes } from './table';
import { processMarkdownVariables } from './variableParser';

// Test constants
const MOCK_HTML_CONTENT = '<p>Test HTML content</p>';
const MOCK_MARKDOWN_CONTENT = '# Test Markdown\n\nThis is a test.';
const MOCK_PROCESSED_HTML = '<p>Processed HTML</p>';
const MOCK_NORMALIZED_HTML = '<p>Normalized HTML</p>';
const MOCK_FINAL_HTML = '<p>Final HTML with table styles</p>';
const MOCK_PLAIN_TEXT = 'Plain text content';
const MOCK_MARKDOWN_OUTPUT = '# Processed Markdown';

const MOCK_BLOCKS = [
  {
    type: 'paragraph',
    content: [
      {
        type: 'text',
        text: 'Test content',
        styles: {},
      },
    ],
  },
];

const MOCK_PROCESSED_BLOCKS = [
  {
    type: 'paragraph',
    content: [
      {
        type: 'text',
        text: 'Processed content',
        styles: {},
      },
    ],
  },
];

const MOCK_EDITOR_DOCUMENT = [
  {
    type: 'paragraph',
    content: [
      {
        type: 'text',
        text: 'Editor document content',
        styles: {},
      },
    ],
  },
];

// Create mock BlockNote editor
const createMockEditor = (): BlockNoteEditor =>
  ({
    tryParseHTMLToBlocks: vi.fn(),
    tryParseMarkdownToBlocks: vi.fn(),
    blocksToFullHTML: vi.fn(),
    blocksToMarkdownLossy: vi.fn(),
    document: MOCK_EDITOR_DOCUMENT,
  }) as unknown as BlockNoteEditor;

describe('editorContent', () => {
  let mockEditor: BlockNoteEditor;

  beforeEach(() => {
    vi.clearAllMocks();
    mockEditor = createMockEditor();
  });

  describe('loadEditorContent', () => {
    describe('HTML content processing', () => {
      beforeEach(() => {
        vi.mocked(removeCSSStyles).mockImplementation((html) => html); // Pass through by default
        vi.mocked(replaceWhiteSpaceToHTMLNbsp).mockReturnValue(MOCK_PROCESSED_HTML);
        vi.mocked(convertBreakLineToHTML).mockReturnValue(MOCK_HTML_CONTENT);
        vi.mocked(mockEditor.tryParseHTMLToBlocks).mockResolvedValue(MOCK_BLOCKS);
        vi.mocked(processMarkdownVariables).mockReturnValue(MOCK_PROCESSED_BLOCKS);
      });

      it('should process HTML content correctly', async () => {
        const result = await loadEditorContent(mockEditor, MOCK_HTML_CONTENT, false, true);

        expect(removeCSSStyles).toHaveBeenCalledWith(MOCK_HTML_CONTENT);
        expect(convertBreakLineToHTML).toHaveBeenCalledWith(MOCK_HTML_CONTENT);
        expect(replaceWhiteSpaceToHTMLNbsp).toHaveBeenCalledWith(MOCK_HTML_CONTENT);
        expect(mockEditor.tryParseHTMLToBlocks).toHaveBeenCalledWith(MOCK_PROCESSED_HTML);
        expect(processMarkdownVariables).toHaveBeenCalledWith(MOCK_BLOCKS);
        expect(result).toEqual(MOCK_PROCESSED_BLOCKS);
      });

      it('should handle empty HTML content', async () => {
        const emptyContent = '';
        vi.mocked(convertBreakLineToHTML).mockReturnValue(emptyContent);
        vi.mocked(replaceWhiteSpaceToHTMLNbsp).mockReturnValue(emptyContent);

        const result = await loadEditorContent(mockEditor, emptyContent, false, true);

        expect(removeCSSStyles).toHaveBeenCalledWith(emptyContent);
        expect(convertBreakLineToHTML).toHaveBeenCalledWith(emptyContent);
        expect(replaceWhiteSpaceToHTMLNbsp).toHaveBeenCalledWith(emptyContent);
        expect(mockEditor.tryParseHTMLToBlocks).toHaveBeenCalledWith(emptyContent);
        expect(processMarkdownVariables).toHaveBeenCalledWith(MOCK_BLOCKS);
        expect(result).toEqual(MOCK_PROCESSED_BLOCKS);
      });

      it('should process HTML content with variables', async () => {
        const htmlWithVariables = '<p>Hello {{name}}, welcome!</p>';
        vi.mocked(removeCSSStyles).mockReturnValue(htmlWithVariables);
        vi.mocked(convertBreakLineToHTML).mockReturnValue(htmlWithVariables);
        vi.mocked(replaceWhiteSpaceToHTMLNbsp).mockReturnValue(htmlWithVariables);

        await loadEditorContent(mockEditor, htmlWithVariables, false, true);

        expect(removeCSSStyles).toHaveBeenCalledWith(htmlWithVariables);
        expect(processMarkdownVariables).toHaveBeenCalledWith(MOCK_BLOCKS);
      });
    });

    describe('Markdown content processing', () => {
      beforeEach(() => {
        vi.mocked(normalizeMarkdownToBlockNote).mockReturnValue(MOCK_PROCESSED_HTML);
        vi.mocked(mockEditor.tryParseMarkdownToBlocks).mockResolvedValue(MOCK_BLOCKS);
        vi.mocked(processMarkdownVariables).mockReturnValue(MOCK_PROCESSED_BLOCKS);
      });

      it('should process Markdown content correctly', async () => {
        const result = await loadEditorContent(mockEditor, MOCK_MARKDOWN_CONTENT, true, true);

        expect(normalizeMarkdownToBlockNote).toHaveBeenCalledWith(MOCK_MARKDOWN_CONTENT);
        expect(mockEditor.tryParseMarkdownToBlocks).toHaveBeenCalledWith(MOCK_PROCESSED_HTML);
        expect(processMarkdownVariables).toHaveBeenCalledWith(MOCK_BLOCKS);
        expect(result).toEqual(MOCK_PROCESSED_BLOCKS);
      });

      it('should handle empty Markdown content', async () => {
        const emptyMarkdown = '';
        vi.mocked(normalizeMarkdownToBlockNote).mockReturnValue(emptyMarkdown);

        const result = await loadEditorContent(mockEditor, emptyMarkdown, true, true);

        expect(normalizeMarkdownToBlockNote).toHaveBeenCalledWith(emptyMarkdown);
        expect(mockEditor.tryParseMarkdownToBlocks).toHaveBeenCalledWith(emptyMarkdown);
        expect(processMarkdownVariables).toHaveBeenCalledWith(MOCK_BLOCKS);
        expect(result).toEqual(MOCK_PROCESSED_BLOCKS);
      });

      it('should process Markdown content with variables', async () => {
        const markdownWithVariables = '# Hello {{name}}\n\nWelcome to {{site}}!';
        vi.mocked(normalizeMarkdownToBlockNote).mockReturnValue(markdownWithVariables);

        await loadEditorContent(mockEditor, markdownWithVariables, true, true);

        expect(processMarkdownVariables).toHaveBeenCalledWith(MOCK_BLOCKS);
      });

      it('should handle complex Markdown with lists and formatting', async () => {
        const complexMarkdown = `
# Title {{title}}

- Item 1 with {{variable1}}
- Item 2 with **bold** text
- Item 3 with *italic* {{variable2}}

> Quote with {{quote}}
        `;
        vi.mocked(normalizeMarkdownToBlockNote).mockReturnValue(complexMarkdown);

        await loadEditorContent(mockEditor, complexMarkdown, true, true);

        expect(normalizeMarkdownToBlockNote).toHaveBeenCalledWith(complexMarkdown);
        expect(processMarkdownVariables).toHaveBeenCalledWith(MOCK_BLOCKS);
      });
    });

    describe('Error handling', () => {
      it('should handle editor parsing errors for HTML', async () => {
        const error = new Error('Parse error');
        vi.mocked(mockEditor.tryParseHTMLToBlocks).mockRejectedValue(error);

        await expect(loadEditorContent(mockEditor, MOCK_HTML_CONTENT, false, true)).rejects.toThrow(
          'Parse error'
        );
      });

      it('should handle editor parsing errors for Markdown', async () => {
        const error = new Error('Markdown parse error');
        vi.mocked(mockEditor.tryParseMarkdownToBlocks).mockRejectedValue(error);

        await expect(
          loadEditorContent(mockEditor, MOCK_MARKDOWN_CONTENT, true, true)
        ).rejects.toThrow('Markdown parse error');
      });

      it('should handle variable processing errors', async () => {
        vi.mocked(processMarkdownVariables).mockImplementation(() => {
          throw new Error('Variable processing error');
        });

        await expect(loadEditorContent(mockEditor, MOCK_HTML_CONTENT, false, true)).rejects.toThrow(
          'Variable processing error'
        );
      });
    });

    describe('CSS styles removal', () => {
      beforeEach(() => {
        vi.mocked(removeCSSStyles).mockImplementation((html) =>
          html.replace(/<style[^>]*>[\s\S]*?<\/style>\s*/gi, '')
        );
        vi.mocked(convertBreakLineToHTML).mockImplementation((html) => html);
        vi.mocked(replaceWhiteSpaceToHTMLNbsp).mockImplementation((html) => html);
        vi.mocked(mockEditor.tryParseHTMLToBlocks).mockResolvedValue(MOCK_BLOCKS);
        vi.mocked(processMarkdownVariables).mockReturnValue(MOCK_PROCESSED_BLOCKS);
      });

      it('should remove CSS styles from HTML content before loading', async () => {
        const htmlWithStyles = `<style type="text/css">.test { color: red; }</style><p>Test content</p>`;
        const cleanHTML = '<p>Test content</p>';

        vi.mocked(removeCSSStyles).mockReturnValue(cleanHTML);

        const result = await loadEditorContent(mockEditor, htmlWithStyles, false, true);

        expect(removeCSSStyles).toHaveBeenCalledWith(htmlWithStyles);
        expect(convertBreakLineToHTML).toHaveBeenCalledWith(cleanHTML);
        expect(replaceWhiteSpaceToHTMLNbsp).toHaveBeenCalledWith(cleanHTML);
        expect(mockEditor.tryParseHTMLToBlocks).toHaveBeenCalledWith(cleanHTML);
        expect(result).toEqual(MOCK_PROCESSED_BLOCKS);
      });

      it('should not affect markdown content processing', async () => {
        const markdownContent = '# Title\n\nParagraph';

        vi.mocked(normalizeMarkdownToBlockNote).mockReturnValue(markdownContent);
        vi.mocked(mockEditor.tryParseMarkdownToBlocks).mockResolvedValue(MOCK_BLOCKS);

        const result = await loadEditorContent(mockEditor, markdownContent, true, true);

        expect(removeCSSStyles).not.toHaveBeenCalled();
        expect(normalizeMarkdownToBlockNote).toHaveBeenCalledWith(markdownContent);
        expect(mockEditor.tryParseMarkdownToBlocks).toHaveBeenCalledWith(markdownContent);
        expect(result).toEqual(MOCK_PROCESSED_BLOCKS);
      });

      it('should handle HTML content without styles correctly', async () => {
        const plainHTML = '<p>Plain content</p>';

        vi.mocked(removeCSSStyles).mockReturnValue(plainHTML);

        const result = await loadEditorContent(mockEditor, plainHTML, false, true);

        expect(removeCSSStyles).toHaveBeenCalledWith(plainHTML);
        expect(convertBreakLineToHTML).toHaveBeenCalledWith(plainHTML);
        expect(result).toEqual(MOCK_PROCESSED_BLOCKS);
      });

      it('should handle multiple style tags in HTML content', async () => {
        const htmlWithMultipleStyles = `
          <style>.test1 { color: red; }</style>
          <p>Content</p>
          <style>.test2 { color: blue; }</style>
        `;
        const cleanHTML = '<p>Content</p>';

        vi.mocked(removeCSSStyles).mockReturnValue(cleanHTML);

        const result = await loadEditorContent(mockEditor, htmlWithMultipleStyles, false, true);

        expect(removeCSSStyles).toHaveBeenCalledWith(htmlWithMultipleStyles);
        expect(result).toEqual(MOCK_PROCESSED_BLOCKS);
      });
    });
  });

  describe('processBlocksForMarkdown', () => {
    beforeEach(() => {
      vi.mocked(parseSpaceContentToEmptyParagraph).mockReturnValue(MOCK_PROCESSED_BLOCKS);
    });

    it('should process blocks for markdown correctly', () => {
      const result = processBlocksForMarkdown(MOCK_BLOCKS);

      expect(parseSpaceContentToEmptyParagraph).toHaveBeenCalledWith(MOCK_BLOCKS);
      expect(result).toEqual(MOCK_PROCESSED_BLOCKS);
    });

    it('should handle empty blocks array', () => {
      const emptyBlocks: any[] = [];
      vi.mocked(parseSpaceContentToEmptyParagraph).mockReturnValue([]);

      const result = processBlocksForMarkdown(emptyBlocks);

      expect(parseSpaceContentToEmptyParagraph).toHaveBeenCalledWith(emptyBlocks);
      expect(result).toEqual([]);
    });

    it('should handle blocks with various content types', () => {
      const complexBlocks = [
        {
          type: 'paragraph',
          content: [{ type: 'text', text: 'Text content', styles: {} }],
        },
        {
          type: 'heading',
          content: [{ type: 'text', text: 'Heading', styles: {} }],
        },
        {
          type: 'bulletListItem',
          content: [{ type: 'text', text: 'List item', styles: {} }],
        },
      ];

      processBlocksForMarkdown(complexBlocks);

      expect(parseSpaceContentToEmptyParagraph).toHaveBeenCalledWith(complexBlocks);
    });

    it('should handle blocks with empty content', () => {
      const blocksWithEmptyContent = [
        {
          type: 'paragraph',
          content: [],
        },
        {
          type: 'paragraph',
          content: [{ type: 'text', text: '', styles: {} }],
        },
      ];

      processBlocksForMarkdown(blocksWithEmptyContent);

      expect(parseSpaceContentToEmptyParagraph).toHaveBeenCalledWith(blocksWithEmptyContent);
    });
  });

  describe('serializeToMarkdown', () => {
    beforeEach(() => {
      vi.mocked(convertEmptyParagraphToSpaceContent).mockReturnValue(MOCK_PROCESSED_BLOCKS);
      vi.mocked(mockEditor.blocksToMarkdownLossy).mockResolvedValue(MOCK_MARKDOWN_CONTENT);
      vi.mocked(finalizeMarkdownFromBlockNote).mockReturnValue(MOCK_MARKDOWN_OUTPUT);
    });

    it('should serialize editor content to markdown correctly', async () => {
      const result = await serializeToMarkdown(mockEditor);

      expect(convertEmptyParagraphToSpaceContent).toHaveBeenCalledWith(MOCK_EDITOR_DOCUMENT);
      expect(mockEditor.blocksToMarkdownLossy).toHaveBeenCalledWith(MOCK_PROCESSED_BLOCKS);
      expect(finalizeMarkdownFromBlockNote).toHaveBeenCalledWith(MOCK_MARKDOWN_CONTENT);
      expect(result).toBe(MOCK_MARKDOWN_OUTPUT);
    });

    it('should handle empty editor document', async () => {
      const emptyDocument: any[] = [];
      mockEditor.document = emptyDocument;
      vi.mocked(convertEmptyParagraphToSpaceContent).mockReturnValue([]);
      vi.mocked(mockEditor.blocksToMarkdownLossy).mockResolvedValue('');
      vi.mocked(finalizeMarkdownFromBlockNote).mockReturnValue('');

      const result = await serializeToMarkdown(mockEditor);

      expect(convertEmptyParagraphToSpaceContent).toHaveBeenCalledWith(emptyDocument);
      expect(result).toBe('');
    });

    it('should handle complex editor content with various block types', async () => {
      const complexDocument = [
        { type: 'heading', content: [{ type: 'text', text: 'Title', styles: {} }] },
        { type: 'paragraph', content: [{ type: 'text', text: 'Paragraph', styles: {} }] },
        { type: 'bulletListItem', content: [{ type: 'text', text: 'List item', styles: {} }] },
        { type: 'table', content: [] },
      ];
      mockEditor.document = complexDocument;

      await serializeToMarkdown(mockEditor);

      expect(convertEmptyParagraphToSpaceContent).toHaveBeenCalledWith(complexDocument);
    });

    it('should handle markdown serialization errors', async () => {
      const error = new Error('Markdown serialization error');
      vi.mocked(mockEditor.blocksToMarkdownLossy).mockRejectedValue(error);

      await expect(serializeToMarkdown(mockEditor)).rejects.toThrow('Markdown serialization error');
    });

    it('should handle finalization errors', async () => {
      vi.mocked(finalizeMarkdownFromBlockNote).mockImplementation(() => {
        throw new Error('Finalization error');
      });

      await expect(serializeToMarkdown(mockEditor)).rejects.toThrow('Finalization error');
    });
  });

  describe('serializeToHTML', () => {
    beforeEach(() => {
      vi.mocked(convertWhiteSpaceToNbspInBlockNote).mockReturnValue(MOCK_PROCESSED_BLOCKS);
      vi.mocked(mockEditor.blocksToFullHTML).mockResolvedValue(MOCK_HTML_CONTENT);
      vi.mocked(normalizeBlockNoteHTML).mockReturnValue(MOCK_NORMALIZED_HTML);
      vi.mocked(processTableDataAttributes).mockReturnValue(MOCK_FINAL_HTML);
      vi.mocked(prependCSSStyles).mockImplementation((html) => html); // Pass through by default
      vi.mocked(convertHTMLToText).mockReturnValue(MOCK_PLAIN_TEXT);
    });

    it('should serialize editor content to HTML correctly with default CSS styles', async () => {
      const result = await serializeToHTML(mockEditor);

      expect(convertWhiteSpaceToNbspInBlockNote).toHaveBeenCalledWith(MOCK_EDITOR_DOCUMENT);
      expect(mockEditor.blocksToFullHTML).toHaveBeenCalledWith(MOCK_PROCESSED_BLOCKS);
      expect(normalizeBlockNoteHTML).toHaveBeenCalledWith(MOCK_HTML_CONTENT);
      expect(processTableDataAttributes).toHaveBeenCalledWith(MOCK_NORMALIZED_HTML);
      expect(prependCSSStyles).toHaveBeenCalledWith(MOCK_FINAL_HTML, undefined);
      expect(convertHTMLToText).toHaveBeenCalledWith(MOCK_HTML_CONTENT);

      expect(result).toEqual({
        html: MOCK_FINAL_HTML,
        plainText: MOCK_PLAIN_TEXT,
      });
    });

    it('should handle empty editor document', async () => {
      const emptyDocument: any[] = [];
      mockEditor.document = emptyDocument;
      vi.mocked(convertWhiteSpaceToNbspInBlockNote).mockReturnValue([]);
      vi.mocked(mockEditor.blocksToFullHTML).mockResolvedValue('');
      vi.mocked(normalizeBlockNoteHTML).mockReturnValue('');
      vi.mocked(processTableDataAttributes).mockReturnValue('');
      vi.mocked(convertHTMLToText).mockReturnValue('');

      const result = await serializeToHTML(mockEditor);

      expect(result).toEqual({
        html: '',
        plainText: '',
      });
    });

    it('should process table data attributes correctly', async () => {
      const htmlWithTables = '<table><tr><td data-text-color="red">Cell</td></tr></table>';
      const processedTableHTML =
        '<table><tr><td data-text-color="red" style="color: #e03e3e;">Cell</td></tr></table>';

      vi.mocked(mockEditor.blocksToFullHTML).mockResolvedValue(htmlWithTables);
      vi.mocked(normalizeBlockNoteHTML).mockReturnValue(htmlWithTables);
      vi.mocked(processTableDataAttributes).mockReturnValue(processedTableHTML);

      const result = await serializeToHTML(mockEditor);

      expect(processTableDataAttributes).toHaveBeenCalledWith(htmlWithTables);
      expect(result.html).toBe(processedTableHTML);
    });

    it('should handle complex HTML content with various elements', async () => {
      const complexHTML = `
        <h1>Title</h1>
        <p>Paragraph with <strong>bold</strong> and <em>italic</em> text.</p>
        <ul>
          <li>List item 1</li>
          <li>List item 2</li>
        </ul>
        <table>
          <tr>
            <th data-background-color="blue">Header</th>
            <td data-text-color="red">Cell</td>
          </tr>
        </table>
      `;

      vi.mocked(mockEditor.blocksToFullHTML).mockResolvedValue(complexHTML);
      vi.mocked(normalizeBlockNoteHTML).mockReturnValue(complexHTML);

      await serializeToHTML(mockEditor);

      expect(processTableDataAttributes).toHaveBeenCalledWith(complexHTML);
    });

    it('should handle HTML serialization errors', async () => {
      const error = new Error('HTML serialization error');
      vi.mocked(mockEditor.blocksToFullHTML).mockRejectedValue(error);

      await expect(serializeToHTML(mockEditor)).rejects.toThrow('HTML serialization error');
    });

    it('should handle normalization errors', async () => {
      vi.mocked(normalizeBlockNoteHTML).mockImplementation(() => {
        throw new Error('Normalization error');
      });

      await expect(serializeToHTML(mockEditor)).rejects.toThrow('Normalization error');
    });

    it('should handle table processing errors', async () => {
      vi.mocked(processTableDataAttributes).mockImplementation(() => {
        throw new Error('Table processing error');
      });

      await expect(serializeToHTML(mockEditor)).rejects.toThrow('Table processing error');
    });

    it('should handle text conversion errors', async () => {
      vi.mocked(convertHTMLToText).mockImplementation(() => {
        throw new Error('Text conversion error');
      });

      await expect(serializeToHTML(mockEditor)).rejects.toThrow('Text conversion error');
    });

    it('should preserve HTML structure while adding table styles', async () => {
      const originalHTML =
        '<div><table><tr><td data-text-color="blue">Content</td></tr></table></div>';
      const expectedHTML =
        '<div><table><tr><td data-text-color="blue" style="color: #0b6e99;">Content</td></tr></table></div>';

      vi.mocked(mockEditor.blocksToFullHTML).mockResolvedValue(originalHTML);
      vi.mocked(normalizeBlockNoteHTML).mockReturnValue(originalHTML);
      vi.mocked(processTableDataAttributes).mockReturnValue(expectedHTML);

      const result = await serializeToHTML(mockEditor);

      expect(result.html).toBe(expectedHTML);
    });

    it('should handle HTML with no table data attributes', async () => {
      const htmlWithoutTableAttrs =
        '<p>Simple paragraph</p><table><tr><td>Plain cell</td></tr></table>';

      vi.mocked(mockEditor.blocksToFullHTML).mockResolvedValue(htmlWithoutTableAttrs);
      vi.mocked(normalizeBlockNoteHTML).mockReturnValue(htmlWithoutTableAttrs);
      vi.mocked(processTableDataAttributes).mockReturnValue(htmlWithoutTableAttrs);

      const result = await serializeToHTML(mockEditor);

      expect(processTableDataAttributes).toHaveBeenCalledWith(htmlWithoutTableAttrs);
      expect(result.html).toBe(htmlWithoutTableAttrs);
    });

    describe('CSS styles options', () => {
      it('should include CSS styles by default', async () => {
        const styledHTML = `<style>css</style>${MOCK_FINAL_HTML}`;
        vi.mocked(prependCSSStyles).mockReturnValue(styledHTML);

        const result = await serializeToHTML(mockEditor);

        expect(prependCSSStyles).toHaveBeenCalledWith(MOCK_FINAL_HTML, undefined);
        expect(result.html).toBe(styledHTML);
      });

      it('should skip CSS styles when includeCSSStyles is false', async () => {
        vi.mocked(prependCSSStyles).mockClear();

        const result = await serializeToHTML(mockEditor, { includeCSSStyles: false });

        expect(prependCSSStyles).not.toHaveBeenCalled();
        expect(result.html).toBe(MOCK_FINAL_HTML);
      });

      it('should use custom CSS styles when provided', async () => {
        const customCSS = '.custom { color: red; }';
        const styledHTML = `<style>${customCSS}</style>${MOCK_FINAL_HTML}`;
        vi.mocked(prependCSSStyles).mockReturnValue(styledHTML);

        const result = await serializeToHTML(mockEditor, {
          includeCSSStyles: true,
          customCSSStyles: customCSS,
        });

        expect(prependCSSStyles).toHaveBeenCalledWith(MOCK_FINAL_HTML, customCSS);
        expect(result.html).toBe(styledHTML);
      });

      it('should handle both includeCSSStyles=false and customCSSStyles provided', async () => {
        vi.mocked(prependCSSStyles).mockClear();
        const customCSS = '.custom { color: red; }';

        const result = await serializeToHTML(mockEditor, {
          includeCSSStyles: false,
          customCSSStyles: customCSS,
        });

        expect(prependCSSStyles).not.toHaveBeenCalled();
        expect(result.html).toBe(MOCK_FINAL_HTML);
      });

      it('should handle empty options object', async () => {
        const styledHTML = `<style>css</style>${MOCK_FINAL_HTML}`;
        vi.mocked(prependCSSStyles).mockReturnValue(styledHTML);

        const result = await serializeToHTML(mockEditor, {});

        expect(prependCSSStyles).toHaveBeenCalledWith(MOCK_FINAL_HTML, undefined);
        expect(result.html).toBe(styledHTML);
      });
    });
  });

  describe('Inline Image Processing', () => {
    // Note: These functions are tested indirectly through integration tests
    // since they are internal functions used by serializeToHTML and loadEditorContent
    describe('Integration with serializeToHTML and loadEditorContent', () => {
      it('should process inline images correctly in HTML serialization workflow', async () => {
        const htmlWithInlineImages = `
          <span class="bn-inline-image-container" data-width="200" data-height="150">
            <img class="bn-inline-image" src="test.jpg" alt="Test" />
          </span>
        `;

        vi.mocked(mockEditor.blocksToFullHTML).mockResolvedValue(htmlWithInlineImages);
        vi.mocked(normalizeBlockNoteHTML).mockReturnValue(htmlWithInlineImages);
        vi.mocked(processTableDataAttributes).mockReturnValue(htmlWithInlineImages);
        vi.mocked(prependCSSStyles).mockImplementation((html) => html); // Pass through
        vi.mocked(convertHTMLToText).mockReturnValue('Test');

        const result = await serializeToHTML(mockEditor, { includeCSSStyles: false });

        // Verify that the processInlineImageStyles function was called and processed the HTML
        // The actual processing happens in the real function, not the mock
        expect(mockEditor.blocksToFullHTML).toHaveBeenCalled();
        expect(normalizeBlockNoteHTML).toHaveBeenCalled();
        expect(processTableDataAttributes).toHaveBeenCalled();
        expect(result.html).toBeDefined();
        expect(result.plainText).toBe('Test');
      });

      it('should process inline images correctly in HTML loading workflow', async () => {
        const htmlWithInlineImages = `
          <span class="bn-inline-image-container" data-width="180" data-height="120">
            <img class="bn-inline-image" src="test.jpg" alt="Test" />
          </span>
        `;

        const mockBlocks = [
          {
            type: 'paragraph',
            content: [
              {
                type: 'inlineImage',
                props: {
                  src: 'test.jpg',
                  alt: 'Test',
                  width: 100, // Default width
                  height: 'auto', // Default height
                },
              },
            ],
          },
        ];

        vi.mocked(removeCSSStyles).mockReturnValue(htmlWithInlineImages);
        vi.mocked(convertBreakLineToHTML).mockReturnValue(htmlWithInlineImages);
        vi.mocked(replaceWhiteSpaceToHTMLNbsp).mockReturnValue(htmlWithInlineImages);
        vi.mocked(mockEditor.tryParseHTMLToBlocks).mockResolvedValue(mockBlocks);
        vi.mocked(processMarkdownVariables).mockImplementation((blocks) => blocks);

        const result = await loadEditorContent(mockEditor, htmlWithInlineImages, false);

        // Verify that the processInlineImagePropsFromHTML function was called
        expect(removeCSSStyles).toHaveBeenCalledWith(htmlWithInlineImages);
        expect(mockEditor.tryParseHTMLToBlocks).toHaveBeenCalled();
        expect(processMarkdownVariables).toHaveBeenCalled();
        expect(result).toBeDefined();
        expect(Array.isArray(result)).toBe(true);
      });
    });

    describe('Inline Image Functions Coverage', () => {
      it('should verify inline image processing functions are called during content operations', async () => {
        // This test ensures the inline image processing functions are being used
        // The actual logic is tested through the real functions, not mocks

        const htmlWithImages = `
          <span class="bn-inline-image-container" data-width="200" data-height="150">
            <img class="bn-inline-image" src="test.jpg" alt="Test" />
          </span>
        `;

        // Test HTML serialization path (uses processInlineImageStyles)
        vi.mocked(mockEditor.blocksToFullHTML).mockResolvedValue(htmlWithImages);
        vi.mocked(normalizeBlockNoteHTML).mockReturnValue(htmlWithImages);
        vi.mocked(processTableDataAttributes).mockReturnValue(htmlWithImages);
        vi.mocked(prependCSSStyles).mockImplementation((html) => html);
        vi.mocked(convertHTMLToText).mockReturnValue('Test');

        const serializeResult = await serializeToHTML(mockEditor, { includeCSSStyles: false });
        expect(serializeResult.html).toBeDefined();

        // Test HTML loading path (uses processInlineImagePropsFromHTML)
        const mockBlocks = [
          { type: 'paragraph', content: [{ type: 'inlineImage', props: { src: 'test.jpg' } }] },
        ];
        vi.mocked(removeCSSStyles).mockReturnValue(htmlWithImages);
        vi.mocked(convertBreakLineToHTML).mockReturnValue(htmlWithImages);
        vi.mocked(replaceWhiteSpaceToHTMLNbsp).mockReturnValue(htmlWithImages);
        vi.mocked(mockEditor.tryParseHTMLToBlocks).mockResolvedValue(mockBlocks);
        vi.mocked(processMarkdownVariables).mockImplementation((blocks) => blocks);

        const loadResult = await loadEditorContent(mockEditor, htmlWithImages, false);
        expect(Array.isArray(loadResult)).toBe(true);
      });

      it('should handle HTML content without inline images', async () => {
        const plainHTML = '<p>Just text content</p>';

        // Test that functions handle non-image content gracefully
        vi.mocked(mockEditor.blocksToFullHTML).mockResolvedValue(plainHTML);
        vi.mocked(normalizeBlockNoteHTML).mockReturnValue(plainHTML);
        vi.mocked(processTableDataAttributes).mockReturnValue(plainHTML);
        vi.mocked(prependCSSStyles).mockImplementation((html) => html);
        vi.mocked(convertHTMLToText).mockReturnValue('Just text content');

        const result = await serializeToHTML(mockEditor, { includeCSSStyles: false });
        expect(result.html).toBeDefined();
        expect(result.plainText).toBe('Just text content');
      });
    });
  });

  describe('Integration scenarios', () => {
    it('should handle a complete workflow from HTML to serialization', async () => {
      // Setup for loadEditorContent
      vi.mocked(replaceWhiteSpaceToHTMLNbsp).mockReturnValue(MOCK_PROCESSED_HTML);
      vi.mocked(convertBreakLineToHTML).mockReturnValue(MOCK_HTML_CONTENT);
      vi.mocked(mockEditor.tryParseHTMLToBlocks).mockResolvedValue(MOCK_BLOCKS);
      vi.mocked(processMarkdownVariables).mockReturnValue(MOCK_PROCESSED_BLOCKS);

      // Setup for serializeToHTML
      vi.mocked(convertWhiteSpaceToNbspInBlockNote).mockReturnValue(MOCK_PROCESSED_BLOCKS);
      vi.mocked(mockEditor.blocksToFullHTML).mockResolvedValue(MOCK_HTML_CONTENT);
      vi.mocked(normalizeBlockNoteHTML).mockReturnValue(MOCK_NORMALIZED_HTML);
      vi.mocked(processTableDataAttributes).mockReturnValue(MOCK_FINAL_HTML);
      vi.mocked(prependCSSStyles).mockImplementation((html) => html); // Reset to pass-through
      vi.mocked(convertHTMLToText).mockReturnValue(MOCK_PLAIN_TEXT);

      // Load content
      const loadedBlocks = await loadEditorContent(mockEditor, MOCK_HTML_CONTENT, false);
      expect(loadedBlocks).toEqual(MOCK_PROCESSED_BLOCKS);

      // Serialize to HTML
      const serializedResult = await serializeToHTML(mockEditor);
      expect(serializedResult).toEqual({
        html: MOCK_FINAL_HTML,
        plainText: MOCK_PLAIN_TEXT,
      });
    });

    it('should handle a complete workflow from Markdown to serialization', async () => {
      // Setup for loadEditorContent (Markdown)
      vi.mocked(normalizeMarkdownToBlockNote).mockReturnValue(MOCK_PROCESSED_HTML);
      vi.mocked(mockEditor.tryParseMarkdownToBlocks).mockResolvedValue(MOCK_BLOCKS);
      vi.mocked(processMarkdownVariables).mockReturnValue(MOCK_PROCESSED_BLOCKS);

      // Setup for serializeToMarkdown
      vi.mocked(convertEmptyParagraphToSpaceContent).mockReturnValue(MOCK_PROCESSED_BLOCKS);
      vi.mocked(mockEditor.blocksToMarkdownLossy).mockResolvedValue(MOCK_MARKDOWN_CONTENT);
      vi.mocked(finalizeMarkdownFromBlockNote).mockReturnValue(MOCK_MARKDOWN_OUTPUT);

      // Load content
      const loadedBlocks = await loadEditorContent(mockEditor, MOCK_MARKDOWN_CONTENT, true);
      expect(loadedBlocks).toEqual(MOCK_PROCESSED_BLOCKS);

      // Serialize to Markdown
      const serializedResult = await serializeToMarkdown(mockEditor);
      expect(serializedResult).toBe(MOCK_MARKDOWN_OUTPUT);
    });
  });
});

describe('filterVariableNodesFromContent', () => {
  describe('when schema supports variables', () => {
    it('should return original content unchanged', () => {
      const contentWithVariables =
        '<p>Hello <span class="blocknote-variable">{{name}}</span> world</p>';
      const result = filterVariableNodesFromContent(contentWithVariables, true);
      expect(result).toBe(contentWithVariables);
    });

    it('should return empty content unchanged', () => {
      const result = filterVariableNodesFromContent('', true);
      expect(result).toBe('');
    });

    it('should return null content unchanged', () => {
      const result = filterVariableNodesFromContent(null as any, true);
      expect(result).toBe(null);
    });
  });

  describe('when schema does not support variables', () => {
    it('should filter out Variable nodes with class blocknote-variable', () => {
      const contentWithVariables =
        '<p>Hello <span class="blocknote-variable">{{name}}</span> world</p>';
      const result = filterVariableNodesFromContent(contentWithVariables, false);
      expect(result).toBe('<p>Hello {{name}} world</p>');
    });

    it('should filter out Variable nodes with data-blocknote-variable attribute', () => {
      const contentWithVariables =
        '<p>Hello <span data-blocknote-variable="true">{{email}}</span> world</p>';
      const result = filterVariableNodesFromContent(contentWithVariables, false);
      expect(result).toBe('<p>Hello {{email}} world</p>');
    });

    it('should handle multiple Variable nodes', () => {
      const contentWithVariables =
        '<p>Hello <span class="blocknote-variable">{{name}}</span> and <span data-blocknote-variable="true">{{email}}</span></p>';
      const result = filterVariableNodesFromContent(contentWithVariables, false);
      expect(result).toBe('<p>Hello {{name}} and {{email}}</p>');
    });

    it('should handle nested Variable nodes', () => {
      const contentWithVariables =
        '<div><p>Hello <span class="blocknote-variable">{{name}}</span></p><p>Email: <span data-blocknote-variable="true">{{email}}</span></p></div>';
      const result = filterVariableNodesFromContent(contentWithVariables, false);
      expect(result).toBe('<div><p>Hello {{name}}</p><p>Email: {{email}}</p></div>');
    });

    it('should handle content without Variable nodes', () => {
      const contentWithoutVariables = '<p>Hello world</p>';
      const result = filterVariableNodesFromContent(contentWithoutVariables, false);
      expect(result).toBe(contentWithoutVariables);
    });

    it('should handle empty content', () => {
      const result = filterVariableNodesFromContent('', false);
      expect(result).toBe('');
    });

    it('should handle malformed HTML gracefully', () => {
      const malformedContent = '<p>Hello <span class="blocknote-variable">{{name</p>';
      const result = filterVariableNodesFromContent(malformedContent, false);
      // Should filter Variable nodes even in malformed HTML
      expect(result).toBe('<p>Hello {{name</p>');
    });

    it('should handle Variable nodes with empty text content', () => {
      const contentWithEmptyVariables =
        '<p>Hello <span class="blocknote-variable"></span> world</p>';
      const result = filterVariableNodesFromContent(contentWithEmptyVariables, false);
      expect(result).toBe('<p>Hello  world</p>');
    });

    it('should preserve other HTML elements and attributes', () => {
      const complexContent =
        '<div class="container"><p>Hello <span class="blocknote-variable">{{name}}</span> <strong>world</strong></p><img src="test.jpg" alt="test" /></div>';
      const result = filterVariableNodesFromContent(complexContent, false);
      expect(result).toBe(
        '<div class="container"><p>Hello {{name}} <strong>world</strong></p><img src="test.jpg" alt="test"></div>'
      );
    });
  });
});

describe('filterVariableNodesFromBlocks', () => {
  describe('when schema supports variables', () => {
    it('should return original blocks unchanged', () => {
      const blocksWithVariables = [
        {
          id: '1',
          type: 'paragraph',
          content: [
            { type: 'text', text: 'Hello ' },
            { type: 'variable', props: { variable: 'name' } },
            { type: 'text', text: ' world' },
          ],
        },
      ];
      const result = filterVariableNodesFromBlocks(blocksWithVariables, true);
      expect(result).toBe(blocksWithVariables);
    });

    it('should return empty blocks unchanged', () => {
      const result = filterVariableNodesFromBlocks([], true);
      expect(result).toEqual([]);
    });

    it('should return null blocks unchanged', () => {
      const result = filterVariableNodesFromBlocks(null as any, true);
      expect(result).toBe(null);
    });
  });

  describe('when schema does not support variables', () => {
    it('should convert Variable nodes to text nodes', () => {
      const blocksWithVariables = [
        {
          id: '1',
          type: 'paragraph',
          content: [
            { type: 'text', text: 'Hello ' },
            { type: 'variable', props: { variable: 'name' } },
            { type: 'text', text: ' world' },
          ],
        },
      ];
      const result = filterVariableNodesFromBlocks(blocksWithVariables, false);
      expect(result).toEqual([
        {
          id: '1',
          type: 'paragraph',
          content: [
            { type: 'text', text: 'Hello ' },
            { type: 'text', text: '{{name}}', styles: {} },
            { type: 'text', text: ' world' },
          ],
        },
      ]);
    });

    it('should handle Variable nodes with missing props', () => {
      const blocksWithVariables = [
        {
          id: '1',
          type: 'paragraph',
          content: [{ type: 'variable', props: {} }, { type: 'variable' }],
        },
      ];
      const result = filterVariableNodesFromBlocks(blocksWithVariables, false);
      expect(result).toEqual([
        {
          id: '1',
          type: 'paragraph',
          content: [
            { type: 'text', text: '{{unknown}}', styles: {} },
            { type: 'text', text: '{{unknown}}', styles: {} },
          ],
        },
      ]);
    });

    it('should handle nested blocks (e.g., table cells)', () => {
      const blocksWithNestedVariables = [
        {
          id: '1',
          type: 'table',
          children: [
            {
              id: '2',
              type: 'tableRow',
              children: [
                {
                  id: '3',
                  type: 'tableCell',
                  content: [
                    { type: 'text', text: 'Name: ' },
                    { type: 'variable', props: { variable: 'name' } },
                  ],
                },
              ],
            },
          ],
        },
      ];
      const result = filterVariableNodesFromBlocks(blocksWithNestedVariables, false);
      expect(result).toEqual([
        {
          id: '1',
          type: 'table',
          children: [
            {
              id: '2',
              type: 'tableRow',
              children: [
                {
                  id: '3',
                  type: 'tableCell',
                  content: [
                    { type: 'text', text: 'Name: ' },
                    { type: 'text', text: '{{name}}', styles: {} },
                  ],
                },
              ],
            },
          ],
        },
      ]);
    });

    it('should preserve other inline content types', () => {
      const blocksWithMixedContent = [
        {
          id: '1',
          type: 'paragraph',
          content: [
            { type: 'text', text: 'Hello ', styles: { bold: true } },
            { type: 'variable', props: { variable: 'name' } },
            { type: 'text', text: ' world', styles: { italic: true } },
          ],
        },
      ];
      const result = filterVariableNodesFromBlocks(blocksWithMixedContent, false);
      expect(result).toEqual([
        {
          id: '1',
          type: 'paragraph',
          content: [
            { type: 'text', text: 'Hello ', styles: { bold: true } },
            { type: 'text', text: '{{name}}', styles: {} },
            { type: 'text', text: ' world', styles: { italic: true } },
          ],
        },
      ]);
    });

    it('should handle blocks without content', () => {
      const blocksWithoutContent = [
        {
          id: '1',
          type: 'paragraph',
        },
        {
          id: '2',
          type: 'heading',
          content: [{ type: 'variable', props: { variable: 'title' } }],
        },
      ];
      const result = filterVariableNodesFromBlocks(blocksWithoutContent, false);
      expect(result).toEqual([
        {
          id: '1',
          type: 'paragraph',
        },
        {
          id: '2',
          type: 'heading',
          content: [{ type: 'text', text: '{{title}}', styles: {} }],
        },
      ]);
    });
  });
});
