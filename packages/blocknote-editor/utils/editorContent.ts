import type { BlockNoteEditor } from '@blocknote/core';
import {
  convertEmptyParagraphToSpaceContent,
  convertWhiteSpaceToNbspInBlockNote,
  normalizeBlockNoteHTML,
  parseSpaceContentToEmptyParagraph,
} from './content';
import { prependCSSStyles, removeCSSStyles } from './cssStyles';
import {
  convertBreakLineToHTML,
  finalizeMarkdownFromBlockNote,
  normalizeMarkdownToBlockNote,
  replaceWhiteSpaceToHTMLNbsp,
} from './string';
import { convertHTMLToText } from './string';
import { processTableDataAttributes } from './table';

/**
 * Process inline images and apply their width/height styles from data attributes
 * @param htmlContent - The HTML content containing inline images
 * @returns Processed HTML content with inline styles for inline images
 */
const processInlineImageStyles = (htmlContent: string): string => {
  try {
    if (!htmlContent || !htmlContent.includes('bn-inline-image')) {
      return htmlContent || '';
    }

    // Create a temporary DOM element to parse the HTML
    const parser = new DOMParser();
    const doc = parser.parseFromString(htmlContent, 'text/html');

    // Find all inline image containers with data attributes
    const inlineImageContainers = doc.querySelectorAll(
      'span.bn-inline-image-container[data-width], span.bn-inline-image-container[data-height]'
    );

    if (inlineImageContainers.length === 0) {
      return htmlContent;
    }

    // Process each container and apply styles to the inner image from data attributes
    inlineImageContainers.forEach((container) => {
      const dataWidth = container.getAttribute('data-width');
      const dataHeight = container.getAttribute('data-height');

      // Find the img element within this container
      const img = container.querySelector('img.bn-inline-image');
      if (!img) return;

      if (dataWidth || dataHeight) {
        const existingStyle = img.getAttribute('style') || '';

        // Create width and height styles from data attributes
        const widthStyle = dataWidth ? `width: ${dataWidth}px` : '';
        const heightStyle =
          dataHeight === 'auto' ? 'height: auto' : dataHeight ? `height: ${dataHeight}px` : '';

        // Remove any existing width/height styles first
        let newStyle = existingStyle
          .replace(/width:\s*[^;]+;?/gi, '')
          .replace(/height:\s*[^;]+;?/gi, '')
          .trim();

        // Add the new width and height styles
        const stylesToAdd = [widthStyle, heightStyle].filter(Boolean);
        if (stylesToAdd.length > 0) {
          if (newStyle && !newStyle.endsWith(';')) {
            newStyle += '; ';
          }
          newStyle += stylesToAdd.join('; ');
        }

        // Set the updated style attribute
        img.setAttribute('style', newStyle);

        // Keep data attributes for proper parsing when loading HTML back into editor
        // Do NOT remove data-width and data-height as they're needed for persistence
      }
    });

    // Return the processed HTML
    return doc.body.innerHTML;
  } catch (error) {
    console.error('BlockNote: Error processing inline image styles:', error);
    return htmlContent; // Return original content if processing fails
  }
};
import { processMarkdownVariables } from './variableParser';

/**
 * Post-process blocks to fix inline image props from HTML data attributes
 * @param blocks Array of blocks to process
 * @param htmlContent Original HTML content for reference
 * @returns Processed blocks with corrected inline image props
 */
const processInlineImagePropsFromHTML = (blocks: any[], htmlContent: string): any[] => {
  if (!htmlContent || !htmlContent.includes('bn-inline-image-container')) {
    return blocks;
  }

  try {
    // Parse the HTML to extract data attributes
    const parser = new DOMParser();
    const doc = parser.parseFromString(htmlContent, 'text/html');
    const containers = doc.querySelectorAll(
      'span.bn-inline-image-container[data-width], span.bn-inline-image-container[data-height]'
    );

    if (containers.length === 0) {
      return blocks;
    }

    // Extract dimension data from HTML
    const imageDimensions: Array<{ width?: number; height?: number | 'auto' }> = [];
    containers.forEach((container) => {
      const dataWidth = container.getAttribute('data-width');
      const dataHeight = container.getAttribute('data-height');

      const dimensions: { width?: number; height?: number | 'auto' } = {};

      if (dataWidth) {
        const width = Number(dataWidth);
        if (!Number.isNaN(width)) {
          dimensions.width = width;
        }
      }

      if (dataHeight) {
        dimensions.height = dataHeight === 'auto' ? 'auto' : Number(dataHeight) || 'auto';
      }

      imageDimensions.push(dimensions);
    });

    // Recursively update inline image props in blocks
    let imageIndex = 0;
    const updateBlocks = (blocks: any[]): any[] => {
      return blocks.map((block) => {
        const updatedBlock = { ...block };

        // Process inline content
        if (block.content && Array.isArray(block.content)) {
          updatedBlock.content = block.content.map((item: any) => {
            if (item.type === 'inlineImage' && imageIndex < imageDimensions.length) {
              const dimensions = imageDimensions[imageIndex];
              imageIndex++;

              return {
                ...item,
                props: {
                  ...item.props,
                  ...dimensions,
                },
              };
            }
            return item;
          });
        }

        // Process nested blocks (like table cells)
        if (block.children && Array.isArray(block.children)) {
          updatedBlock.children = updateBlocks(block.children);
        }

        return updatedBlock;
      });
    };

    return updateBlocks(blocks);
  } catch (error) {
    console.error('Error processing inline image props from HTML:', error);
    return blocks;
  }
};

/**
 * Load HTML content into the editor
 * @param editor BlockNote editor instance (any compatible type)
 * @param content HTML or Markdown content to load
 * @param isMarkdown Whether the content is in Markdown format
 * @returns Promise<any[]> Array of parsed blocks
 */
export const loadEditorContent = async (
  editor: any,
  content: string,
  isMarkdown = false,
  supportsVariables = true
): Promise<any[]> => {
  let blocks: any[];

  if (isMarkdown) {
    // Normalize markdown and parse to blocks
    const normalizedMarkdown = normalizeMarkdownToBlockNote(content);
    blocks = await editor.tryParseMarkdownToBlocks(normalizedMarkdown);

    // Post-process blocks to ensure variables are properly converted to inline content
    blocks = processMarkdownVariables(blocks);
  } else {
    // Remove any CSS styles that might have been prepended to the HTML content
    // This prevents <style> tags from being rendered as content in the editor
    const cleanHTML = removeCSSStyles(content);
    const processedHTML = replaceWhiteSpaceToHTMLNbsp(convertBreakLineToHTML(cleanHTML));
    blocks = await editor.tryParseHTMLToBlocks(processedHTML);

    // Post-process blocks to fix inline image props from HTML data attributes
    blocks = processInlineImagePropsFromHTML(blocks, content);

    // Also process HTML content for variables (in case HTML contains {{variable}} syntax)
    blocks = processMarkdownVariables(blocks);
  }

  // Filter Variable nodes from blocks if the schema doesn't support them
  blocks = filterVariableNodesFromBlocks(blocks, supportsVariables);

  return blocks;
};

/**
 * Process blocks for Markdown editor (handles empty paragraphs)
 * @param blocks Editor blocks
 * @returns Processed blocks
 */
export const processBlocksForMarkdown = (blocks: any[]): any[] => {
  return parseSpaceContentToEmptyParagraph(blocks);
};

/**
 * Serialize editor content to Markdown
 * @param editor BlockNote editor instance
 * @returns Promise<string> The markdown content
 */
export const serializeToMarkdown = async (editor: BlockNoteEditor): Promise<string> => {
  // Convert empty paragraphs to space content for markdown
  const processedBlocks = convertEmptyParagraphToSpaceContent(editor.document);

  // Convert to markdown
  const markdown = await editor.blocksToMarkdownLossy(processedBlocks);

  // Finalize markdown
  return finalizeMarkdownFromBlockNote(markdown);
};

/**
 * Serialize editor content to HTML
 * @param editor BlockNote editor instance (any compatible type)
 * @param options Optional configuration for HTML serialization
 * @param options.includeCSSStyles Whether to prepend CSS styles to the HTML output (default: true)
 * @param options.customCSSStyles Custom CSS styles to prepend instead of default styles
 * @returns Promise<{html: string, plainText: string}> The HTML content and plain text
 */
export const serializeToHTML = async (
  editor: any,
  options: {
    includeCSSStyles?: boolean;
    customCSSStyles?: string;
  } = {}
): Promise<{ html: string; plainText: string }> => {
  const { includeCSSStyles = true, customCSSStyles } = options;

  const html = await editor.blocksToFullHTML(convertWhiteSpaceToNbspInBlockNote(editor.document));
  const normalizedHTML = normalizeBlockNoteHTML(html);

  // Process table data attributes and convert to inline CSS styles
  const processedTableHTML = processTableDataAttributes(normalizedHTML);

  // Process inline images and apply their width/height styles from data attributes
  const processedHTML = processInlineImageStyles(processedTableHTML);

  // Prepend CSS styles if requested
  const finalHTML = includeCSSStyles
    ? prependCSSStyles(processedHTML, customCSSStyles)
    : processedHTML;

  const plainText = convertHTMLToText(html);

  return { html: finalHTML, plainText };
};

/**
 * Filters Variable nodes from HTML content when the schema doesn't support them
 * This prevents errors when rendering old content that contains Variable nodes
 * but the current schema doesn't include Variable support
 *
 * @param htmlContent - The HTML content that may contain Variable nodes
 * @param supportsVariables - Whether the current schema supports Variable nodes
 * @returns Filtered HTML content with Variable nodes removed if not supported
 */
export const filterVariableNodesFromContent = (
  htmlContent: string,
  supportsVariables: boolean
): string => {
  if (supportsVariables || !htmlContent) {
    return htmlContent;
  }

  try {
    // Create a temporary DOM element to parse the HTML
    const parser = new DOMParser();
    const doc = parser.parseFromString(htmlContent, 'text/html');

    // Find all Variable nodes (they have the class 'blocknote-variable')
    const variableNodes = doc.querySelectorAll(
      '.blocknote-variable, [data-blocknote-variable="true"]'
    );

    if (variableNodes.length === 0) {
      return htmlContent;
    }

    // Replace Variable nodes with their text content (the variable name)
    variableNodes.forEach((node) => {
      const textContent = node.textContent || '';
      // Replace the Variable node with a text node containing the variable name
      const textNode = doc.createTextNode(textContent);
      node.parentNode?.replaceChild(textNode, node);
    });

    // Return the modified HTML
    return doc.body.innerHTML;
  } catch (error) {
    console.warn('Failed to filter Variable nodes from content:', error);
    // Return original content if filtering fails
    return htmlContent;
  }
};

/**
 * Filters Variable nodes from parsed blocks when the schema doesn't support them
 * This prevents errors when rendering old content that contains Variable nodes
 * but the current schema doesn't include Variable support
 *
 * @param blocks - The parsed blocks that may contain Variable nodes
 * @param supportsVariables - Whether the current schema supports Variable nodes
 * @returns Filtered blocks with Variable nodes converted to text if not supported
 */
export const filterVariableNodesFromBlocks = (blocks: any[], supportsVariables: boolean): any[] => {
  if (supportsVariables || !blocks || !Array.isArray(blocks)) {
    return blocks;
  }

  const filterBlocks = (blockList: any[]): any[] => {
    return blockList.map((block) => {
      const filteredBlock = { ...block };

      // Process inline content in the block
      if (block.content && Array.isArray(block.content)) {
        filteredBlock.content = block.content.map((item: any) => {
          // If this is a Variable node and variables are not supported, convert to text
          if (item.type === 'variable') {
            return {
              type: 'text',
              text: `{{${item.props?.variable || 'unknown'}}}`,
              styles: {},
            };
          }
          return item;
        });
      }

      // Recursively process nested blocks (e.g., in table cells)
      if (block.children && Array.isArray(block.children)) {
        filteredBlock.children = filterBlocks(block.children);
      }

      return filteredBlock;
    });
  };

  return filterBlocks(blocks);
};
