import type { defaultStyleSpecs } from '@blocknote/core';
import get from 'lodash/get';
import isEmpty from 'lodash/isEmpty';
import map from 'lodash/map';
import set from 'lodash/set';
import { DEFAULT_COLORS } from '../constants';
import { decodeSpecialCharacters, replaceWhiteSpaceToHTMLNbsp } from './string';
import { isMarkdownContent } from './string';

/**
 * Customize default style specs.
 * @param styleSpecs Style specs to customize
 * @returns Customized style specs
 */
export const customizeDefaultStyleSpecs = (styleSpecs: typeof defaultStyleSpecs) => {
  // Customize text color implementation to include data-text-color attribute.
  if (styleSpecs.textColor) {
    set(styleSpecs.textColor.implementation.mark.config, 'renderHTML', ({ HTMLAttributes }) => {
      const color = get(HTMLAttributes, 'data-text-color', '');

      // Use color from root vars if it is a default color.
      const colorStyle = DEFAULT_COLORS.includes(color)
        ? `var(--bn-colors-highlights-${color}-text)`
        : color;

      set(HTMLAttributes, 'style', `color: ${colorStyle}; text-decoration-color: ${colorStyle};`);

      return ['span', HTMLAttributes, 0];
    });
  }

  // Customize background color implementation to include data-background-color attribute.
  if (styleSpecs.backgroundColor) {
    set(
      styleSpecs.backgroundColor.implementation.mark.config,
      'renderHTML',
      ({ HTMLAttributes }) => {
        const color = get(HTMLAttributes, 'data-background-color', '');

        if (!DEFAULT_COLORS.includes(color)) {
          set(HTMLAttributes, 'style', `background-color: ${color};`);
        }

        return ['span', HTMLAttributes, 0];
      }
    );
  }

  return styleSpecs;
};

/**
 * Replace empty paragraph from block to Space Content with `&nbsp`.
 * @param blocks Blocks to replace empty paragraph
 * @returns Blocks with replaced empty paragraph
 */
export const convertEmptyParagraphToSpaceContent = (blocks: any[]) => {
  return map(blocks, (block, index) => {
    // Skip the last block
    if (!blocks[index + 1]) {
      return block;
    }

    // Skip if the block is not a paragraph
    if (block.type === 'paragraph' && isEmpty(block.content)) {
      return {
        ...block,
        content: [{ styles: {}, type: 'text', text: '&nbsp;' }],
      };
    }

    return block;
  });
};

/**
 * Parse Space Content to empty paragraph.
 * @param blocks Blocks to parse
 * @returns Blocks with parsed empty paragraph
 */
export const parseSpaceContentToEmptyParagraph = (blocks: any[]) => {
  return map(blocks, (block, index) => {
    // Skip the last block
    if (!blocks[index + 1]) {
      return block;
    }

    // Skip if the block is not a paragraph
    if (block.type === 'paragraph' && !isEmpty(block.content)) {
      const content = block.content[0];

      // Skip if the paragraph content is not text (e.g. image, video, link, etc.)
      if (content.type !== 'text') return block;

      // If the content is empty, remove the block content
      if (/^\s*$/.test(content.text)) {
        return { ...block, content: [] };
      }
    }

    return block;
  });
};

/**
 * Replace white space to HTML nbsp.
 * @param blocks Blocks to replace white space
 * @returns Blocks with replaced white space
 */
export const convertWhiteSpaceToNbspInBlockNote = (blocks: any[]) => {
  return map(blocks, (block, index) => {
    // Skip the last block
    if (!blocks[index + 1]) {
      return block;
    }

    // Skip if the block is not a paragraph
    if (block.type === 'paragraph' && !isEmpty(block.content)) {
      const customizedContent = block.content.map((content: any) => {
        // Skip if the paragraph content is not text (e.g. image, video, link, etc.)
        if (content.type !== 'text') return content;

        return { ...content, text: replaceWhiteSpaceToHTMLNbsp(content.text) };
      });

      // If the content is empty, remove the block content
      return {
        ...block,
        content: customizedContent,
      };
    }

    return block;
  });
};

/**
 * Decode special characters in paragraph.
 * @param blocks Blocks to decode special characters
 * @returns Blocks with decoded special characters
 */
export const decodeTextInParagraphBlock = (blocks: any[]) => {
  return map(blocks, (block) => {
    // Skip if the block is not a paragraph
    if (block.type === 'paragraph' && !isEmpty(block.content)) {
      const customizedContent = block.content.map((content: any) => {
        // Skip if the paragraph content is not text (e.g. image, video, link, etc.)
        if (content.type !== 'text') return content;

        // Remove &nbsp; and white space from the start of the text
        const cleanedText = content.text.replace(/^(&nbsp;|&amp;nbsp;|\s)+/g, '');

        return { ...content, text: decodeSpecialCharacters(cleanedText) };
      });

      // If the content is empty, remove the block content
      return {
        ...block,
        content: customizedContent,
      };
    }

    return block;
  });
};

/**
 * Check if the content is a BlockNote markdown content
 * @param content Content to check
 * @returns boolean
 */
export const isBlockNoteMarkdownContent = (content: string): boolean => {
  // Return false if content is null, undefined, or empty
  if (!content || typeof content !== 'string') {
    return false;
  }

  // Check for common BlockNote HTML markers
  const hasBlockNoteHTML =
    content.includes('bn-block') ||
    content.includes('data-node-type') ||
    content.includes('data-content-type') ||
    content.includes('bn-inline-content') ||
    content.includes('bn-block-content');

  // Return true only if it's valid markdown content and doesn't contain BlockNote HTML
  return !hasBlockNoteHTML && isMarkdownContent(content);
};

/**
 * Processes HTML anchor tags to ensure proper target attributes.
 * Adds target="_blank" by default or uses specified target from href query parameter.
 *
 * @param htmlContent - Raw HTML content containing anchor tags
 * @returns Processed HTML content with normalized target attributes
 */
export function processAnchorTargets(html: string): string {
  // Create a temporary DOM element to parse the HTML
  const parser = new DOMParser();
  const doc = parser.parseFromString(html, 'text/html');

  // Process all anchor elements
  doc.querySelectorAll('a').forEach((anchor) => {
    // Check if URL has target parameter
    const url = new URL(anchor.href);
    const urlTarget = url.searchParams.get('target');

    // If anchor doesn't have a target attribute, set it
    if (!anchor.hasAttribute('target')) {
      // Use URL target parameter if present, otherwise default to _blank
      anchor.setAttribute('target', urlTarget || '_blank');
    }
  });

  return doc.body.innerHTML;
}

/**
 * Normalizes HTML content from BlockNote editor by:
 * 1. Processing anchor tag target attributes
 * 2. Converting special characters (e.g., &nbsp;)
 *
 * @param htmlContent - Raw HTML content from BlockNote editor
 * @returns Normalized HTML content
 */
export function normalizeBlockNoteHTML(html: string): string {
  // Process anchor targets first
  const processedAnchors = processAnchorTargets(html);

  // Replace &nbsp; with regular space
  return processedAnchors.replace(/&amp;nbsp;/g, ' ').replace(/&nbsp;/g, ' ');
}
