import { defaultStyleSpecs } from '@blocknote/core';
import { describe, expect, it } from 'vitest';
import {
  convertEmptyParagraphToSpaceContent,
  convertWhiteSpaceToNbspInBlockNote,
  customizeDefaultStyleSpecs,
  decodeTextInParagraphBlock,
  isBlockNoteMarkdownContent,
  normalizeBlockNoteHTML,
  parseSpaceContentToEmptyParagraph,
  processAnchorTargets,
} from './content';

// Test Constants
const TEST_INPUTS = {
  SIMPLE_LINK: '<a href="https://resola-test.dev">Link</a>',
  LINK_WITH_TARGET: '<a href="https://resola-test.dev" target="_blank">Link</a>',
  LINK_WITH_TARGET_PARAM: '<a href="https://resola-test.dev?target=_self">Link</a>',
  EMPTY_CONTENT: '',
  MULTIPLE_LINKS:
    '<a href="https://resola-test.dev/1">Link1</a><a href="https://resola-test.dev/2">Link2</a>',
};

const EXPECTED_OUTPUTS = {
  SIMPLE_LINK_WITH_TARGET: '<a href="https://resola-test.dev" target="_blank">Link</a>',
  LINK_WITH_EXISTING_TARGET: '<a href="https://resola-test.dev" target="_blank">Link</a>',
  LINK_WITH_SELF_TARGET: '<a href="https://resola-test.dev?target=_self" target="_self">Link</a>',
  EMPTY_CONTENT: '',
  MULTIPLE_LINKS_WITH_TARGET:
    '<a href="https://resola-test.dev/1" target="_blank">Link1</a><a href="https://resola-test.dev/2" target="_blank">Link2</a>',
};

describe('processAnchorTargets', () => {
  it('should add target="_blank" to links without target', () => {
    const result = processAnchorTargets(TEST_INPUTS.SIMPLE_LINK);
    expect(result).toBe(EXPECTED_OUTPUTS.SIMPLE_LINK_WITH_TARGET);
  });

  it('should preserve existing target attributes', () => {
    const result = processAnchorTargets(TEST_INPUTS.LINK_WITH_TARGET);
    expect(result).toBe(EXPECTED_OUTPUTS.LINK_WITH_EXISTING_TARGET);
  });

  it('should use target from URL parameter if present', () => {
    const result = processAnchorTargets(TEST_INPUTS.LINK_WITH_TARGET_PARAM);
    expect(result).toBe(EXPECTED_OUTPUTS.LINK_WITH_SELF_TARGET);
  });

  it('should handle empty content', () => {
    const result = processAnchorTargets(TEST_INPUTS.EMPTY_CONTENT);
    expect(result).toBe(EXPECTED_OUTPUTS.EMPTY_CONTENT);
  });

  it('should process multiple links in content', () => {
    const result = processAnchorTargets(TEST_INPUTS.MULTIPLE_LINKS);
    expect(result).toBe(EXPECTED_OUTPUTS.MULTIPLE_LINKS_WITH_TARGET);
  });
});

describe('normalizeBlockNoteHTML', () => {
  it('should process anchors and decode nbsp', () => {
    const input = '<a href="https://resola-test.dev">Link</a>&nbsp;text';
    const expected = '<a href="https://resola-test.dev" target="_blank">Link</a> text';
    expect(normalizeBlockNoteHTML(input)).toBe(expected);
  });

  it('should replace &amp;nbsp; with spaces', () => {
    const input = '<p>Hello&amp;nbsp;world</p>';
    const expected = '<p>Hello world</p>';
    expect(normalizeBlockNoteHTML(input)).toBe(expected);
  });
});

describe('convertEmptyParagraphToSpaceContent', () => {
  it('should convert empty paragraph to space content', () => {
    const blocks = [
      { type: 'paragraph', content: [] },
      { type: 'heading', content: [{ type: 'text', text: 'Heading', styles: {} }] },
    ];
    const expected = [
      {
        type: 'paragraph',
        content: [{ styles: {}, type: 'text', text: '&nbsp;' }],
      },
      { type: 'heading', content: [{ type: 'text', text: 'Heading', styles: {} }] },
    ];
    expect(convertEmptyParagraphToSpaceContent(blocks)).toEqual(expected);
  });

  it('should not modify non-empty paragraphs', () => {
    const blocks = [
      {
        type: 'paragraph',
        content: [{ type: 'text', text: 'Not empty', styles: {} }],
      },
      { type: 'heading', content: [{ type: 'text', text: 'Heading', styles: {} }] },
    ];
    expect(convertEmptyParagraphToSpaceContent(blocks)).toEqual(blocks);
  });

  it('should not modify the last block even if it is an empty paragraph', () => {
    const blocks = [
      { type: 'heading', content: [{ type: 'text', text: 'Heading', styles: {} }] },
      { type: 'paragraph', content: [] },
    ];
    expect(convertEmptyParagraphToSpaceContent(blocks)).toEqual(blocks);
  });
});

describe('parseSpaceContentToEmptyParagraph', () => {
  it('should parse space content to empty paragraph', () => {
    const blocks = [
      {
        type: 'paragraph',
        content: [{ styles: {}, type: 'text', text: ' ' }],
      },
      { type: 'heading', content: [{ type: 'text', text: 'Heading', styles: {} }] },
    ];
    const expected = [
      { type: 'paragraph', content: [] },
      { type: 'heading', content: [{ type: 'text', text: 'Heading', styles: {} }] },
    ];
    expect(parseSpaceContentToEmptyParagraph(blocks)).toEqual(expected);
  });

  it('should not modify non-space content', () => {
    const blocks = [
      {
        type: 'paragraph',
        content: [{ type: 'text', text: 'Not empty', styles: {} }],
      },
      { type: 'heading', content: [{ type: 'text', text: 'Heading', styles: {} }] },
    ];
    expect(parseSpaceContentToEmptyParagraph(blocks)).toEqual(blocks);
  });

  it('should not modify the last block even if it has space content', () => {
    const blocks = [
      { type: 'heading', content: [{ type: 'text', text: 'Heading', styles: {} }] },
      {
        type: 'paragraph',
        content: [{ styles: {}, type: 'text', text: ' ' }],
      },
    ];
    expect(parseSpaceContentToEmptyParagraph(blocks)).toEqual(blocks);
  });

  it('should not modify non-text content', () => {
    const blocks = [
      {
        type: 'paragraph',
        content: [{ type: 'image', url: 'image.jpg', styles: {} }],
      },
      { type: 'heading', content: [{ type: 'text', text: 'Heading', styles: {} }] },
    ];
    expect(parseSpaceContentToEmptyParagraph(blocks)).toEqual(blocks);
  });
});

describe('convertWhiteSpaceToNbspInBlockNote', () => {
  it('should convert white space to HTML nbsp in paragraphs', () => {
    const blocks = [
      {
        type: 'paragraph',
        content: [{ styles: {}, type: 'text', text: 'Hello   world' }],
      },
      { type: 'heading', content: [{ type: 'text', text: 'Heading', styles: {} }] },
    ];
    // Testing for the expected outcome based on replaceWhiteSpaceToHTMLNbsp implementation
    // This is a simplified test - the actual result might differ depending on implementation
    expect(convertWhiteSpaceToNbspInBlockNote(blocks)[0].content[0].text).not.toBe('Hello   world');
  });

  it('should not modify non-text content', () => {
    const blocks = [
      {
        type: 'paragraph',
        content: [{ type: 'image', url: 'image.jpg', styles: {} }],
      },
      { type: 'heading', content: [{ type: 'text', text: 'Heading', styles: {} }] },
    ];
    expect(convertWhiteSpaceToNbspInBlockNote(blocks)).toEqual(blocks);
  });
});

describe('decodeTextInParagraphBlock', () => {
  it('should decode special characters in paragraph blocks', () => {
    const blocks = [
      {
        type: 'paragraph',
        content: [{ styles: {}, type: 'text', text: '&nbsp;Hello&amp;world' }],
      },
      { type: 'heading', content: [{ type: 'text', text: 'Heading', styles: {} }] },
    ];
    // Check that the leading &nbsp; is removed
    expect(decodeTextInParagraphBlock(blocks)[0].content[0].text).not.toContain('&nbsp;');
  });

  it('should not modify non-text content', () => {
    const blocks = [
      {
        type: 'paragraph',
        content: [{ type: 'image', url: 'image.jpg', styles: {} }],
      },
      { type: 'heading', content: [{ type: 'text', text: 'Heading', styles: {} }] },
    ];
    expect(decodeTextInParagraphBlock(blocks)).toEqual(blocks);
  });
});

describe('isBlockNoteMarkdownContent', () => {
  it('should identify non-BlockNote HTML content as markdown', () => {
    const content = '# Heading\n\nThis is a paragraph with **bold** text.';
    expect(isBlockNoteMarkdownContent(content)).toBe(true);
  });

  it('should identify BlockNote HTML content as not markdown', () => {
    const content = '<div class="bn-block" data-node-type="paragraph">This is BlockNote HTML</div>';
    expect(isBlockNoteMarkdownContent(content)).toBe(false);
  });

  it('should return false for empty content', () => {
    expect(isBlockNoteMarkdownContent('')).toBe(false);
  });

  it('should return false for undefined content', () => {
    expect(isBlockNoteMarkdownContent(undefined as any)).toBe(false);
  });

  it('should return false for null content', () => {
    expect(isBlockNoteMarkdownContent(null as any)).toBe(false);
  });

  it('should return false for non-string content', () => {
    expect(isBlockNoteMarkdownContent(123 as any)).toBe(false);
  });
});

describe('customizeDefaultStyleSpecs', () => {
  it('should customize text color implementation', () => {
    const customStyleSpecs = customizeDefaultStyleSpecs({ ...defaultStyleSpecs });
    expect(customStyleSpecs.textColor).toBeDefined();
    expect(typeof customStyleSpecs.textColor?.implementation.mark.config.renderHTML).toBe(
      'function'
    );
  });

  it('should customize background color implementation', () => {
    const customStyleSpecs = customizeDefaultStyleSpecs({ ...defaultStyleSpecs });
    expect(customStyleSpecs.backgroundColor).toBeDefined();
    expect(typeof customStyleSpecs.backgroundColor?.implementation.mark.config.renderHTML).toBe(
      'function'
    );
  });
});
