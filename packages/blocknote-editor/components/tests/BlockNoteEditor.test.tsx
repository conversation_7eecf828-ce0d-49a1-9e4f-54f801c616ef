import { fireEvent, screen } from '@testing-library/react';
import React from 'react';
import { afterEach, beforeEach, describe, expect, it, vi } from 'vitest';
import {
  MOCK_MEDIA_HTML_CONTENT,
  MOCK_RICH_HTML_CONTENT,
  MOCK_SIMPLE_HTML_CONTENT,
} from '../../mock-data/mockEditorContent';
import { renderWithMantine } from '../../utils/unitTest';
import BlockNoteEditor from '../BlockNoteEditor';

// Use vi.hoisted to ensure mocks are applied before any imports
const mockUseFilterSuggestions = vi.hoisted(() => {
  return () => ({
    filterVariableSuggestions: vi.fn(async () => []),
  });
});

// Create a hoisted mock for useBlockNoteEditor
const mockUseBlockNoteEditor = vi.hoisted(() => {
  return () => ({
    editor: {
      _tiptapEditor: {
        on: vi.fn(),
        off: vi.fn(),
        isFocused: false,
      },
      document: [],
      focus: vi.fn(),
      replaceBlocks: vi.fn(),
      blocksToFullHTML: vi.fn(),
      tryParseHTMLToBlocks: vi.fn().mockResolvedValue([]),
      tryParseMarkdownToBlocks: vi.fn().mockResolvedValue([]),
    },
    editorRef: { current: null },
    handleAutoFocus: vi.fn(),
  });
});

// Create a hoisted mock for useEditorPasteClipboard
const mockUseEditorPasteClipboard = vi.hoisted(() => {
  return () => ({
    handlePasteToEditorEvent: vi.fn(),
  });
});

// Mock all dependencies before importing the component
vi.mock('@mantine/core', () => ({
  MantineProvider: ({ children }) => <div data-testid='mantine-provider'>{children}</div>,
  Box: ({ children, className, ...props }) => (
    <div className={className} data-testid='mantine-box' {...props}>
      {children}
    </div>
  ),
  useMantineTheme: () => ({
    colors: {},
    primaryColor: 'blue',
    colorScheme: 'light',
  }),
}));

vi.mock('@mantine/emotion', () => ({
  MantineEmotionProvider: ({ children }) => (
    <div data-testid='mantine-emotion-provider'>{children}</div>
  ),
  emotionTransform: { type: 'selector-transform' },
  createStyles: () => () => ({
    classes: { editorContainer: 'mock-editor-container' },
    cx: (...classNames) => classNames.filter(Boolean).join(' '),
  }),
}));

vi.mock('@blocknote/react', () => ({
  useCreateBlockNote: vi.fn(),
  FilePanelController: ({ filePanel }) => (
    <div data-testid='file-panel-controller'>{filePanel && React.createElement(filePanel)}</div>
  ),
  LinkToolbarController: ({ linkToolbar }) => (
    <div data-testid='link-toolbar-controller'>
      {linkToolbar &&
        React.createElement(linkToolbar, { title: 'test', url: 'https://example.com' })}
    </div>
  ),
  SideMenuController: ({ sideMenu }) => (
    <div data-testid='side-menu-controller'>{sideMenu && React.createElement(sideMenu, {})}</div>
  ),
  RemoveBlockItem: ({ children }) => <div data-testid='remove-block-item'>{children}</div>,
  DragHandleMenu: ({ children }) => <div data-testid='drag-handle-menu'>{children}</div>,
  SuggestionMenuController: ({ triggerCharacter, getItems }) => (
    <button
      type='button'
      data-testid='suggestion-menu-controller'
      data-trigger-character={triggerCharacter}
      onClick={() => getItems?.('test')}
    >
      Suggestion Menu
    </button>
  ),
  DefaultReactSuggestionItem: {},
}));

vi.mock('@blocknote/shadcn', () => ({
  BlockNoteView: (props) => {
    const {
      formattingToolbar,
      linkToolbar,
      filePanel,
      sideMenu,
      editable,
      onChange,
      children,
      ...restProps
    } = props;
    return (
      <div
        data-testid='blocknote-view'
        className={props.className}
        data-theme={props.theme}
        data-formatting-toolbar={formattingToolbar ? 'true' : 'false'}
        data-link-toolbar={linkToolbar ? 'true' : 'false'}
        data-file-panel={filePanel ? 'true' : 'false'}
        data-side-menu={sideMenu ? 'true' : 'false'}
        data-editable={editable ? 'true' : 'false'}
        aria-multiline='true'
        aria-hidden='false'
        aria-disabled={editable ? 'false' : 'true'}
        data-blocknote-editor=''
        {...restProps}
      >
        <textarea
          data-testid='editor-content'
          defaultValue=''
          onChange={() => onChange?.()}
          onInput={() => onChange?.()}
          onBlur={() => props.onBlur?.()}
          onFocus={() => props.onFocus?.()}
        />
        {children}
      </div>
    );
  },
}));

vi.mock('../utils', () => ({
  validateUploadFile: vi.fn().mockReturnValue(true),
  convertWhiteSpaceToNbspInBlockNote: vi.fn((content) => content),
  normalizeBlockNoteHTML: vi.fn((html) => html),
}));

vi.mock('../../utils/string', () => ({
  convertHTMLToText: vi.fn((html) => 'Plain text'),
  convertBreakLineToHTML: vi.fn((content) => content),
  replaceWhiteSpaceToHTMLNbsp: vi.fn((content) => content),
}));

// Mock the hooks individually - IMPORTANT: Mock the hooks index file
vi.mock('../../hooks', () => ({
  useFilterSuggestions: mockUseFilterSuggestions,
  useEditorCopyClipboard: () => {},
  useEditorPasteClipboard: mockUseEditorPasteClipboard,
  useEditorMediaViewer: vi.fn(),
  useBlockNoteEditor: mockUseBlockNoteEditor,
  // useBlockNoteStyles is imported directly, so it's mocked separately
}));

// Mock @mantine/emotion to control createStyles behavior
vi.mock('@mantine/emotion', () => ({
  MantineEmotionProvider: ({ children }) => (
    <div data-testid='mantine-emotion-provider'>{children}</div>
  ),
  emotionTransform: { type: 'selector-transform' },
  createStyles: vi.fn(() => ({ isBordered = true } = {}) => ({
    classes: {
      editorContainer:
        isBordered === false
          ? 'mock-editor-container unbordered'
          : 'mock-editor-container bordered',
    },
    cx: (...classNames) => classNames.filter(Boolean).join(' '),
  })),
}));

vi.mock('../BlockNoteSchema', () => ({
  default: {},
  createConditionalBlockNoteSchema: vi.fn(() => ({})),
}));
vi.mock('../BlockNoteLocales', () => ({ default: { en: {}, fr: {} } }));
vi.mock('../BlockNoteExtensions', () => ({ default: {} }));

vi.mock('../BlockNoteToolbar', () => ({
  default: () => <div data-testid='block-note-toolbar'>Toolbar</div>,
}));

vi.mock('../CustomComponents', () => ({
  FilePanel: () => <div data-testid='file-panel'>File Panel</div>,
  LinkToolbar: () => <div data-testid='link-toolbar'>Link Toolbar</div>,
  CustomSlashMenu: () => <div data-testid='custom-slash-menu'>Custom Slash Menu</div>,
}));

vi.mock('../BlockNoteEditorRenderer', () => ({
  default: (props) => (
    <div
      data-testid='blocknote-view'
      className={props.className}
      data-theme={props.theme}
      data-formatting-toolbar={props.formattingToolbarEnabled ? 'true' : 'false'}
      data-link-toolbar={props.linkToolbarEnabled ? 'true' : 'false'}
      data-file-panel={props.filePanelEnabled ? 'true' : 'false'}
      data-side-menu={props.sideMenuEnabled ? 'true' : 'false'}
      data-editable={props.editable ? 'true' : 'false'}
      {...props.ariaAttributes}
      {...props.dataAttributes}
    >
      <textarea
        data-testid='editor-content'
        defaultValue=''
        onChange={() => props.onChange?.()}
        onInput={() => props.onChange?.()}
        onBlur={(e) => {
          // Simulate the real editor's onBlur behavior
          if (props.onBlur) {
            props.onBlur();
          }
        }}
        onFocus={(e) => {
          // Simulate the real editor's onFocus behavior
          if (props.onFocus) {
            props.onFocus();
          }
        }}
      />
      {props.formattingToolbarEnabled && (
        <div data-editor-control data-editor-control-formatting-toolbar>
          <div data-testid='block-note-toolbar'>Toolbar</div>
        </div>
      )}
      {props.filePanelEnabled && (
        <div data-editor-control>
          <div data-testid='file-panel-controller'>
            <div data-testid='file-panel'>File Panel</div>
          </div>
        </div>
      )}
      {props.linkToolbarEnabled && (
        <div data-editor-control>
          <div data-testid='link-toolbar-controller'>
            <div data-testid='link-toolbar'>Link Toolbar</div>
          </div>
        </div>
      )}
      {props.suggestionVariableEnabled && (
        <div data-testid='suggestion-menu-controller'>Suggestion Menu</div>
      )}
    </div>
  ),
}));

// Mock the useBlockNoteStyles hook - it's a hook created by createStyles
vi.mock('../../hooks/useBlockNoteStyles', () => ({
  useBlockNoteStyles: ({ isBordered = true } = {}) => ({
    classes: {
      editorContainer:
        isBordered === false
          ? 'mock-editor-container unbordered'
          : 'mock-editor-container bordered',
    },
    cx: (...classNames) => classNames.filter(Boolean).join(' '),
  }),
}));

vi.mock('../BlockNoteThemeProvider', () => ({
  default: ({ children }) => <div data-testid='blocknote-theme-provider'>{children}</div>,
}));

// Mock the editor content utilities
vi.mock('../../utils/editorContent', () => ({
  loadEditorContent: vi.fn(),
  serializeToHTML: vi.fn(),
  filterVariableNodesFromContent: vi.fn((content) => content), // Simplified mock - returns original content
  filterVariableNodesFromBlocks: vi.fn((blocks) => blocks), // Simplified mock
}));

// Import after mocks
import { useCreateBlockNote } from '@blocknote/react';
import { useBlockNoteStyles } from '../../hooks/useBlockNoteStyles';
import {
  filterVariableNodesFromContent,
  loadEditorContent,
  serializeToHTML,
} from '../../utils/editorContent';

// Test constants
const MOCK_EMPTY_HTML_INPUT = '';
const MOCK_EMPTY_HTML_OUTPUT = '<div class="bn-block-group" data-node-type="blockGroup"></div>';

const MOCK_SIMPLE_TEXT_INPUT = '<p>Hello World</p>';
const MOCK_SIMPLE_TEXT_OUTPUT =
  '<div class="bn-block-group" data-node-type="blockGroup"><div class="bn-block-outer" data-node-type="blockOuter"><div class="bn-block" data-node-type="blockContainer"><div class="bn-block-content" data-content-type="paragraph"><p class="bn-inline-content">Hello World</p></div></div></div></div>';

const MOCK_COMPLEX_HTML_INPUT = `
<div>
  <h1>Title</h1>
  <p>Paragraph with <strong>bold</strong> and <em>italic</em> text.</p>
  <ul>
    <li>List item 1</li>
    <li>List item 2</li>
  </ul>
</div>`;

const MOCK_COMPLEX_HTML_OUTPUT =
  '<div class="bn-block-group" data-node-type="blockGroup"><div class="bn-block-outer" data-node-type="blockOuter"><div class="bn-block" data-node-type="blockContainer"><div class="bn-block-content" data-content-type="heading"><h1 class="bn-inline-content">Title</h1></div></div></div><div class="bn-block-outer" data-node-type="blockOuter"><div class="bn-block" data-node-type="blockContainer"><div class="bn-block-content" data-content-type="paragraph"><p class="bn-inline-content">Paragraph with <strong>bold</strong> and <em>italic</em> text.</p></div></div></div><div class="bn-block-outer" data-node-type="blockOuter"><div class="bn-block" data-node-type="blockContainer"><div class="bn-block-content" data-content-type="bulletListItem"><ul class="bn-inline-content"><li>List item 1</li><li>List item 2</li></ul></div></div></div></div>';

describe('BlockNoteEditor', () => {
  let mockEditor: any;
  let mockOnChange: ReturnType<typeof vi.fn>;
  let mockOnBlur: ReturnType<typeof vi.fn>;
  let mockOnFocus: ReturnType<typeof vi.fn>;

  beforeEach(() => {
    vi.clearAllMocks();
    mockOnChange = vi.fn();
    mockOnBlur = vi.fn();
    mockOnFocus = vi.fn();

    // Create a mock editor with all necessary methods
    mockEditor = {
      _tiptapEditor: {
        on: vi.fn(),
        off: vi.fn(),
        isFocused: false,
      },
      document: [],
      focus: vi.fn(),
      replaceBlocks: vi.fn(),
      blocksToFullHTML: vi.fn(),
      tryParseHTMLToBlocks: vi.fn().mockResolvedValue([]),
      tryParseMarkdownToBlocks: vi.fn().mockResolvedValue([]),
    };

    vi.mocked(useCreateBlockNote).mockReturnValue(mockEditor);

    // Mock the editor content utilities as async functions
    vi.mocked(loadEditorContent).mockResolvedValue([]);
    vi.mocked(serializeToHTML).mockResolvedValue({ html: '', plainText: '' });
    vi.mocked(filterVariableNodesFromContent).mockImplementation((content) => content);
  });

  afterEach(() => {
    vi.resetAllMocks();
  });

  describe('Rendering', () => {
    it('should render with default props', () => {
      renderWithMantine(<BlockNoteEditor />);

      const editorElement = screen.getByTestId('blocknote-view');
      expect(editorElement).toBeInTheDocument();
      expect(editorElement).toHaveClass('mock-editor-container bordered');
      expect(editorElement).toHaveAttribute('data-theme', 'light');
      expect(editorElement).toHaveAttribute('data-editable', 'true');
    });

    it('should render with custom className', () => {
      const customClass = 'custom-class';
      renderWithMantine(<BlockNoteEditor className={customClass} />);

      const editorElement = screen.getByTestId('blocknote-view');
      expect(editorElement).toHaveClass('mock-editor-container bordered');
      expect(editorElement).toHaveClass(customClass);
    });

    it('should respect editable prop', () => {
      renderWithMantine(<BlockNoteEditor isEditable={false} />);

      const editorElement = screen.getByTestId('blocknote-view');
      expect(editorElement).toHaveAttribute('data-editable', 'false');
    });

    it('should respect isBordered prop', () => {
      renderWithMantine(<BlockNoteEditor isBordered={false} />);

      const editorElement = screen.getByTestId('blocknote-view');
      expect(editorElement).toHaveClass('mock-editor-container unbordered');
    });

    it('should render custom formatting toolbar when enabled', () => {
      renderWithMantine(<BlockNoteEditor usingCustomFormattingToolbar={true} />);

      expect(screen.getByTestId('blocknote-view')).toHaveAttribute(
        'data-formatting-toolbar',
        'true'
      );
      expect(screen.getByTestId('block-note-toolbar')).toBeInTheDocument();
    });

    it('should use built-in formatting toolbar when custom is disabled', () => {
      renderWithMantine(<BlockNoteEditor usingCustomFormattingToolbar={false} />);

      expect(screen.getByTestId('blocknote-view')).toHaveAttribute(
        'data-formatting-toolbar',
        'false'
      );
      expect(screen.queryByTestId('block-note-toolbar')).not.toBeInTheDocument();
    });

    it('should render custom link toolbar when enabled', () => {
      renderWithMantine(<BlockNoteEditor usingCustomLinkToolbar={true} />);

      expect(screen.getByTestId('blocknote-view')).toHaveAttribute('data-link-toolbar', 'true');
      expect(screen.getByTestId('link-toolbar-controller')).toBeInTheDocument();
    });

    it('should render custom file panel when enabled', () => {
      renderWithMantine(<BlockNoteEditor usingCustomFilePanel={true} />);

      expect(screen.getByTestId('blocknote-view')).toHaveAttribute('data-file-panel', 'true');
      expect(screen.getByTestId('file-panel-controller')).toBeInTheDocument();
    });

    it('should render suggestion menu when enabled', () => {
      renderWithMantine(<BlockNoteEditor usingCustomSuggestionVariable={true} />);

      expect(screen.getByTestId('suggestion-menu-controller')).toBeInTheDocument();
    });
  });

  describe('Props and Configuration', () => {
    it('should set up the editor with correct language configuration', () => {
      renderWithMantine(<BlockNoteEditor language='fr' />);

      expect(useCreateBlockNote).toHaveBeenCalledWith(
        expect.objectContaining({
          dictionary: expect.anything(),
          tables: {
            splitCells: true,
            cellBackgroundColor: true,
            cellTextColor: true,
            headers: true,
          },
        })
      );
    });

    it('should handle file uploads when provided', () => {
      const mockUpload = vi.fn().mockResolvedValue('https://example.com/image.jpg');

      renderWithMantine(<BlockNoteEditor uploadFile={mockUpload} />);

      expect(useCreateBlockNote).toHaveBeenCalledWith(
        expect.objectContaining({
          uploadFile: expect.any(Function),
          tables: {
            splitCells: true,
            cellBackgroundColor: true,
            cellTextColor: true,
            headers: true,
          },
        })
      );
    });

    it('should handle variable suggestions when provided', () => {
      const variableSuggestions = ['var1', 'var2', 'var3'];

      renderWithMantine(<BlockNoteEditor variableSuggestions={variableSuggestions} />);

      expect(useCreateBlockNote).toHaveBeenCalledWith(
        expect.objectContaining({
          // The hook should be called with variable suggestions
        })
      );
    });

    it('should handle usingCustomSuggestionVariable prop', () => {
      renderWithMantine(<BlockNoteEditor usingCustomSuggestionVariable={true} />);

      expect(useCreateBlockNote).toHaveBeenCalledWith(
        expect.objectContaining({
          // The hook should be called with custom suggestion variable enabled
        })
      );
    });

    it('should handle usingCustomSuggestionVariable prop set to false', () => {
      renderWithMantine(<BlockNoteEditor usingCustomSuggestionVariable={false} />);

      expect(useCreateBlockNote).toHaveBeenCalledWith(
        expect.objectContaining({
          // The hook should be called with custom suggestion variable disabled
        })
      );
    });

    it('should handle autoFocus prop correctly', () => {
      renderWithMantine(<BlockNoteEditor autoFocus={false} />);

      expect(useCreateBlockNote).toHaveBeenCalledWith(
        expect.objectContaining({
          // autoFocus should be passed to the hook
          tables: {
            splitCells: true,
            cellBackgroundColor: true,
            cellTextColor: true,
            headers: true,
          },
        })
      );
    });

    it('should enable tables with default configuration', () => {
      renderWithMantine(<BlockNoteEditor />);

      expect(useCreateBlockNote).toHaveBeenCalledWith(
        expect.objectContaining({
          tables: {
            splitCells: true,
            cellBackgroundColor: true,
            cellTextColor: true,
            headers: true,
          },
        })
      );
    });
  });

  describe('Event Handling', () => {
    it('should render with onBlur callback provided', () => {
      renderWithMantine(<BlockNoteEditor onBlur={mockOnBlur} />);

      // Component should render successfully with onBlur callback
      expect(screen.getByTestId('blocknote-view')).toBeInTheDocument();
    });

    it('should render with onFocus callback provided', () => {
      renderWithMantine(<BlockNoteEditor onFocus={mockOnFocus} />);

      // Component should render successfully with onFocus callback
      expect(screen.getByTestId('blocknote-view')).toBeInTheDocument();
    });

    it('should call onChange with correct HTML output when editor content changes', async () => {
      vi.mocked(serializeToHTML).mockResolvedValue({
        html: MOCK_SIMPLE_TEXT_OUTPUT,
        plainText: 'Test plain text',
      });

      renderWithMantine(
        <BlockNoteEditor initialHTML={MOCK_SIMPLE_TEXT_INPUT} onChange={mockOnChange} />
      );

      // Simulate editor content change
      const editorContent = screen.getByTestId('editor-content');
      fireEvent.change(editorContent, { target: { value: 'New content' } });

      // Give a moment for async operations
      await new Promise((resolve) => setTimeout(resolve, 10));

      expect(serializeToHTML).toHaveBeenCalledWith(mockEditor, {
        includeCSSStyles: true,
        customCSSStyles: undefined,
      });
      expect(mockOnChange).toHaveBeenCalledWith(MOCK_SIMPLE_TEXT_OUTPUT, 'Test plain text');
    });

    it('should not call onChange when onChange prop is not provided', async () => {
      vi.mocked(serializeToHTML).mockResolvedValue({
        html: MOCK_SIMPLE_TEXT_OUTPUT,
        plainText: 'Test plain text',
      });

      renderWithMantine(<BlockNoteEditor initialHTML={MOCK_SIMPLE_TEXT_INPUT} />);

      // Simulate editor content change
      const editorContent = screen.getByTestId('editor-content');
      fireEvent.change(editorContent, { target: { value: 'New content' } });

      // Wait a bit for any async operations
      await new Promise((resolve) => setTimeout(resolve, 100));

      // serializeToHTML should not be called when onChange is not provided
      expect(serializeToHTML).not.toHaveBeenCalled();
      // onChange should not be called since it wasn't provided
      expect(mockOnChange).not.toHaveBeenCalled();
    });
  });

  describe('Content Loading and Initialization', () => {
    it('should load empty HTML content correctly', async () => {
      vi.mocked(serializeToHTML).mockResolvedValue({
        html: MOCK_EMPTY_HTML_OUTPUT,
        plainText: '',
      });

      renderWithMantine(
        <BlockNoteEditor initialHTML={MOCK_EMPTY_HTML_INPUT} onChange={mockOnChange} />
      );

      // Give a moment for component to mount and call loadEditorContent
      await new Promise((resolve) => setTimeout(resolve, 10));

      expect(loadEditorContent).toHaveBeenCalledWith(
        mockEditor,
        MOCK_EMPTY_HTML_INPUT,
        false,
        false
      );
    });

    it('should load simple HTML content correctly', async () => {
      vi.mocked(serializeToHTML).mockResolvedValue({
        html: MOCK_SIMPLE_TEXT_OUTPUT,
        plainText: 'Hello World',
      });

      renderWithMantine(
        <BlockNoteEditor initialHTML={MOCK_SIMPLE_TEXT_INPUT} onChange={mockOnChange} />
      );

      // Give a moment for component to mount and call loadEditorContent
      await new Promise((resolve) => setTimeout(resolve, 10));

      expect(loadEditorContent).toHaveBeenCalledWith(
        mockEditor,
        MOCK_SIMPLE_TEXT_INPUT,
        false,
        false
      );
    });

    it('should handle markdown content when isMarkdown is true', async () => {
      const markdownInput = '# Title\n\nThis is **bold** text.';
      vi.mocked(serializeToHTML).mockResolvedValue({
        html: '<div class="bn-block-group" data-node-type="blockGroup"><div class="bn-block-outer" data-node-type="blockOuter"><div class="bn-block" data-node-type="blockContainer"><div class="bn-block-content" data-content-type="heading"><h1 class="bn-inline-content">Title</h1></div></div></div><div class="bn-block-outer" data-node-type="blockOuter"><div class="bn-block" data-node-type="blockContainer"><div class="bn-block-content" data-content-type="paragraph"><p class="bn-inline-content">This is <strong>bold</strong> text.</p></div></div></div></div>',
        plainText: 'Title This is bold text.',
      });

      renderWithMantine(
        <BlockNoteEditor initialHTML={markdownInput} isMarkdown={true} onChange={mockOnChange} />
      );

      // Give a moment for component to mount and call loadEditorContent
      await new Promise((resolve) => setTimeout(resolve, 10));

      expect(loadEditorContent).toHaveBeenCalledWith(mockEditor, markdownInput, true, false);
    });

    it('should not load content when initialHTML is null or undefined', () => {
      // Test with null - this should not load content
      renderWithMantine(<BlockNoteEditor initialHTML={null} onChange={mockOnChange} />);

      expect(loadEditorContent).not.toHaveBeenCalled();
    });
  });

  describe('Content Format Consistency', () => {
    describe('Simple HTML Content', () => {
      it('should maintain consistent format for simple HTML content', async () => {
        vi.mocked(serializeToHTML).mockResolvedValue({
          html: MOCK_SIMPLE_HTML_CONTENT,
          plainText: 'Mock plain text for simple content',
        });

        renderWithMantine(
          <BlockNoteEditor initialHTML={MOCK_SIMPLE_HTML_CONTENT} onChange={mockOnChange} />
        );

        // Give a moment for component to mount and call loadEditorContent
        await new Promise((resolve) => setTimeout(resolve, 10));

        expect(loadEditorContent).toHaveBeenCalledWith(
          mockEditor,
          MOCK_SIMPLE_HTML_CONTENT,
          false,
          false
        );

        // Verify the content structure is maintained
        expect(MOCK_SIMPLE_HTML_CONTENT).toContain('class="bn-block-group"');
        expect(MOCK_SIMPLE_HTML_CONTENT).toContain('data-node-type="blockGroup"');
        expect(MOCK_SIMPLE_HTML_CONTENT).toContain('data-content-type="paragraph"');

        // Verify the editor was rendered
        expect(screen.getByTestId('blocknote-view')).toBeInTheDocument();
      });

      it('should preserve Japanese text content', () => {
        renderWithMantine(
          <BlockNoteEditor initialHTML={MOCK_SIMPLE_HTML_CONTENT} onChange={mockOnChange} />
        );

        // Verify Japanese text is preserved
        expect(MOCK_SIMPLE_HTML_CONTENT).toContain('個人ミーティングID');
        expect(MOCK_SIMPLE_HTML_CONTENT).toContain('Zoomミーティング');
        expect(MOCK_SIMPLE_HTML_CONTENT).toContain('アプリ一覧');
      });
    });

    describe('Media HTML Content', () => {
      it('should maintain consistent format for media HTML content', async () => {
        vi.mocked(serializeToHTML).mockResolvedValue({
          html: MOCK_MEDIA_HTML_CONTENT,
          plainText: 'Mock plain text for media content',
        });

        renderWithMantine(
          <BlockNoteEditor initialHTML={MOCK_MEDIA_HTML_CONTENT} onChange={mockOnChange} />
        );

        // Give a moment for component to mount and call loadEditorContent
        await new Promise((resolve) => setTimeout(resolve, 10));

        expect(loadEditorContent).toHaveBeenCalledWith(
          mockEditor,
          MOCK_MEDIA_HTML_CONTENT,
          false,
          false
        );

        // Verify the content structure is maintained
        expect(MOCK_MEDIA_HTML_CONTENT).toContain('class="bn-block-group"');
        expect(MOCK_MEDIA_HTML_CONTENT).toContain('data-node-type="blockGroup"');
        expect(MOCK_MEDIA_HTML_CONTENT).toContain('data-content-type="video"');
        expect(MOCK_MEDIA_HTML_CONTENT).toContain('data-content-type="image"');

        // Verify the editor was rendered
        expect(screen.getByTestId('blocknote-view')).toBeInTheDocument();
      });

      it('should preserve video content structure', () => {
        renderWithMantine(
          <BlockNoteEditor initialHTML={MOCK_MEDIA_HTML_CONTENT} onChange={mockOnChange} />
        );

        // Verify video content structure is preserved
        expect(MOCK_MEDIA_HTML_CONTENT).toContain('data-content-type="video"');
        expect(MOCK_MEDIA_HTML_CONTENT).toContain(
          'data-url="https://www.youtube.com/watch?v=zvawRiGll4Y"'
        );
        expect(MOCK_MEDIA_HTML_CONTENT).toContain('bn-file-block-content-wrapper');
      });

      it('should preserve image content structure', () => {
        renderWithMantine(
          <BlockNoteEditor initialHTML={MOCK_MEDIA_HTML_CONTENT} onChange={mockOnChange} />
        );

        // Verify image content structure is preserved
        expect(MOCK_MEDIA_HTML_CONTENT).toContain('data-content-type="image"');
        expect(MOCK_MEDIA_HTML_CONTENT).toContain('data-caption="zoom.webp"');
        expect(MOCK_MEDIA_HTML_CONTENT).toContain('bn-visual-media-wrapper');
        expect(MOCK_MEDIA_HTML_CONTENT).toContain('bn-visual-media');
      });
    });

    describe('Rich HTML Content', () => {
      it('should maintain consistent format for rich HTML content', async () => {
        vi.mocked(serializeToHTML).mockResolvedValue({
          html: MOCK_RICH_HTML_CONTENT,
          plainText: 'Mock plain text for rich content',
        });

        renderWithMantine(
          <BlockNoteEditor initialHTML={MOCK_RICH_HTML_CONTENT} onChange={mockOnChange} />
        );

        // Give a moment for component to mount and call loadEditorContent
        await new Promise((resolve) => setTimeout(resolve, 10));

        expect(loadEditorContent).toHaveBeenCalledWith(
          mockEditor,
          MOCK_RICH_HTML_CONTENT,
          false,
          false
        );

        // Verify the content structure is maintained
        expect(MOCK_RICH_HTML_CONTENT).toContain('class="bn-block-group"');
        expect(MOCK_RICH_HTML_CONTENT).toContain('data-node-type="blockGroup"');
        expect(MOCK_RICH_HTML_CONTENT).toContain('data-content-type="heading"');
        expect(MOCK_RICH_HTML_CONTENT).toContain('data-content-type="bulletListItem"');
        expect(MOCK_RICH_HTML_CONTENT).toContain('data-content-type="numberedListItem"');
        expect(MOCK_RICH_HTML_CONTENT).toContain('data-content-type="checkListItem"');

        // Verify the editor was rendered
        expect(screen.getByTestId('blocknote-view')).toBeInTheDocument();
      });

      it('should preserve heading structure and levels', () => {
        renderWithMantine(
          <BlockNoteEditor initialHTML={MOCK_RICH_HTML_CONTENT} onChange={mockOnChange} />
        );

        // Verify heading structure is preserved
        expect(MOCK_RICH_HTML_CONTENT).toContain('data-content-type="heading"');
        expect(MOCK_RICH_HTML_CONTENT).toContain('data-level="1"');
        expect(MOCK_RICH_HTML_CONTENT).toContain('data-level="2"');
        expect(MOCK_RICH_HTML_CONTENT).toContain('data-level="3"');
        expect(MOCK_RICH_HTML_CONTENT).toContain('<h1 class="bn-inline-content">Heading 1</h1>');
        expect(MOCK_RICH_HTML_CONTENT).toContain('<h2 class="bn-inline-content">Heading 2</h2>');
        expect(MOCK_RICH_HTML_CONTENT).toContain('<h3 class="bn-inline-content">Heading 3</h3>');
      });

      it('should preserve text formatting (bold, italic, underline)', () => {
        renderWithMantine(
          <BlockNoteEditor initialHTML={MOCK_RICH_HTML_CONTENT} onChange={mockOnChange} />
        );

        // Verify text formatting is preserved
        expect(MOCK_RICH_HTML_CONTENT).toContain('<strong>Bold Text</strong>');
        expect(MOCK_RICH_HTML_CONTENT).toContain('<em>Italic Text</em>');
        expect(MOCK_RICH_HTML_CONTENT).toContain('<u>Underline Text</u>');
      });

      it('should preserve text colors and background colors', () => {
        renderWithMantine(
          <BlockNoteEditor initialHTML={MOCK_RICH_HTML_CONTENT} onChange={mockOnChange} />
        );

        // Verify text colors and backgrounds are preserved
        expect(MOCK_RICH_HTML_CONTENT).toContain('data-text-color="#228be6"');
        expect(MOCK_RICH_HTML_CONTENT).toContain('data-background-color="gray"');
        expect(MOCK_RICH_HTML_CONTENT).toContain(
          'style="color: #228be6; text-decoration-color: #228be6;"'
        );
      });

      it('should preserve checkbox structure and states', () => {
        renderWithMantine(
          <BlockNoteEditor initialHTML={MOCK_RICH_HTML_CONTENT} onChange={mockOnChange} />
        );

        // Verify checkbox structure is preserved
        expect(MOCK_RICH_HTML_CONTENT).toContain('data-content-type="checkListItem"');
        expect(MOCK_RICH_HTML_CONTENT).toContain('data-checked="true"');
        expect(MOCK_RICH_HTML_CONTENT).toContain('<input type="checkbox" checked="">');
        expect(MOCK_RICH_HTML_CONTENT).toContain('<input type="checkbox">');
      });
    });

    describe('Cross-Content Type Consistency', () => {
      it('should maintain consistent block structure across all content types', () => {
        const contentTypes = [
          MOCK_SIMPLE_HTML_CONTENT,
          MOCK_MEDIA_HTML_CONTENT,
          MOCK_RICH_HTML_CONTENT,
        ];

        contentTypes.forEach((content) => {
          expect(content).toContain('class="bn-block-group"');
          expect(content).toContain('data-node-type="blockGroup"');
          expect(content).toContain('class="bn-block-outer"');
          expect(content).toContain('data-node-type="blockOuter"');
          expect(content).toContain('class="bn-block"');
          expect(content).toContain('data-node-type="blockContainer"');
          expect(content).toContain('class="bn-block-content"');
        });
      });

      it('should preserve data-id attributes consistently', () => {
        const contentTypes = [
          MOCK_SIMPLE_HTML_CONTENT,
          MOCK_MEDIA_HTML_CONTENT,
          MOCK_RICH_HTML_CONTENT,
        ];

        contentTypes.forEach((content) => {
          // Verify UUID format for data-id attributes
          const idMatches = content.match(/data-id="[a-f0-9-]+"/g);
          expect(idMatches).toBeTruthy();
          expect(idMatches!.length).toBeGreaterThan(0);
        });
      });

      it('should maintain consistent class naming conventions', () => {
        const contentTypes = [
          MOCK_SIMPLE_HTML_CONTENT,
          MOCK_MEDIA_HTML_CONTENT,
          MOCK_RICH_HTML_CONTENT,
        ];

        contentTypes.forEach((content) => {
          // Verify consistent class naming
          expect(content).toContain('bn-block-group');
          expect(content).toContain('bn-block-outer');
          expect(content).toContain('bn-block');
          expect(content).toContain('bn-block-content');
          expect(content).toContain('bn-inline-content');
        });
      });
    });
  });

  describe('Edge Cases and Error Handling', () => {
    it('should render component successfully', () => {
      // Test basic component rendering with default props
      renderWithMantine(<BlockNoteEditor />);

      // Component should render successfully
      expect(screen.getByTestId('blocknote-view')).toBeInTheDocument();
    });

    it('should handle empty content loading', async () => {
      // Test with empty content
      vi.mocked(loadEditorContent).mockResolvedValue([]);

      renderWithMantine(<BlockNoteEditor initialHTML='' onChange={mockOnChange} />);

      // Give a moment for component to mount and call loadEditorContent
      await new Promise((resolve) => setTimeout(resolve, 10));

      expect(loadEditorContent).toHaveBeenCalledWith(mockEditor, '', false, false);

      // Component should render successfully
      expect(screen.getByTestId('blocknote-view')).toBeInTheDocument();
    });

    it('should handle very large content', async () => {
      const largeContent = `<p>${'A'.repeat(10000)}</p>`;
      vi.mocked(serializeToHTML).mockResolvedValue({
        html: largeContent,
        plainText: 'A'.repeat(10000),
      });

      renderWithMantine(<BlockNoteEditor initialHTML={largeContent} onChange={mockOnChange} />);

      // Give a moment for component to mount and call loadEditorContent
      await new Promise((resolve) => setTimeout(resolve, 10));

      expect(loadEditorContent).toHaveBeenCalledWith(mockEditor, largeContent, false, false);

      expect(screen.getByTestId('blocknote-view')).toBeInTheDocument();
    });

    it('should handle special characters and encoding', async () => {
      const specialCharsContent = '<p>Special chars: &lt;&gt;&amp;&quot;&#39; éñ中文日本語</p>';
      vi.mocked(serializeToHTML).mockResolvedValue({
        html: specialCharsContent,
        plainText: 'Special chars: <>&\'" éñ中文日本語',
      });

      renderWithMantine(
        <BlockNoteEditor initialHTML={specialCharsContent} onChange={mockOnChange} />
      );

      // Give a moment for component to mount and call loadEditorContent
      await new Promise((resolve) => setTimeout(resolve, 10));

      expect(loadEditorContent).toHaveBeenCalledWith(mockEditor, specialCharsContent, false, false);

      expect(screen.getByTestId('blocknote-view')).toBeInTheDocument();
    });

    it('should handle invalid HTML content gracefully', async () => {
      const invalidHTML = '<p>Unclosed paragraph<div>Nested div</p>';
      vi.mocked(loadEditorContent).mockResolvedValue([]);

      renderWithMantine(<BlockNoteEditor initialHTML={invalidHTML} onChange={mockOnChange} />);

      // Give a moment for component to mount and call loadEditorContent
      await new Promise((resolve) => setTimeout(resolve, 10));

      expect(loadEditorContent).toHaveBeenCalledWith(mockEditor, invalidHTML, false, false);

      expect(screen.getByTestId('blocknote-view')).toBeInTheDocument();
    });
  });

  describe('Accessibility', () => {
    it('should have proper ARIA attributes', () => {
      renderWithMantine(<BlockNoteEditor />);

      const editorElement = screen.getByTestId('blocknote-view');
      expect(editorElement).toHaveAttribute('aria-multiline', 'true');
      expect(editorElement).toHaveAttribute('aria-hidden', 'false');
      expect(editorElement).toHaveAttribute('aria-disabled', 'false');
    });

    it('should have proper ARIA attributes when disabled', () => {
      renderWithMantine(<BlockNoteEditor isEditable={false} />);

      const editorElement = screen.getByTestId('blocknote-view');
      expect(editorElement).toHaveAttribute('aria-disabled', 'true');
    });

    it('should have proper data attributes', () => {
      renderWithMantine(<BlockNoteEditor />);

      const editorElement = screen.getByTestId('blocknote-view');
      expect(editorElement).toHaveAttribute('data-blocknote-editor');
    });
  });

  describe('Performance and Memory', () => {
    it('should handle rapid content changes efficiently', async () => {
      vi.mocked(serializeToHTML).mockResolvedValue({
        html: '<p>Updated content</p>',
        plainText: 'Updated content',
      });

      renderWithMantine(<BlockNoteEditor onChange={mockOnChange} />);

      const editorContent = screen.getByTestId('editor-content');

      // Simulate rapid changes
      for (let i = 0; i < 5; i++) {
        fireEvent.change(editorContent, { target: { value: `Content ${i}` } });
      }

      // Give a moment for serialization to be called
      await new Promise((resolve) => setTimeout(resolve, 10));

      expect(serializeToHTML).toHaveBeenCalled();

      // Should handle multiple rapid changes without errors
      expect(screen.getByTestId('blocknote-view')).toBeInTheDocument();
    });

    it('should not cause memory leaks with frequent re-renders', () => {
      const { rerender } = renderWithMantine(<BlockNoteEditor />);

      // Simulate frequent re-renders with proper component structure
      for (let i = 0; i < 10; i++) {
        rerender(<BlockNoteEditor key={i} onChange={vi.fn()} />);
      }

      expect(screen.getByTestId('blocknote-view')).toBeInTheDocument();
    });
  });
});
