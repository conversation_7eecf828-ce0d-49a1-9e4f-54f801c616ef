import { beforeEach, describe, expect, it, vi } from 'vitest';

import { defaultStyleSpecs } from '@blocknote/core';
import { customizeDefaultStyleSpecs } from '../../utils/content';
import {
  CustomBlockNoteSchema,
  MarkdownBlockSchema,
  createConditionalBlockNoteSchema,
  createConditionalMarkdownBlockSchema,
} from '../BlockNoteSchema';
import { ReactImageBlock, ReactVideoBlock } from '../CustomBlocks';
import { Variable } from '../InlineContentSpecs';

// Reset mocks between tests to ensure test isolation
beforeEach(() => {
  vi.resetAllMocks();
  vi.resetModules();
});

describe('CustomBlockNoteSchema', () => {
  it('should include custom video and image block specs', () => {
    expect(CustomBlockNoteSchema.blockSpecs.video).toBe(ReactVideoBlock);
    expect(CustomBlockNoteSchema.blockSpecs.image).toBe(ReactImageBlock);
  });

  it('should include customized style specs', () => {
    const expectedStyleSpecs = customizeDefaultStyleSpecs({
      ...defaultStyleSpecs,
    });

    // Validate that customized style specs are applied (spot-check a couple of keys)
    expect(CustomBlockNoteSchema.styleSpecs.backgroundColor).toBeDefined();
    expect(
      typeof (CustomBlockNoteSchema.styleSpecs.backgroundColor as any).implementation.mark.config
        .renderHTML
    ).toBe('function');

    // Ensure the customization utility produces equivalent result for backgroundColor & textColor
    expect(CustomBlockNoteSchema.styleSpecs.backgroundColor).toMatchObject(
      expectedStyleSpecs.backgroundColor as object
    );
    expect(CustomBlockNoteSchema.styleSpecs.textColor).toMatchObject(
      expectedStyleSpecs.textColor as object
    );
  });

  it('should include default inline content specs', () => {
    // CustomBlockNoteSchema should include all default inline content specs
    expect(CustomBlockNoteSchema.inlineContentSpecs).toBeDefined();
    expect(Object.keys(CustomBlockNoteSchema.inlineContentSpecs).length).toBeGreaterThan(0);
  });
});

describe('MarkdownBlockSchema', () => {
  it('should exclude multimedia and advanced block specs', () => {
    const { blockSpecs } = MarkdownBlockSchema;
    const excludedBlocks = [
      'video',
      'audio',
      'file',
      'image',
      'table',
      'quote',
      'toggleListItem',
      'codeBlock',
    ];

    excludedBlocks.forEach((block) => {
      // eslint-disable-next-line vitest/no-conditional-expect
      expect(blockSpecs).not.toHaveProperty(block);
    });
  });

  it('should exclude certain style specs', () => {
    const { styleSpecs } = MarkdownBlockSchema;
    const excludedStyles = ['backgroundColor', 'textColor', 'underline', 'code'];

    excludedStyles.forEach((style) => {
      // eslint-disable-next-line vitest/no-conditional-expect
      expect(styleSpecs).not.toHaveProperty(style);
    });
  });

  it('should include Variable inline content spec', () => {
    expect(MarkdownBlockSchema.inlineContentSpecs.variable).toBe(Variable);
  });
});

describe('createConditionalBlockNoteSchema', () => {
  it('should include Variable inline content spec when enableVariableSuggestions is true', () => {
    const schema = createConditionalBlockNoteSchema(true);
    expect(schema.inlineContentSpecs.variable).toBe(Variable);
  });

  it('should not include Variable inline content spec when enableVariableSuggestions is false', () => {
    const schema = createConditionalBlockNoteSchema(false);
    expect(schema.inlineContentSpecs.variable).toBeUndefined();
  });

  it('should default to including Variable inline content spec when no parameter is provided', () => {
    const schema = createConditionalBlockNoteSchema();
    expect(schema.inlineContentSpecs.variable).toBe(Variable);
  });

  it('should include all other inline content specs regardless of Variable support', () => {
    const schemaWithVariable = createConditionalBlockNoteSchema(true);
    const schemaWithoutVariable = createConditionalBlockNoteSchema(false);

    // Both schemas should have the same base inline content specs (excluding variable)
    const baseSpecs = Object.keys(schemaWithVariable.inlineContentSpecs).filter(
      (key) => key !== 'variable'
    );
    const baseSpecsWithoutVariable = Object.keys(schemaWithoutVariable.inlineContentSpecs);

    expect(baseSpecs).toEqual(baseSpecsWithoutVariable);
  });

  it('should include custom video and image block specs', () => {
    const schema = createConditionalBlockNoteSchema(true);
    expect(schema.blockSpecs.video).toBe(ReactVideoBlock);
    expect(schema.blockSpecs.image).toBe(ReactImageBlock);
  });
});

describe('createConditionalMarkdownBlockSchema', () => {
  it('should include Variable inline content spec when enableVariableSuggestions is true', () => {
    const schema = createConditionalMarkdownBlockSchema(true);
    expect(schema.inlineContentSpecs.variable).toBe(Variable);
  });

  it('should not include Variable inline content spec when enableVariableSuggestions is false', () => {
    const schema = createConditionalMarkdownBlockSchema(false);
    expect(schema.inlineContentSpecs.variable).toBeUndefined();
  });

  it('should default to including Variable inline content spec when no parameter is provided', () => {
    const schema = createConditionalMarkdownBlockSchema();
    expect(schema.inlineContentSpecs.variable).toBe(Variable);
  });

  it('should exclude multimedia and advanced block specs', () => {
    const schema = createConditionalMarkdownBlockSchema(true);
    const excludedBlocks = [
      'video',
      'audio',
      'file',
      'image',
      'table',
      'quote',
      'toggleListItem',
      'codeBlock',
    ];

    excludedBlocks.forEach((block) => {
      // eslint-disable-next-line vitest/no-conditional-expect
      expect(schema.blockSpecs).not.toHaveProperty(block);
    });
  });

  it('should exclude certain style specs', () => {
    const schema = createConditionalMarkdownBlockSchema(true);
    const excludedStyles = ['backgroundColor', 'textColor', 'underline', 'code'];

    excludedStyles.forEach((style) => {
      // eslint-disable-next-line vitest/no-conditional-expect
      expect(schema.styleSpecs).not.toHaveProperty(style);
    });
  });

  it('should include all other inline content specs regardless of Variable support', () => {
    const schemaWithVariable = createConditionalMarkdownBlockSchema(true);
    const schemaWithoutVariable = createConditionalMarkdownBlockSchema(false);

    // Both schemas should have the same base inline content specs (excluding variable)
    const baseSpecs = Object.keys(schemaWithVariable.inlineContentSpecs).filter(
      (key) => key !== 'variable'
    );
    const baseSpecsWithoutVariable = Object.keys(schemaWithoutVariable.inlineContentSpecs);

    expect(baseSpecs).toEqual(baseSpecsWithoutVariable);
  });

  it('should include inlineImage spec in both variable configurations', () => {
    const schemaWithVariable = createConditionalMarkdownBlockSchema(true);
    const schemaWithoutVariable = createConditionalMarkdownBlockSchema(false);

    expect(schemaWithVariable.inlineContentSpecs.inlineImage).toBeDefined();
    expect(schemaWithoutVariable.inlineContentSpecs.inlineImage).toBeDefined();
  });
});
