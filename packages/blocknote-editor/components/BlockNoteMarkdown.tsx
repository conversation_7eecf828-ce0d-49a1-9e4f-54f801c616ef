import { type BlockNoteEditor, filterSuggestionItems } from '@blocknote/core';
import {
  type DefaultReactSuggestionItem,
  SuggestionMenuController,
  getDefaultReactSlashMenuItems,
} from '@blocknote/react';
import { Box } from '@mantine/core';
import isNil from 'lodash/isNil';
import type React from 'react';
import { memo, useCallback, useEffect, useMemo, useState } from 'react';
import { IGNORED_SLASH_MENU_ITEMS_FROM_MARKDOWN } from '../constants';
import { useBlockNoteStyles, useFilterSuggestions } from '../hooks';
import { useBlockNoteEditor } from '../hooks/useBlockNoteEditor';
import type { MarkdownEditorProps } from '../types/blocknote';
import {
  filterVariableNodesFromContent,
  loadEditorContent,
  processBlocksForMarkdown,
  serializeToMarkdown,
} from '../utils/editorContent';
import BlockNoteEditor<PERSON>enderer from './BlockNoteEditorRenderer';
import BlockNoteLocales from './BlockNoteLocales';
import { createConditionalMarkdownBlockSchema } from './BlockNoteSchema';
import BlockNoteThemeProvider from './BlockNoteThemeProvider';

import '@blocknote/core/fonts/inter.css';
import '@blocknote/shadcn/style.css';

/**
 * BlockNoteMarkdown - A markdown editor component built on top of BlockNote
 *
 * @important CSS Import Required: You must import the CSS file in your main application file:
 * ```tsx
 * import '@resola-ai/blocknote-editor/styles.css';
 * ```
 *
 * @param props - MarkdownEditorProps containing editor configuration
 * @returns React component for the BlockNote markdown editor
 */
const BlockNoteMarkdown: React.FC<MarkdownEditorProps> = (props) => {
  const {
    className,
    initialMarkdown = '',
    isBordered = true,
    isEditable = true,
    usingCustomFormattingToolbar = true,
    usingCustomLinkToolbar = true,
    usingCustomFilePanel = false,
    usingCustomSuggestionVariable = false,
    variableSuggestions = [],
    language = 'en',
    onChange,
    onBlur,
    onFocus,
    autoFocus,
  } = props;

  // Track loading and error states
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const dictionary = useMemo(() => BlockNoteLocales[language], [language]);

  const { filterVariableSuggestions } = useFilterSuggestions({
    variableSuggestions,
  });

  // Create conditional schema based on variable suggestions support
  const schema = createConditionalMarkdownBlockSchema(usingCustomSuggestionVariable);

  // Use the shared editor hook
  const { editor, editorRef, handleAutoFocus } = useBlockNoteEditor({
    schema,
    language,
    autoFocus,
    onBlur,
    onFocus,
  });

  const { classes, cx } = useBlockNoteStyles({ isBordered });

  const getCustomSlashMenuItems = useCallback(
    (editor: BlockNoteEditor): DefaultReactSuggestionItem[] => {
      try {
        return getDefaultReactSlashMenuItems(editor).filter(
          (item) => !IGNORED_SLASH_MENU_ITEMS_FROM_MARKDOWN.includes((item as any).key)
        );
      } catch (err) {
        console.warn('Failed to get custom slash menu items:', err);
        return [];
      }
    },
    []
  );

  const customSlashMenu = useMemo(() => {
    if (!editor) return null;

    return (
      <SuggestionMenuController
        triggerCharacter={'/'}
        // Replaces the default Slash Menu items with our custom ones.
        getItems={async (query) => {
          try {
            return filterSuggestionItems(getCustomSlashMenuItems(editor), query);
          } catch (err) {
            console.warn('Failed to filter suggestion items:', err);
            return [];
          }
        }}
      />
    );
  }, [editor, getCustomSlashMenuItems]);

  /**
   * Load initial Markdown content to the editor
   * @param markdownContent Markdown content to load
   * @returns void
   */
  const loadInitialMarkdown = useCallback(
    async (markdownContent: string) => {
      if (!editor || !markdownContent.trim()) {
        return;
      }

      try {
        setIsLoading(true);
        setError(null);

        // Filter Variable nodes if the schema doesn't support them
        const filteredContent = filterVariableNodesFromContent(
          markdownContent,
          usingCustomSuggestionVariable
        );

        // Use shared content loading utility
        const blocks = await loadEditorContent(
          editor,
          filteredContent,
          true,
          usingCustomSuggestionVariable
        );

        // Process blocks for markdown
        const parsedBlocks = processBlocksForMarkdown(blocks);

        editor.replaceBlocks(editor.document, parsedBlocks);

        if (autoFocus) {
          handleAutoFocus();
        }
      } catch (err) {
        const errorMessage = err instanceof Error ? err.message : 'Failed to load markdown content';
        setError(errorMessage);
        console.error('Error loading initial markdown:', err);
      } finally {
        setIsLoading(false);
      }
    },
    [editor, autoFocus, handleAutoFocus, usingCustomSuggestionVariable]
  );

  /**
   * Handle editor change event
   * @returns void
   */
  const handleEditorChange = useCallback(async () => {
    if (!editor || !onChange) {
      return;
    }

    try {
      // Use shared serialization utility
      const finalMarkdown = await serializeToMarkdown(editor);
      onChange(finalMarkdown);
    } catch (err) {
      console.error('Error serializing markdown:', err);
      // Don't update error state for onChange events to avoid disrupting user flow
    }
  }, [editor, onChange]);

  /**
   * Load initial Markdown content on mount
   * @returns void
   */
  useEffect(() => {
    if (!isNil(initialMarkdown) && initialMarkdown.trim()) {
      loadInitialMarkdown(initialMarkdown);
    }
  }, [initialMarkdown, loadInitialMarkdown]);

  // Data attributes for the editor
  const dataAttributes = useMemo(
    () => ({
      'data-blocknote-editor': '',
      'data-loading': isLoading.toString(),
      'data-error': error ? 'true' : 'false',
    }),
    [isLoading, error]
  );

  // Memoize variable suggestion handler to prevent unnecessary re-renders
  const variableSuggestionHandler = useCallback(
    async (query: string) => {
      if (!usingCustomSuggestionVariable || !editor) {
        return [];
      }
      try {
        return await filterVariableSuggestions(editor, query);
      } catch (err) {
        console.warn('Failed to filter variable suggestions:', err);
        return [];
      }
    },
    [usingCustomSuggestionVariable, editor, filterVariableSuggestions]
  );

  if (error) {
    console.error('BlockNoteMarkdown error:', error);
  }

  return (
    <BlockNoteThemeProvider>
      <Box>
        <BlockNoteEditorRenderer
          ref={editorRef}
          editor={editor}
          className={cx(classes.editorContainer, classes.markdownEditor, className)}
          editable={isEditable && !isLoading}
          onChange={handleEditorChange}
          theme='light'
          initialVariables={variableSuggestions}
          formattingToolbarEnabled={usingCustomFormattingToolbar}
          linkToolbarEnabled={usingCustomLinkToolbar}
          filePanelEnabled={usingCustomFilePanel}
          sideMenuEnabled={true}
          suggestionVariableEnabled={usingCustomSuggestionVariable}
          variableSuggestionHandler={variableSuggestionHandler}
          dictionary={dictionary}
          dataAttributes={dataAttributes}
          enabledNestedBlock={false}
          enabledTextAlignment={false}
          customSlashMenu={customSlashMenu}
        />
      </Box>
    </BlockNoteThemeProvider>
  );
};

export default memo(BlockNoteMarkdown);
