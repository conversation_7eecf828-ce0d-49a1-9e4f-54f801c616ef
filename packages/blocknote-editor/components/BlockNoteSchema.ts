import {
  BlockNoteSchema,
  defaultBlockSpecs,
  defaultInlineContentSpecs,
  defaultStyleSpecs,
} from '@blocknote/core';
import omit from 'lodash/omit';
import { customizeDefaultStyleSpecs } from '../utils';
import { ReactImageBlock, ReactVideoBlock } from './CustomBlocks';
import { InlineImageSpec, Variable } from './InlineContentSpecs';

/**
 * Create Custom BlockNote Schema to overide default block (Video, ...)
 * @returns {BlockNoteSchema}
 */
export const CustomBlockNoteSchema = BlockNoteSchema.create({
  blockSpecs: {
    ...defaultBlockSpecs,
    video: ReactVideoBlock,
    image: ReactImageBlock,
    // table: ReactTableWithImageSupport, // Temporarily disable custom table
  },
  styleSpecs: {
    ...customizeDefaultStyleSpecs(defaultStyleSpecs),
  },
  inlineContentSpecs: {
    ...defaultInlineContentSpecs,
    variable: Variable,
    inlineImage: InlineImageSpec,
  },
});

/**
 * Create Markdown BlockNote Schema to overide default block (Video, Table)
 * @returns {BlockNoteSchema}
 */
export const MarkdownBlockSchema = BlockNoteSchema.create({
  blockSpecs: {
    ...omit(defaultBlockSpecs, [
      'video',
      'audio',
      'file',
      'image',
      'table',
      'quote',
      'toggleListItem',
      'codeBlock',
    ]),
  },
  styleSpecs: {
    ...omit(defaultStyleSpecs, ['backgroundColor', 'textColor', 'underline', 'code']),
  },
  inlineContentSpecs: {
    ...defaultInlineContentSpecs,
    variable: Variable,
    inlineImage: InlineImageSpec,
  },
});

/**
 * Create a conditional BlockNote Schema based on whether variable suggestions are enabled
 * @param enableVariableSuggestions - Whether to include Variable inline content spec
 * @returns BlockNoteSchema with or without Variable support
 */
export const createConditionalBlockNoteSchema = (enableVariableSuggestions = true) => {
  const baseInlineContentSpecs = {
    ...defaultInlineContentSpecs,
    inlineImage: InlineImageSpec,
  };

  // Add Variable spec only if variable suggestions are enabled
  const inlineContentSpecs = enableVariableSuggestions
    ? {
        ...baseInlineContentSpecs,
        variable: Variable,
      }
    : baseInlineContentSpecs;

  return BlockNoteSchema.create({
    blockSpecs: {
      ...defaultBlockSpecs,
      video: ReactVideoBlock,
      image: ReactImageBlock,
      // table: ReactTableWithImageSupport, // Temporarily disable custom table
    },
    styleSpecs: {
      ...customizeDefaultStyleSpecs(defaultStyleSpecs),
    },
    inlineContentSpecs,
  });
};

/**
 * Create a conditional Markdown BlockNote Schema based on whether variable suggestions are enabled
 * @param enableVariableSuggestions - Whether to include Variable inline content spec
 * @returns Markdown BlockNoteSchema with or without Variable support
 */
export const createConditionalMarkdownBlockSchema = (enableVariableSuggestions = true) => {
  const baseInlineContentSpecs = {
    ...defaultInlineContentSpecs,
    inlineImage: InlineImageSpec,
  };

  // Add Variable spec only if variable suggestions are enabled
  const inlineContentSpecs = enableVariableSuggestions
    ? {
        ...baseInlineContentSpecs,
        variable: Variable,
      }
    : baseInlineContentSpecs;

  return BlockNoteSchema.create({
    blockSpecs: {
      ...omit(defaultBlockSpecs, [
        'video',
        'audio',
        'file',
        'image',
        'table',
        'quote',
        'toggleListItem',
        'codeBlock',
      ]),
    },
    styleSpecs: {
      ...omit(defaultStyleSpecs, ['backgroundColor', 'textColor', 'underline', 'code']),
    },
    inlineContentSpecs,
  });
};

export default CustomBlockNoteSchema;
