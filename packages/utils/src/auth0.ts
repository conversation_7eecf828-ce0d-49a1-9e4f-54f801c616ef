/**
 * Opens an Auth0 popup window for OAuth2 authentication
 * @param auth0Data - The Auth0 data containing redirectUrl
 * @throws Error if popup is blocked
 */
export const openAuth0Popup = (auth0Data: { redirectUrl?: string }): void => {
  if (auth0Data?.redirectUrl) {
    // Open popup window with specific dimensions
    const popup = window.open(
      auth0Data.redirectUrl,
      'auth0-popup',
      'width=500,height=600,scrollbars=yes,resizable=yes,status=yes,location=yes,toolbar=no,menubar=no'
    );

    if (!popup) {
      // Fallback if popup was blocked
      throw new Error('Popup blocked');
    }
  }
};
