import { AppConfig } from '@/configs';
import type { FormSection } from '@/types';
import { ActionIcon, Flex, LoadingOverlay, SegmentedControl, Select, rem } from '@mantine/core';
import { createStyles } from '@mantine/emotion';
import { useDisclosure } from '@mantine/hooks';
import { DecaButton } from '@resola-ai/ui';
import { usePathParams } from '@resola-ai/ui/hooks';
import { IconDeviceDesktop, IconDeviceMobile, IconHome, IconX } from '@tabler/icons-react';
import clsx from 'clsx';
import { useEffect, useRef, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { Link } from 'react-router-dom';

const useStyles = createStyles((theme) => ({
  sharePreviewSection: {
    display: 'flex',
    flexDirection: 'column',
    height: '100%',
    width: '100%',
    flex: 1,
    borderRadius: 8,
    position: 'relative',
  },
  screenActions: {
    position: 'absolute',
    top: rem(12),
    right: rem(12),
    backgroundColor: '#fff',
    border: '1px solid',
    borderColor: theme.colors.decaLight[2],
    borderRadius: rem(4),
    width: rem(72),
  },
  previewSection: {
    height: '100%',
    width: '100%',
    margin: 'auto',
  },
  previewType: {
    width: rem(35),
    height: '100%',
    padding: rem(10),
    opacity: 0.5,
    cursor: 'pointer',
    '&.active': {
      color: theme.colors.decaNavy[5],
      opacity: 1,
    },
    '&:last-child': {
      borderLeft: '1px solid',
      borderLeftColor: theme.colors.decaLight[2],
    },
  },
  fullPagePreview: {
    display: 'flex',
    flexDirection: 'column',
    height: '100%',
    width: '100%',
    flex: 1,
    backgroundColor: '#fff',
    overflow: 'hidden',
  },
  header: {
    padding: rem(10),
    borderBottom: `1px solid ${theme.colors.gray[2]}`,
  },
  selectMenu: {
    marginLeft: rem(20),
  },
  flexSegment: {
    width: '30%',
  },
  exitBtn: {
    span: {
      marginBottom: rem(1),
    },
    svg: {
      marginRight: rem(5),
    },
  },
  previewTypeSlider: {
    borderRadius: rem(8),
    '.mantine-SegmentedControl-indicator, .mantine-SegmentedControl-control': {
      borderRadius: rem(8),
    },
    label: {
      paddingLeft: rem(30),
      paddingRight: rem(30),
      borderRadius: rem(8),
      '&[data-active]': {
        color: theme.colors.decaNavy[4],
      },
    },
  },
}));

type Props = {
  url: string;
  headerType?: 1 | 2;
  pages?: FormSection[] | undefined;
  onClose?: () => void;
  embedMode?: boolean;
  type?: number;
};

const PreviewPage = ({
  url,
  headerType = 1,
  pages,
  onClose,
  embedMode = false,
  type = 1,
}: Props) => {
  const { classes } = useStyles();
  const [previewType, setPreviewType] = useState<number>(1);
  const iframeRef = useRef<HTMLIFrameElement>(null);
  const pathParams = usePathParams();
  const fullPath = pathParams.createPathWithLngParam('');
  const { origin } = new URL(AppConfig.FORM_PREVIEW_URL as string);

  const selectPageList = pages?.filter((page) => !!page.name && !page.logics) || [];
  const [selectedPage, setSelectedPage] = useState<string>(selectPageList?.[0]?.id || '');
  const [visible, { open, close }] = useDisclosure(false);
  const { t } = useTranslation('form_builder');

  // DEBUG: Initial component setup
  console.log('[DEBUG Preview] Component initialized:', {
    embedMode,
    headerType,
    type,
    url,
    origin,
    previewURL: AppConfig.FORM_PREVIEW_URL,
    currentLocation: window.location.href,
    environment: process.env.NODE_ENV,
    pagesCount: pages?.length,
    selectPageListCount: selectPageList.length,
    firstPageId: pages?.[0]?.id,
  });

  useEffect(() => {
    console.log('[DEBUG Preview] Preview type changed:', type);
    setPreviewType(type);
  }, [type]);

  useEffect(() => {
    if (embedMode) {
      console.log('[DEBUG Preview] EmbedMode - opening loading overlay', {
        url,
        timestamp: Date.now(),
      });
      open();

      // Safety timeout to unfreeze UI
      const timeout = setTimeout(() => {
        console.error('[DEBUG Preview] ⚠️ TIMEOUT after 5s - force closing overlay');
        console.error('[DEBUG Preview] iframe probably crashed, has different origin, or loadSuccess not sent');
        close();
      }, 5000);

      return () => {
        console.log('[DEBUG Preview] Cleanup timeout');
        clearTimeout(timeout);
      };
    }
  }, [url, embedMode, open, close]);

  useEffect(() => {
    let messageCount = 0;

    const handleMessage = (event: MessageEvent) => {
      messageCount++;
      console.log(`[DEBUG Preview] 📨 Message #${messageCount} received:`, {
        data: event.data,
        origin: event.origin,
        expectedOrigin: origin,
        originMatches: event.origin === origin,
        timestamp: Date.now(),
      });

      if (event.data === 'loadSuccess' && embedMode) {
        console.log('[DEBUG Preview] ✅ loadSuccess received - closing overlay');
        close();
      }
      
      if (event.origin !== origin) {
        console.log('[DEBUG Preview] ⚠️ Origin mismatch - ignoring message');
        return;
      }
      
      if (event.data.pageChange) {
        const pageIndex = event.data.pageChange.page;
        const pageId = pages?.[pageIndex].id || '';
        console.log('[DEBUG Preview] Page change requested:', { pageIndex, pageId });
        setSelectedPage(pageId);
      }
    };

    console.log('[DEBUG Preview] Adding message listener, expecting origin:', origin);
    window.addEventListener('message', handleMessage);

    return () => {
      console.log('[DEBUG Preview] Removing message listener, received', messageCount, 'messages total');
      window.removeEventListener('message', handleMessage);
    };
  }, [origin, embedMode, pages, close]);

  const renderHeaderType1 = () => {
    return (
      <Flex align={'center'} className={classes.screenActions}>
        <IconDeviceDesktop
          size={16}
          className={clsx(classes.previewType, { active: previewType === 1 })}
          onClick={() => setPreviewType(1)}
        />
        <IconDeviceMobile
          size={16}
          className={clsx(classes.previewType, { active: previewType === 2 })}
          onClick={() => setPreviewType(2)}
        />
      </Flex>
    );
  };

  const renderHeaderType2 = () => {
    const options = selectPageList.map((page) => ({ value: page.id, label: page.name })) || [];

    return (
      <Flex justify='space-between' align='center' className={classes.header}>
        <Flex className={classes.flexSegment}>
          <Link to={fullPath}>
            <ActionIcon size='lg' radius='xl' variant='filled' color='decaNavy.0' c='decaNavy.5'>
              <IconHome size={17} />
            </ActionIcon>
          </Link>
          <Select
            data={options}
            comboboxProps={{ withinPortal: false }}
            withScrollArea={false}
            styles={{ dropdown: { maxHeight: 200, overflowY: 'auto' } }}
            value={selectedPage}
            className={classes.selectMenu}
            onChange={(value) => handleChangeSelectedPage(value || '')}
          />
        </Flex>
        <Flex align={'center'}>
          <SegmentedControl
            className={classes.previewTypeSlider}
            value={previewType.toString()}
            onChange={(value) => setPreviewType(Number.parseInt(value))}
            data={[
              { label: t('preview.desktop'), value: '1' },
              { label: t('preview.mobile'), value: '2' },
            ]}
          />
        </Flex>
        <Flex justify='flex-end' align='center' className={classes.flexSegment}>
          <DecaButton
            radius='xl'
            fz='sm'
            onClick={() => onClose?.()}
            leftSection={<IconX size={16} />}
          >
            {t('preview.exit')}
          </DecaButton>
        </Flex>
      </Flex>
    );
  };

  const handleChangeSelectedPage = (value: string) => {
    console.log('[DEBUG Preview] handleChangeSelectedPage:', value);
    setSelectedPage(value);
    sendDataToIframe({ eventType: 'pageChange', payload: value });
  };

  const sendDataToIframe = (data: Record<string, any>) => {
    console.log('[DEBUG Preview] sendDataToIframe:', {
      data,
      targetOrigin: origin,
      iframeExists: !!iframeRef?.current,
      contentWindowExists: !!iframeRef?.current?.contentWindow,
    });
    
    if (iframeRef?.current) {
      iframeRef?.current?.contentWindow?.postMessage(data, origin);
    } else {
      console.error('[DEBUG Preview] Cannot send message - iframe ref not available');
    }
  };

  return (
    <div className={headerType === 1 ? classes.sharePreviewSection : classes.fullPagePreview}>
      <LoadingOverlay visible={visible} overlayProps={{ blur: 2 }} />
      {embedMode ? null : headerType === 1 ? renderHeaderType1() : renderHeaderType2()}
      <iframe
        title='Preview iframe'
        ref={iframeRef}
        src={url}
        className={classes.previewSection}
        style={{ maxWidth: previewType === 2 ? 360 : '100%' }}
        onLoad={() => {
          console.log('[DEBUG Preview] ✅ iframe onLoad fired', {
            src: iframeRef.current?.src,
            contentWindow: !!iframeRef.current?.contentWindow,
            timestamp: Date.now(),
          });
        }}
        onError={(e) => {
          console.error('[DEBUG Preview] ❌ iframe onError:', e);
          console.error('[DEBUG Preview] Force closing overlay due to iframe error');
          close();
        }}
      />
    </div>
  );
};

export default PreviewPage;
