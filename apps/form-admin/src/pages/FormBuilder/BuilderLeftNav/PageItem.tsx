import {
  DUPLICATE_SECTION,
  REMOVE_SECTION,
  SET_ACTIVE_SECTION,
  UPDATE_SECTION,
} from '@/store/action/actionTypes';
import { setIsDragging } from '@/store/action/builder';
import { useAppDispatch } from '@/store/hooks';
import type { FormSection } from '@/types/form-builder';
import { Checkbox, Flex, Text } from '@mantine/core';
import { modals } from '@mantine/modals';
import { cleanBadMarkdownContent } from '@resola-ai/ui/utils/string';
import { clsx } from 'clsx';
import { useEffect, useRef, useState } from 'react';
import { useDrag, useDrop } from 'react-dnd';
import { useTranslation } from 'react-i18next';
import { useListPageStyles } from './ListPage';
import PageActions from './PageActions';
import { getLocalStorage, setLocalStorage } from '@/utils/localStorage';
interface PageItemProps {
  isFullMode: boolean;
  page: FormSection;
  isActive: boolean;
  index: number;
  isFormContainLogic: boolean;
  visibleIndex: number;
  ableToDelete: boolean;
  moveNavItem?: (dragIndex: number, hoverIndex: number) => void;
}

const ItemType = 'NAV_ITEM';
const DO_NOT_SHOW_AGAIN_KEY = 'CONFIRM_MOVE_PAGE_DO_NOT_SHOW_AGAIN';

export default function PageItem({
  isFullMode,
  page,
  isActive,
  index,
  isFormContainLogic, // check if the form contains logic
  visibleIndex,
  ableToDelete,
  moveNavItem,
}: PageItemProps) {
  const { classes } = useListPageStyles();
  const dispatch = useAppDispatch();
  const [isRenamingPage, setIsRenamingPage] = useState(false);
  const [renamingInputValue, setRenamingInputValue] = useState(
    cleanBadMarkdownContent(page.name || '')
  );
  const [isHoveredOver, setIsHoveredOver] = useState(false);
  const ref = useRef(null);
  const { t } = useTranslation(['form_builder', 'common']);

  const enableRenamingPage = () => {
    setIsRenamingPage(true);
  };

  const renameSection = () => {
    if (cleanBadMarkdownContent(renamingInputValue?.trim() || '')) {
      dispatch({
        type: UPDATE_SECTION,
        payload: { id: page.id, name: cleanBadMarkdownContent(renamingInputValue.trim()) },
      });
    }
    setIsRenamingPage(false);
  };

  const onNameChange = (e) => {
    setRenamingInputValue(e?.target?.value);
  };

  useEffect(() => {
    const handleMouseDown = (e) => {
      if (isRenamingPage && !(e?.target as Element)?.closest(`.${classes.editingInput}`)) {
        renameSection();
      }
    };

    document.addEventListener('mousedown', handleMouseDown);

    return () => {
      document.removeEventListener('mousedown', handleMouseDown);
    };
  }, [isRenamingPage, renamingInputValue]);

  const showConfirmModal = (pageId) => {
    modals.openConfirmModal({
      title: t('deletePage.title'),
      children: t('deletePage.content', { name: page.name }),
      labels: { confirm: t('deletePage.confirm'), cancel: t('deletePage.cancel') },
      confirmProps: { color: 'red' },
      onConfirm: () => dispatch({ type: REMOVE_SECTION, payload: pageId }),
    });   
  };

  const showMoveConfirmModal = (draggedItemIndex, hoverIndex) => {
      const doNotShowAgain = getLocalStorage(DO_NOT_SHOW_AGAIN_KEY) === 'true';
      let checked = doNotShowAgain;

      const toggleDoNotShowAgain = ( doNotShowAgain: boolean ) => {
        checked = doNotShowAgain;
      };

      modals.openConfirmModal({
        children: <Flex direction='column' gap={10}>
          <Text fw={700} fz='lg'>{t('movePage.title', { ns: 'form_builder' })}</Text>
          <Text fz='md'>{t('movePage.content')}</Text>
          {/* Do not show againg checkbox */}
          <Checkbox
            label={<Text fz='md'>{t('movePage.doNotShowAgain', { ns: 'form_builder' })}</Text>}
            onChange={(e) => {
              toggleDoNotShowAgain(e.target.checked);
            }}
          />
        </Flex>,
        centered: true,
        labels: { confirm: t('common.continue', { ns: 'common' }), cancel: t('common.cancel', { ns: 'common' }) },
        confirmProps: { color: 'red' },
        onConfirm: () => {
          moveNavItem && moveNavItem(draggedItemIndex, hoverIndex);
          setLocalStorage(DO_NOT_SHOW_AGAIN_KEY, (checked).toString());
        },
      });
  };

  const setAciveSection = (id) => {
    dispatch({ type: SET_ACTIVE_SECTION, payload: id });
  };

  //   Drag handler
  const [{ isOver }, drop] = useDrop({
    accept: ItemType,
    hover(draggedItem: { index: number }) {
      if (draggedItem.index !== index) {
        setIsHoveredOver(true);
      }
    },
    drop: (draggedItem: { index: number }) => {
      if (draggedItem.index !== index) {
        const doNotShowAgain = getLocalStorage(DO_NOT_SHOW_AGAIN_KEY) === 'true';
        if (doNotShowAgain || !isFormContainLogic ) {
          moveNavItem && moveNavItem(draggedItem.index, index);
          return;
        }
        showMoveConfirmModal(draggedItem.index, index);
      }
      setIsHoveredOver(false);
    },
    collect: (monitor) => ({
      isOver: monitor.isOver(),
    }),
  });

  // Reset hover state when drag leaves
  useEffect(() => {
    if (!isOver) {
      setIsHoveredOver(false);
    }
  }, [isOver]);

  const [{ isDragging }, drag] = useDrag({
    type: ItemType,
    item: { type: ItemType, index },
    end: () => {
      dispatch(setIsDragging(false));
    },
    collect: (monitor) => {
      if (monitor.isDragging()) {
        dispatch(setIsDragging(true));
      }
      return {
        isDragging: monitor.isDragging(),
      };
    },
  });

  drag(drop(ref));

  return (
    <Flex
      ref={ref}
      justify='space-between'
      align='center'
      key={page.id}
      className={clsx(classes.navItem, { miniMode: !isFullMode }, { active: isActive }, { isHoveredOver: isHoveredOver })}
      style={{ 
        opacity: isDragging ? 0.5 : 1,
      }}
      onMouseDown={() => setAciveSection(page.id)}
      onDoubleClick={() => isFullMode && enableRenamingPage()}
    >
      {isRenamingPage ? (
        <input
          className={classes.editingInput}
          type='text'
          value={renamingInputValue}
          onChange={onNameChange}
          onBlur={renameSection}
        />
      ) : (
        <Text className={classes.navItemText}>{isFullMode ? page.name : visibleIndex}</Text>
      )}
      {isFullMode && (
        <PageActions
          onDelete={() => showConfirmModal(page.id)}
          deleteDisabled={!ableToDelete}
          onDuplicate={() => dispatch({ type: DUPLICATE_SECTION, payload: page.id })}
          onRename={enableRenamingPage}
        />
      )}
    </Flex>
  );
}
