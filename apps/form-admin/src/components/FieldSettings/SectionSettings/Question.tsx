import { UPDATE_FIELD } from '@/store/action/actionTypes';
import { selectingFieldSelector } from '@/store/reducers/builder';
import { type GroupFormField } from '@/types/form-builder';
import { Divider, Flex, Group, Text, TextInput, rem } from '@mantine/core';
import { cleanBadMarkdownContent } from '@resola-ai/ui/utils/string';
import { useTranslation } from 'react-i18next';
import { useDispatch, useSelector } from 'react-redux';
import { useState, useEffect } from 'react';
import GroupFieldManager from './GroupFieldManager';

const SectionSettings = () => {
  const selectingGroup = useSelector(selectingFieldSelector) as GroupFormField;
  const dispatch = useDispatch();
  const { t } = useTranslation('form_builder');
  const [error, setError] = useState<string>('');
  const [localValue, setLocalValue] = useState<string>('');

  // Sync local value with store value when component mounts or selectingGroup changes
  useEffect(() => {
    setLocalValue(cleanBadMarkdownContent(selectingGroup?.label) || '');
  }, []);

  const validateSectionName = (value: string): boolean => {
    const trimmedValue = value.trim();
    if (trimmedValue.length < 2 || trimmedValue.length > 40) {
      setError(t('formField.section.nameLengthError'));
      return false;
    }
    setError('');
    return true;
  };

  const onInputLabel = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setLocalValue(value); // Always update local value to allow typing
    const isValid = validateSectionName(value);
    
    // Only update store if validation passes
    if (isValid) {
      dispatch({
        type: UPDATE_FIELD,
        payload: { ...selectingGroup, label: value },
      });
    }
  };

  return (
    <Flex direction='column' gap={rem(16)}>
      <Group>
        <Text fw={500}>{t('formField.section.name')}</Text>
        <TextInput 
          className='w-full' 
          value={localValue} 
          onInput={onInputLabel}
          error={error}
        />
      </Group>

      <Divider my='xs' style={{ margin: 0 }} />

      <Text fw={500} size='sm'>
        {t('formField.section.fieldsInSection', { count: selectingGroup?.fields?.length || 0 })}
      </Text>

      <GroupFieldManager group={selectingGroup} />
    </Flex>
  );
};

export default SectionSettings;
