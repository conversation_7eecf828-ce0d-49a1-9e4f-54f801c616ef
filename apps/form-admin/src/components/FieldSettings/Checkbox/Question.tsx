import { UPDATE_FIELD } from '@/store/action/actionTypes';
import { useAppDispatch } from '@/store/hooks';
import { selectingFieldSelector } from '@/store/reducers/builder';
import { Group, Select, Text, rem } from '@mantine/core';
import { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useSelector } from 'react-redux';
import { SwitchesSetting } from '../Settings';

const QuestionSettings = () => {
  const { t } = useTranslation('form_builder');

  const DefaultValues = [
    {
      value: 'checked',
      label: t('checked'),
    },
    {
      value: 'unchecked',
      label: t('unchecked'),
    },
  ];
  const selectingField = useSelector(selectingFieldSelector);
  const dispatch = useAppDispatch();
  const [value, setValue] = useState(
    selectingField?.options.length && selectingField.options[0].defaultCheck
      ? 'checked'
      : 'unchecked'
  );

  const handleChangeLayoutOption = (value: string) => {
    setValue(value);
    const options = [...selectingField.options];
    if (options.length) {
      options[0] = { ...options[0], ...{ defaultCheck: value === 'checked' } };
    } else {
      options[0] = [
        { label: t('typeYourDescription'), value: t('typeYourDescription'), defaultCheck: true },
      ];
    }

    dispatch({ type: UPDATE_FIELD, payload: { ...selectingField, options: options } });
  };

  return (
    <div>
      <Group mb={rem(12)}>
        <Text fw={500}>{t('defaultValueLabel')}</Text>
        <Select
          value={value}
          data={DefaultValues}
          allowDeselect={false}
          w={'100%'}
          onChange={(value) => handleChangeLayoutOption(value as string)}
        />
      </Group>

      <SwitchesSetting
        allowRequired={true}
        allowDescription={false}
        allowHalfWidth={false}
        allowLegalLinkClick={true}
      />
    </div>
  );
};

export default QuestionSettings;
