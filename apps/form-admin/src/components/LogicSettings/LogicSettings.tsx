import { DEFAULT_COMBINATOR, MAX_GROUPS } from '@/constants/form-logic';
import { useFieldLogic } from '@/hooks/useFieldLogic';
import { useUpdateFieldLogic } from '@/hooks/useUpdateFieldLogic';
import type { FormLogicAction } from '@/types/form-logic';
import { <PERSON><PERSON>, <PERSON>ack, Text } from '@mantine/core';
import { IconPlus } from '@tabler/icons-react';
import { debounce } from 'lodash';
import { useCallback } from 'react';
import { useTranslation } from 'react-i18next';
import type { RuleGroupType } from 'react-querybuilder';
import ConditionsBuilder from './ConditionsBuilder';

export const LogicSettings = () => {
  const { t } = useTranslation('form_builder');
  const { logic, setLogic } = useFieldLogic();
  const updateFieldLogic = useUpdateFieldLogic();

  const debouncedUpdateFieldLogic = useCallback(
    debounce(updateFieldLogic, 1000),
    [updateFieldLogic]
  );

  const handleAddCondition = () => {
    if (!logic) return;

    const newConditionGroup = {
      combinator: DEFAULT_COMBINATOR,
      rules: [
        {
          field: '',
          operator: 'is',
          value: '',
        },
      ],
    };

    const updatedLogic = {
      ...logic,
      conditions: {
        ...logic.conditions,
        combinator: logic.conditions.combinator || DEFAULT_COMBINATOR,
        rules: [...logic.conditions.rules, newConditionGroup as any],
      },
    };

    setLogic(updatedLogic);
  };

  const handleQueryChange = (newQuery: RuleGroupType) => {
    if (!logic) return;

    const updatedLogic = {
      ...logic,
      conditions: newQuery as any,
    };

    setLogic(updatedLogic);
    debouncedUpdateFieldLogic(updatedLogic);
  };

  const handleActionChange = (action: FormLogicAction) => {
    if (!logic) return;

    const updatedLogic = {
      ...logic,
      action,
    };

    setLogic(updatedLogic);
    updateFieldLogic(updatedLogic);
  };

  if (!logic) {
    return null;
  }

  const hasConditions = logic.conditions?.rules?.length > 0;
  const showAddConditionButton = logic.conditions?.rules?.length < MAX_GROUPS;

  return (
    <Stack gap='sm' align='flex-start'>
      {hasConditions ? (
        <ConditionsBuilder
          query={logic.conditions as RuleGroupType}
          action={logic.action}
          onQueryChange={handleQueryChange}
          onActionChange={handleActionChange}
          showDuplicateGroupButton={showAddConditionButton}
        />
      ) : (
        <Text>{t('logic.setUpConditions')}</Text>
      )}

      {showAddConditionButton && (
        <Button
          variant='transparent'
          color='decaBlue.5'
          px='0'
          fz='md'
          fw={500}
          leftSection={<IconPlus size={16} />}
          onClick={handleAddCondition}
        >
          {t('logic.addCondition')}
        </Button>
      )}
    </Stack>
  );
};
