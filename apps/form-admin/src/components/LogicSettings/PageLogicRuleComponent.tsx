import { ActionIcon, Button, Flex, Group, rem, Select, Stack, Text, useMantineTheme } from '@mantine/core';
import { IconPlus, IconTrash, IconChevronDown, IconChevronRight } from '@tabler/icons-react';
import type { RuleGroupType } from 'react-querybuilder';
import type { PageLogicRule } from '@/types/form-logic';
import PageConditionsBuilder from './PageConditionsBuilder';
import { useTranslation } from 'react-i18next';
import { useState } from 'react';

const ENDING_CONFIG_VALUE = '__ending_configuration__';

interface PageLogicRuleComponentProps {
  rule: PageLogicRule;
  index: number;
  pageOptions: Array<{ value: string; label: string }>;
  canAddMoreConditions: boolean;
  onRemove: (ruleId: string) => void;
  onTargetChange: (ruleId: string, targetPageId: string | null) => void;
  onQueryChange: (ruleId: string, newQuery: RuleGroupType) => void;
  onAddCondition: (ruleId: string) => void;
}

export const PageLogicRuleComponent = ({
  rule,
  index,
  pageOptions,
  canAddMoreConditions,
  onRemove,
  onTargetChange,
  onQueryChange,
  onAddCondition,
}: PageLogicRuleComponentProps) => {
  const { t } = useTranslation('form_builder');
  const theme = useMantineTheme();
  const [isExpanded, setIsExpanded] = useState(true);
  
  const handleSelectChange = (value: string | null) => {
    onTargetChange(rule.id!, value);
  };

  const toggleExpanded = () => {
    setIsExpanded(!isExpanded);
  };

  return (
    <Stack gap="sm" sx={{ borderTop: `1px solid ${theme.colors.decaLight[2]}`, paddingTop: rem(10) }}>
      <Group justify="space-between" align="center">
        <Group gap="xs" align="center" style={{ cursor: 'pointer' }} onClick={toggleExpanded}>
          <Text fw={500} size="md">{t('logic.rule_number', { number: index + 1 })}</Text>
          <ActionIcon variant="transparent" size="sm">
            {isExpanded ? <IconChevronDown size={16} /> : <IconChevronRight size={16} />}
          </ActionIcon>
        </Group>
        <ActionIcon 
          variant="transparent" 
          color="red" 
          onClick={() => onRemove(rule.id!)}
        >
          <IconTrash size={16} />
        </ActionIcon>
      </Group>
      
      {!isExpanded && (
        <Text size="md" c="theme.colors.decaGrey[9]">
          {rule.targetPageId === ENDING_CONFIG_VALUE 
            ? t('logic.redirect_to_ending')
            : rule.targetPageId 
              ? t('logic.redirect_to_page', { 
                  pageName: pageOptions.find(option => option.value === rule.targetPageId)?.label || rule.targetPageId 
                })
              : t('logic.no_target_selected')
          }
        </Text>
      )}
      
      {isExpanded && (
        <>
          <Text size="md" fw={500}>{t('logic.only_go_to')}</Text>
          <Select
            placeholder={t('logic.select_target_page')}
            data={[
              ...pageOptions,
              { value: ENDING_CONFIG_VALUE, label: t('logic.ending_configuration') }
            ]}
            value={rule.targetPageId}
            onChange={handleSelectChange}
          />
          
          <Text size="md" fw={500}>{t('logic.when')}</Text>
          
          <Stack gap="sm">
            <PageConditionsBuilder
              query={rule.conditions as RuleGroupType}
              onQueryChange={(newQuery) => onQueryChange(rule.id!, newQuery)}
            />
            
            {canAddMoreConditions && (
              <Flex justify="flex-start">
                <Button
                  variant="transparent"
                  color="decaBlue.5"
                  px="0"
                  fz="sm"
                  fw={500}
                  leftSection={<IconPlus size={14} />}
                  onClick={() => onAddCondition(rule.id!)}
                >
                  {t('logic.add_condition')}
                </Button>
              </Flex>
            )}
          </Stack>
        </>
      )}
    </Stack>
  );
};
