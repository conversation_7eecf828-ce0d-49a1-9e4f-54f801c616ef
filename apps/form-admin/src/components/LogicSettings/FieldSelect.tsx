import {
  Combobox,
  Divider,
  Group,
  InputBase,
  ScrollArea,
  Stack,
  Text,
  rem,
  useCombobox,
} from '@mantine/core';
import { createStyles } from '@mantine/emotion';
import { IconChevronDown, IconEyeOff } from '@tabler/icons-react';
import { unescape as lodashUnescape } from 'lodash';
import { useTranslation } from 'react-i18next';
import type { Field } from 'react-querybuilder';

const useStyles = createStyles((theme) => ({
  chevronIcon: {
    color: theme.colors.decaGrey[5],
  },
  hiddenFieldChip: {
    display: 'inline-flex',
    padding: `${rem(2)} ${rem(12)}`,
    backgroundColor: '#F2F2F6',
    color: theme.colors.decaGrey[5],
    borderRadius: theme.radius.lg,
  },
  hiddenFieldIcon: {
    color: theme.colors.decaGrey[3],
  },
  fieldSelect: {
    '& .mantine-InputBase-input': {
      whiteSpace: 'nowrap',
      textOverflow: 'ellipsis',
      overflow: 'hidden',
      paddingTop: rem(2)
    },
  },
}));

interface FieldSelectProps {
  fields: Field[];
  value: string;
  onChange: (value: string) => void;
}

const HiddenFieldChip = ({ label }: { label: string }) => {
  const { classes } = useStyles();
  return (
    <Group gap='xs' className={classes.hiddenFieldChip}>
      <IconEyeOff size={16} className={classes.hiddenFieldIcon} />
      <Text>{lodashUnescape(label)}</Text>
    </Group>
  );
};

export const FieldSelect = ({ fields, value, onChange }: FieldSelectProps) => {
  const { t } = useTranslation('form_builder');
  const { classes } = useStyles();
  const combobox = useCombobox({
    onDropdownClose: () => combobox.resetSelectedOption(),
  });

  const groupedFields = () => {
    const regularFields = fields.filter((f: Field) => f.fieldType !== 'hidden');
    const hiddenFields = fields.filter((f: Field) => f.fieldType === 'hidden');

    return { regularFields, hiddenFields };
  };

  const { regularFields, hiddenFields } = groupedFields();

  const handleFieldSelect = (fieldValue: string) => {
    onChange(fieldValue);
    combobox.closeDropdown();
  };

  const isHiddenField = (fieldId: string) => {
    return hiddenFields.some((f: Field) => f.name === fieldId);
  };

  const getFieldLabel = () => {
    const fieldOption = fields.find((f: Field) => f.name === value);
    return fieldOption?.label ? lodashUnescape(fieldOption.label) : '';
  };
  
  return (
    <Combobox store={combobox} onOptionSubmit={handleFieldSelect} withinPortal={false}>
      <Combobox.Target>
        <InputBase
          component='button'
          type='button'
          pointer
          rightSection={<IconChevronDown size={16} className={classes.chevronIcon} />}
          onClick={() => combobox.toggleDropdown()}
          rightSectionPointerEvents='none'
          className={classes.fieldSelect}
        >
          {isHiddenField(value) ? (
            <HiddenFieldChip label={getFieldLabel()} />
          ) : value ? (
            getFieldLabel()
          ) : (
            <Text c='dimmed'>{t('logic.selectQuestionOrField')}</Text>
          )}
        </InputBase>
      </Combobox.Target>

      <Combobox.Dropdown>
        <Combobox.Options>
          <ScrollArea.Autosize mah={300} type='scroll'>
            {regularFields.length > 0 && (
              <Stack gap='0'>
                <Text px='sm' py='xs' c='decaNavy.5' fw={500}>
                  {t('fields')}
                </Text>
                {regularFields.map((field: Field) => (
                  <Combobox.Option
                    key={field.name}
                    value={field.name}
                    active={value === field.name}
                  >
                    {lodashUnescape(field.label)}
                  </Combobox.Option>
                ))}
              </Stack>
            )}

            {hiddenFields.length > 0 && (
              <>
                <Divider my='xs' />
                <Stack gap='0'>
                  <Text px='sm' py='xs' c='decaNavy.5' fw={500}>
                    {t('hiddenFields')}
                  </Text>
                  {hiddenFields.map((field: Field) => (
                    <Combobox.Option
                      key={field.name}
                      value={field.name}
                      active={value === field.name}
                    >
                      <HiddenFieldChip label={field.label} />
                    </Combobox.Option>
                  ))}
                </Stack>
              </>
            )}
          </ScrollArea.Autosize>
        </Combobox.Options>
      </Combobox.Dropdown>
    </Combobox>
  );
};
