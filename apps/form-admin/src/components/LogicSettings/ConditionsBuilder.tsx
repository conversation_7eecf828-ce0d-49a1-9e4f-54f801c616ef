import { useLogicFields } from '@/hooks/useLogicFields';
import type { FormLogicAction } from '@/types/form-logic';
import { ulid } from '@/utils/uuid';
import { Stack } from '@mantine/core';
import { QueryBuilder, type RuleGroupType } from 'react-querybuilder';
import { CustomCombinatorSelector } from './CustomCombinatorSelector';
import { CustomRuleGroup } from './CustomRuleGroup';
import { ShowHideToggle } from './ShowHideToggle';

interface ConditionsBuilderProps {
  action: FormLogicAction;
  query: RuleGroupType;
  showDuplicateGroupButton: boolean;
  onActionChange: (action: FormLogicAction) => void;
  onQueryChange: (query: RuleGroupType) => void;
}

const ConditionsBuilder = ({
  action,
  query,
  showDuplicateGroupButton,
  onActionChange,
  onQueryChange,
}: ConditionsBuilderProps) => {
  const { availableFields } = useLogicFields();
  
  return (
    <Stack gap='sm' w='100%'>
      <ShowHideToggle value={action} onChange={onActionChange} />
      <QueryBuilder
        fields={availableFields}
        query={query}
        onQueryChange={onQueryChange}
        showCombinatorsBetweenRules={true}
        showNotToggle={false}
        addRuleToNewGroups={false}
        idGenerator={ulid}
        controlElements={{
          ruleGroup: props => <CustomRuleGroup {...props} showDuplicateGroupButton={showDuplicateGroupButton} />,
          combinatorSelector: CustomCombinatorSelector,
          addRuleAction: () => null,
          addGroupAction: () => null,
          removeGroupAction: () => null,
        }}
      />
    </Stack>
  );
};

export default ConditionsBuilder;
