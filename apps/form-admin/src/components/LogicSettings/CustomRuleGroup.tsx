import { MAX_CONDITIONS, OPERATOR_LABELS } from '@/constants/form-logic';
import { useLogicFields } from '@/hooks';
import { FieldType } from '@/types';
import { ulid } from '@/utils/uuid';
import { ActionIcon, Button, Divider, Group, Paper, Stack, Text } from '@mantine/core';
import { createStyles } from '@mantine/emotion';
import { IconChevronDown, IconCopy, IconPlus } from '@tabler/icons-react';
import dayjs from 'dayjs';
import { unescape as lodashUnescape } from 'lodash';
import { useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';
import type { Field, RuleType } from 'react-querybuilder';
import { CustomCombinatorSelector } from './CustomCombinatorSelector';
import { CustomRule } from './CustomRule';

const useStyles = createStyles((theme) => ({
  conditionContainer: {
    backgroundColor: theme.colors.decaLight[0],
    padding: theme.spacing.sm,
    borderRadius: theme.radius.md,
    border: `1px solid ${theme.colors.decaLight[2]}`,
    position: 'relative',
  },
  ruleGroup: {
    backgroundColor: 'transparent',
    border: 'none',
    padding: 0,
    display: 'flex',
    flexDirection: 'column',
    gap: theme.spacing.md,
    marginTop: theme.spacing.xs,
  },
  conditionHeader: {
    cursor: 'pointer',
    userSelect: 'none',
  },
  chevronIcon: {
    color: theme.colors.decaGrey[5],
    transition: 'transform 200ms ease',
  },
  chevronIconRotated: {
    transform: 'rotate(-90deg)',
  },
  collapsedSummary: {
    fontSize: theme.fontSizes.md,
    paddingLeft: theme.spacing.lg,
  },
  addValueButton: {
    '& .mantine-Button-inner': {
      justifyContent: 'flex-start',
    },
  },
}));

export const CustomRuleGroup = (props: any) => {
  const { t } = useTranslation('form_builder');
  const { classes } = useStyles();
  const {
    ruleGroup,
    rules,
    combinator,
    path,
    schema,
    actions,
    context,
    translations,
    showDuplicateGroupButton,
    ...restProps
  } = props;
  const { getFieldData, getFieldOptions, isHiddenField } = useLogicFields();

  const isConditionGroup = path && path.length > 0;
  const showAddValueButton = rules.length < MAX_CONDITIONS;
  const [isExpanded, setIsExpanded] = useState(true);

  const toggleExpanded = () => {
    setIsExpanded(!isExpanded);
  };

  const handleAddValue = () => {
    const newRule = {
      field: '',
      operator: 'is',
      value: '',
    };

    actions?.onRuleAdd?.(newRule, path);
  };

  const handleDuplicateGroup = () => {
    const duplicatedGroup = {
      combinator: combinator || 'and',
      rules: rules.map((rule: RuleType) => ({
        ...rule,
        id: ulid(),
      })),
    };

    const parentPath = path.slice(0, -1);
    actions?.onGroupAdd?.(duplicatedGroup, parentPath);
  };

  const getFieldLabel = (rule: RuleType) => {
    const isHidden = isHiddenField(rule.field);
    const fieldOption = schema?.fields?.find((f: Field) => f.name === rule.field);
    if (isHidden) {
      return t('logic.hiddenFieldNamed', { name: lodashUnescape(fieldOption.label) });
    }
    return fieldOption?.label ? lodashUnescape(fieldOption.label) : '';
  };

  const getOperatorLabel = (operator: string) => {
    return OPERATOR_LABELS[operator]?.toLowerCase();
  };

  const getRuleValue = (rule: RuleType) => {
    if (['is_empty', 'is_not_empty'].includes(rule.operator)) {
      return '';
    }
    if (['is_any_of', 'is_not_any_of'].includes(rule.operator)) {
      const options = getFieldOptions(rule.field);
      return options
        .filter((option) => rule.value.includes(option.value))
        .map((option) => option.label)
        .join(', ');
    }

    const fieldData = getFieldData(rule.field);

    if (fieldData?.type === FieldType.Date) {
      return dayjs(rule.value).format(fieldData.dateFormat);
    }

    return rule.value;
  };

  const renderRulesAndCombinators = useMemo(() => {
    if (!rules || rules.length === 0) {
      return null;
    }

    const elements: JSX.Element[] = [];

    rules.forEach((rule: any, index: number) => {
      const rulePath = [...path, index];

      if (rule.rules) {
        elements.push(
          <CustomRuleGroup
            key={rule.id}
            ruleGroup={rule}
            rules={rule.rules}
            combinator={rule.combinator}
            path={rulePath}
            schema={schema}
            actions={actions}
            context={context}
            translations={translations}
            showDuplicateGroupButton={showDuplicateGroupButton}
            {...restProps}
          />
        );
      } else {
        elements.push(
          <CustomRule
            key={rule.id}
            rule={rule}
            path={rulePath}
            schema={schema}
            actions={actions}
            context={context}
            translations={translations}
            id={rule.id}
            parentRules={rules}
          />
        );
      }

      if (index < rules.length - 1) {
        const isFirstCombinator = index === 0;

        if (isFirstCombinator) {
          elements.push(
            <CustomCombinatorSelector
              key={`combinator-${ulid()}`}
              options={[
                { name: 'and', label: 'And' },
                { name: 'or', label: 'Or' },
              ]}
              value={combinator}
              handleOnChange={(newCombinator: string) => {
                actions?.onPropChange?.('combinator', newCombinator, path);
              }}
              rules={rules}
              path={path}
              level={path.length}
            />
          );
        } else {
          elements.push(
            <Text fw={500} c='decaGrey.9' key={`combinator-${ulid()}`} ta='left'>
              {combinator === 'and' ? 'And' : 'Or'}
            </Text>
          );
        }
      }
    });

    return elements;
  }, [rules, path, combinator, schema, actions, context, translations, restProps]);

  if (isConditionGroup) {
    const conditionNumber = path[path.length - 1] + 1;
    const isNoFieldsSelected = rules.every((rule) => !rule.field);

    return (
      <Paper className={classes.conditionContainer}>
        <Stack gap='xs'>
          <Group justify='space-between' align='center' className={classes.conditionHeader}>
            <Text fw={500}>{t('logic.condition', { conditionNumber })}</Text>
            <Group gap='xs'>
              {showDuplicateGroupButton && (
                <ActionIcon variant='transparent' color='decaGrey.6' onClick={handleDuplicateGroup}>
                  <IconCopy size={16} />
                </ActionIcon>
              )}
              <ActionIcon variant='transparent' color='decaGrey.6' onClick={toggleExpanded}>
                <IconChevronDown
                  size={16}
                  className={`${classes.chevronIcon} ${!isExpanded ? classes.chevronIconRotated : ''}`}
                />
              </ActionIcon>
            </Group>
          </Group>

          <Divider />

          {isExpanded ? (
            <Stack gap='xs'>
              {renderRulesAndCombinators}
              {showAddValueButton && (
                <Button
                  p='0'
                  variant='transparent'
                  leftSection={<IconPlus size={16} />}
                  c='decaBlue.5'
                  fz='md'
                  onClick={handleAddValue}
                  className={classes.addValueButton}
                >
                  {t('logic.addValue')}
                </Button>
              )}
            </Stack>
          ) : isNoFieldsSelected ? (
            <Text fw={400}>{t('logic.noFieldsSelected')}</Text>
          ) : (
            <Stack gap='4' mt='3'>
              <Text fw={500}>{t('logic.aValueOf')}</Text>
              <ul className={classes.collapsedSummary}>
                {rules
                  ?.filter((rule) => !rule.rules && rule.field)
                  .map((rule, index) => (
                    <li key={rule.id || index}>
                      <strong>{getFieldLabel(rule)}</strong> {getOperatorLabel(rule.operator)} <strong>{getRuleValue(rule)}</strong>
                    </li>
                  ))}
              </ul>
            </Stack>
          )}
        </Stack>
      </Paper>
    );
  }

  return <div className={classes.ruleGroup}>{renderRulesAndCombinators}</div>;
};
