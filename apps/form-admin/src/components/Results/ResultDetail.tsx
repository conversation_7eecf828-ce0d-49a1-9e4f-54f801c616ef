import FileUploadResult from '@/components/Results/FileUploadResult';
import ResultRatingIcon from '@/components/Results/ResultRatingIcon';
import { useFormData } from '@/hooks';
import {
  type CustomizeFieldInfo,
  FieldType,
  type FormResponse,
  type FormResponseProfile,
  type QuestionResponse,
} from '@/types';
import { Avatar, Box, Divider, Flex, Text } from '@mantine/core';
import { createStyles } from '@mantine/emotion';
import { IconX } from '@tabler/icons-react';
import dayjs from 'dayjs';
import { Fragment } from 'react';
import { useTranslation } from 'react-i18next';

const useStyles = createStyles((_, { open }: { open: boolean }) => ({
  container: {
    flexDirection: 'column',
    background: 'white',
    width: open ? '40%' : 0,
    flexShrink: 0,
    transition: 'width 0.3s',
    borderLeft: '1px solid #e3e3e3',
    overflow: 'auto',
    wordBreak: 'break-word',
  },
  questionTitle: {
    fontWeight: 'bolder',
  },
}));

interface ResultDetailProps {
  open: boolean;
  id: string | null;
  onClose: () => void;
  response: FormResponse | null;
  responseProfile?: FormResponseProfile | null;
  questions: CustomizeFieldInfo[];
}

interface QuestionResponseMap extends QuestionResponse {
  questionId: string;
}

const ResultDetail = ({
  response,
  open,
  onClose,
  responseProfile,
  questions,
}: ResultDetailProps) => {
  const { classes } = useStyles({ open });
  const { form } = useFormData();
  const { t } = useTranslation('results');

  if (!response) {
    return null;
  }
  
  const responseAnswers = Object.keys(response)
    .filter((key) => key.startsWith('q_') && ![FieldType.Heading, FieldType.Paragraph].includes(response[key]?.question_type as FieldType))
    .map((key) => ({ ...response[key], questionId: key }))
    .sort((answerA, answerB) => answerA.index - answerB.index);

  const renderCheckboxAnswer = (answer: QuestionResponseMap) => {
    const question = questions.find((questionItem) =>
      answer.questionId.endsWith(questionItem.questionId)
    );
    if (!question) {
      return;
    }
    return (
      <div>
        <div
          className={classes.questionTitle}
          dangerouslySetInnerHTML={{ __html: question.label }}
        />
        {answer.is_visible === false ? t('hiddenByLogic') : answer.flat_answer}
      </div>
    );
  };

  const getAnswerText = (answer: QuestionResponseMap) => {
    switch (answer.question_type) {
      case FieldType.Date:
      case FieldType.DateSelector:
        return answer.flat_answer ? dayjs(answer.flat_answer).format('YYYY/MM/DD') : '';
      case FieldType.DateTime:
        return answer.flat_answer ? dayjs(answer.flat_answer).format('YYYY/MM/DD HH:mm') : '';
      case FieldType.DateRange:
        return answer.values
          ? `${dayjs(answer.values[0]?.value).format('YYYY/MM/DD HH:mm')} - ${dayjs(answer.values[1]?.value).format('YYYY/MM/DD HH:mm')}`
          : '';
      case FieldType.Rating:
        return (
          <Flex align='center' gap={8}>
            <Text>
              {answer.flat_answer}/{answer.question_max_scale}
            </Text>
            <ResultRatingIcon icon={answer.question_shape || ''} />
          </Flex>
        );
      case FieldType.Checkbox:
        return renderCheckboxAnswer(answer);
      case FieldType.FileUploader:
        return answer.values.map((value) =>
          value.asset ? <FileUploadResult key={value.asset.id} asset={value.asset} /> : null
        );

      default:
        return answer.flat_answer;
    }
  };

  const renderAnswer = () => {
    let pageId = '';
    let pageNumber = 0;
    return responseAnswers.map((answer) => {
      const renderPage = pageId !== answer.page_id;
      if (renderPage) {
        pageId = answer.page_id;
        if (response.pages[pageId]?.name) {
          pageNumber += 1;
        }
      }
      return (
        <Fragment key={answer.index}>
          {renderPage && (
            <Text fw={500} c='decaNavy.4' mb={16}>
              {response.pages[pageId]?.name
                ? `${t('page', { pageNumber })}: ${response.pages[pageId]?.name}`
                : t('hiddenFields')}
            </Text>
          )}
          <Box mb={16}>
            {![FieldType.Checkbox].includes(answer.question_type as FieldType) && (
              <div
                className={classes.questionTitle}
                dangerouslySetInnerHTML={{ __html: answer.question_label }}
              />
            )}
            <p>{[FieldType.Checkbox].includes(answer.question_type as FieldType) || answer.is_visible !== false ? getAnswerText(answer) : t('hiddenByLogic')}</p>
          </Box>
        </Fragment>
      );
    });
  };

  return (
    <Flex className={classes.container}>
      <Flex justify={'flex-end'} p={'sm'}>
        <IconX size={20} cursor={'pointer'} onClick={onClose} />
      </Flex>
      <Divider />
      <Box p='lg'>
        <h3>{form?.name}</h3>
        <Box mb={8}>
          <Text c='decaDark.1'>{dayjs(response.created_at).format('YYYY/M/D h:mma')}</Text>
          <Text c='decaDark.3'>{t('questionsSkippedWaring')}</Text>
        </Box>
        {responseProfile && (
          <Flex align='center' mb={28} gap={5}>
            <Avatar radius='xl' src={responseProfile.avatar} />
            <span>
              {t('lineId')}: {responseProfile.id}
            </span>
          </Flex>
        )}
        <section>{renderAnswer()}</section>
      </Box>
    </Flex>
  );
};

export default ResultDetail;
