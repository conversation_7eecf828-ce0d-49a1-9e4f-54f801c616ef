import { IntegrationAPI } from '@/services/api';
import { SET_INTEGRATION_ITEM } from '@/store/action/actionTypes';
import { useAppDispatch } from '@/store/hooks';
import { LoadingOverlay } from '@mantine/core';
import { modals } from '@mantine/modals';
import { logger } from '@resola-ai/services-shared';
import { isEmpty } from 'lodash';
import { useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { useNavigate, useSearchParams } from 'react-router-dom';
import useSWR from 'swr';
import List from './List';
import Settings from './Settings';

const Integrations = () => {
  const [searchParams] = useSearchParams();
  const integrationId = searchParams.get('int_id') || '';
  const { t } = useTranslation('common');
  const navigate = useNavigate();
  const dispatch = useAppDispatch();

  const {
    data: integration,
    mutate,
    error,
  } = useSWR(
    integrationId ? `/integrations/${integrationId}` : null,
    () => IntegrationAPI.get(integrationId),
    {
      revalidateOnMount: true,
      revalidateOnFocus: true,
      onSuccess: (data) => {
        dispatch({ type: SET_INTEGRATION_ITEM, payload: data });
      },
    }
  );

  useEffect(() => {
    if (error) {
      logger.error('Error fetching form data:', error);

      modals.openConfirmModal({
        title: t('error'),
        children: t('formNotFound'),
        centered: true,
        labels: { confirm: t('OK'), cancel: '' },
        confirmProps: { color: 'red' },
        cancelProps: { display: 'none' }, // Hide the cancel button
        withCloseButton: false,
        closeOnClickOutside: false,
        onConfirm: () => {
          navigate('/forms/');
        },
      });
    }
  }, [error]);

  if (integrationId && !integration && !error) {
    return <LoadingOverlay visible />;
  }
  if (error) {
    return null;
  }

  return isEmpty(integrationId) ? (
    <List />
  ) : (
    <Settings integration={integration} fetchLatestData={mutate} />
  );
};

export default Integrations;
