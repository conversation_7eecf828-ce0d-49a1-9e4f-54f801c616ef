import { JAPAN_PREFECTURE_MAP } from '@/constants/form-logic';
import { formPagesDataSelector } from '@/store/reducers/builder';
import { FieldType, GroupFieldType } from '@/types/form-builder';
import { useCallback, useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import type { Field } from 'react-querybuilder';
import { useSelector } from 'react-redux';

const stripHtmlTags = (text: string) => {
  return text.replace(/<[^>]*>/g, '');
};

const IGNORE_FIELDS = [FieldType.Paragraph, FieldType.Heading, GroupFieldType.Section];

export const useLogicFields = () => {
  const { t } = useTranslation('form_builder');
  const formContent = useSelector(formPagesDataSelector);

  const fieldsMap = useMemo(() => {
    if (!formContent) return new Map();

    const map = new Map();
    formContent.forEach((page) => {
      page.content.forEach((item: any) => {
        if (item.id) {
          map.set(item.id, item);
        }
        if (item.fields?.length > 0) {
          item.fields.forEach((field: any) => {
            if (field.id) {
              map.set(field.id, field);
            }
          });
        }
      });
    });
    return map;
  }, [formContent]);

  const availableFields = useMemo((): Field[] => {
    if (!formContent) return [];

    const getFieldLabel = (field: any) => {
      if (field.type === FieldType.Checkbox) {
        return stripHtmlTags(field.options[0].label);
      }
      return stripHtmlTags(field.label);
    };

    const transformField = (field: any): Field => ({
      value: field.id,
      name: field.id,
      label: getFieldLabel(field),
      fieldType: field.type,
    });

    const isValidField = (field: any): boolean =>
      !IGNORE_FIELDS.includes(field.type) &&
      (!!field.label || !!field.fields?.length) &&
      !field.isHide;

    return formContent.flatMap((page) =>
      page.content.flatMap((item: any) => {
        if (!isValidField(item)) return [];

        if (item.fields?.length > 0) {
          return item.fields.filter(isValidField).map(transformField);
        }
        return transformField(item);
      })
    );
  }, [formContent]);

  const getFieldOptions = useCallback(
    (fieldId: string) => {
      if (!fieldId || !fieldsMap.has(fieldId)) return [];

      const fieldData = fieldsMap.get(fieldId);

      if (fieldData.name === 'prefecture') {
        return Object.entries(JAPAN_PREFECTURE_MAP).map(([_, value]) => ({
          value: value,
          label: value,
        }));
      }

      if ([FieldType.OpinionScale, FieldType.Rating].includes(fieldData.type)) {
        return Array.from({ length: fieldData.maxScale }, (_, index) => ({
          value: index + 1,
          label: index + 1,
        }));
      }

      if (!fieldData?.options?.length) return [];
      
      const options = fieldData.options.map((option: any) => ({
        value: option.id || stripHtmlTags(option.label),
        label: stripHtmlTags(option.label),
      }));

      if (fieldData.isOther) {
        options.push({
          value: 'other',
          label: t('otherLabel'),
        });
      }

      return options;
    },
    [fieldsMap, t]
  );

  const getFieldData = useCallback(
    (fieldId: string) => {
      if (!fieldId || !fieldsMap.has(fieldId)) return null;
      return fieldsMap.get(fieldId);
    },
    [fieldsMap]
  );

  const isHiddenField = useCallback(
    (fieldId: string) => {
      if (!fieldId || !fieldsMap.has(fieldId)) return false;
      return fieldsMap.get(fieldId).type === FieldType.Hidden;
    },
    [fieldsMap]
  );

  return {
    availableFields,
    getFieldOptions,
    getFieldData,
    isHiddenField
  };
};
