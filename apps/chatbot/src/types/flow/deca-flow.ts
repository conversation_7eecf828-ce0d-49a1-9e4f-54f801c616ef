import type { TFnType } from '@tolgee/web';
import type {
  <PERSON>,
  EdgeMouseHandler,
  EdgeRemoveChange,
  InternalNode,
  Node,
  NodeMouseHandler,
  NodeProps,
  NodeRemoveChange,
  OnEdgesChange,
  OnNodeDrag,
  OnNodesChange,
  OnNodesDelete,
  OnSelectionChangeFunc,
  OnSelectionChangeParams,
  ReactFlowInstance,
  Viewport,
  XYPosition,
} from '@xyflow/react';
import type { MouseEvent as ReactMouseEvent } from 'react';
import type { Intent } from '../nodes';
import type { EdgeTypes, TEdgeData } from './edge';
import type { FlowCategory, FlowType, TVariable } from './flow';
import {
  ENodeType,
  type IntentScope,
  type TActionData,
  type TApiStepData,
  type TBlockData,
  type TBuiltInNode,
  type TButtonStepData,
  type TCaptureInputStepData,
  type TCarouselStepData,
  type TChoiceStepData,
  type TConditionStepData,
  type TCustomTemplateStepData,
  type TDynamicStepData,
  type TEmailStepData,
  type TEntityStepData,
  type TFormStepData,
  type TGaiStepData,
  type TIntentData,
  type TKBStepData,
  type TLiveChatNodeData,
  type TScriptStepData,
  type TSetVariableData,
  type TStartNodeData,
  type TTableData,
  type TTextStepData,
} from './node';

export type TNodeData<T extends NodeTypeToDataMap[keyof NodeTypeToDataMap]> = {
  properties: T;
  canHandle?: boolean;
  canEditTitle?: boolean;
  isAddNewStep?: boolean;
  isStepInvalid?: boolean;
  isRemoveStep?: boolean;
  stepIndex?: number;
};

export type TReactFlowNode<T extends ENodeType = ENodeType> = Omit<
  Node<TNodeData<T extends keyof NodeTypeToDataMap ? NodeTypeToDataMap[T] : any>, ENodeType>,
  'type'
> & {
  type: T;
};

export type TReactFlowNodeProps<T extends ENodeType> = NodeProps & {
  className?: string;
  data: TNodeData<T extends keyof NodeTypeToDataMap ? NodeTypeToDataMap[T] : any>;
};

export type TEdge = Edge<TEdgeData, EdgeTypes>;

export type TReactFlowInstance = ReactFlowInstance<TReactFlowNode, TEdge>;

export type TInternalNode = InternalNode<TReactFlowNode>;

export type TEdgeMouseHandler = EdgeMouseHandler<TEdge>;

export type TOnEdgesChange = OnEdgesChange<TEdge>;

export type TOnSelectionChange = OnSelectionChangeFunc<TReactFlowNode, TEdge>;

export type TOnNodeDrag = OnNodeDrag<TReactFlowNode>;

export type TOnNodesDelete = OnNodesDelete<TReactFlowNode>;

export type TNodeMouseHandler = NodeMouseHandler<TReactFlowNode>;

export type TOnNodesChange = OnNodesChange<TReactFlowNode>;

export type TOnPaneContextMenu = (event: ReactMouseEvent | MouseEvent) => void;

export type THandleEdgeHover = (edge: TEdge, hover: boolean) => void;

export type TOnSelectionChangeParams = OnSelectionChangeParams<TReactFlowNode, TEdge>;

export type NodeTypeToDataMap = {
  [ENodeType.Start]: TStartNodeData;
  [ENodeType.Block]: TBlockData;
  [ENodeType.Email]: TEmailStepData;
  [ENodeType.QnA]: TKBStepData;
  [ENodeType.Buttons]: TButtonStepData;
  [ENodeType.Text]: TTextStepData;
  [ENodeType.Capture]: TCaptureInputStepData;
  [ENodeType.Choice]: TChoiceStepData;
  [ENodeType.Variables]: TSetVariableData;
  [ENodeType.Action]: TActionData;
  [ENodeType.Carousel]: TCarouselStepData;
  [ENodeType.Livechat]: TLiveChatNodeData;
  [ENodeType.Condition]: TConditionStepData;
  [ENodeType.Form]: TFormStepData;
  [ENodeType.Api]: TApiStepData;
  [ENodeType.Intent]: TIntentData;
  [ENodeType.CustomTemplate]: TCustomTemplateStepData;
  [ENodeType.Gai]: TGaiStepData;
  [ENodeType.Script]: TScriptStepData;
  [ENodeType.Dynamic]: TDynamicStepData;
  [ENodeType.Entity]: TEntityStepData;
  [ENodeType.Table]: TTableData;
};

export type TDataCopied = {
  nodes: TReactFlowNode[];
  edges: TEdge[];
  prevPastedPosition: XYPosition;
  prevPointerEventPosition: XYPosition;
  mapNodesIds: { [key: string]: string };
};

export type TSidebarNodeSetting<T extends ENodeType> = {
  node: TReactFlowNode<T>;
  panelHeight: number;
  submit: (data: TReactFlowNode<T>['data']['properties']) => void;
};

export type TDataPendingRemove = {
  nodes: NodeRemoveChange[];
  edges: EdgeRemoveChange[];
  callback?: () => void;
};

export type TContextMenu = {
  open: boolean;
  node?: TReactFlowNode;
  position?: {
    top?: number;
    left?: number;
    right?: number;
    bottom?: number;
  };
};

export type TPanelContextMenu = Pick<TContextMenu, 'position' | 'open'>;

export type TEdgeContextMenu = {
  open: boolean;
  edge?: TEdge;
};

export interface FlowToolbarItems {
  icon: JSX.Element;
  label: string;
  type: ENodeType;
  items?: FlowToolbarItems[];
}

export interface FlowToolbar {
  icon: JSX.Element;
  label: string;
  items?: FlowToolbarItems[];
}

export type TFlow = {
  botId: string;
  id: string;
  name: string;
  type: FlowType;
  category: FlowCategory;
  nodes: NodesObject;
  configs: FlowConfig;
  updated?: string;
  modifiedAt?: string;
};

export type NodesObject = { [key: string]: TBuiltInNode };

export interface ISuccessFlowResponse {
  flows: TFlow[];
  variables: TVariable[];
  intents: Intent[];
}

export type FlowConfig = {
  viewport: Viewport;
  creator?: number;
  intentNodes?: IntentNodeConfig[];
};

export type IntentNodeConfig = {
  ID: string;
  intentId?: string;
  intentScope: IntentScope;
};

export type TDropEngineParams = {
  reactFlowInstance: TReactFlowInstance;
  dragNode: TInternalNode;
  targetNode?: TInternalNode;
  t?: TFnType;
};
