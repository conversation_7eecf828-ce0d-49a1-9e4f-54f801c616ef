{"action": "アクション", "actionTitle": "アクション", "actionTypes": {"end": "会話終了", "node": "別のノードへ遷移", "postback": "API", "url": "URLを開く", "variable": "変数に設定"}, "actionURLPlaceholder": "URLを入力", "actionURLTitle": "URL", "apiNode": {"applyTo": "変数", "captureResponse": "レスポンスを変数に格納", "contentType": "ボディ", "contentTypeFormKeyValue": "フォームデータ (form-data)", "contentTypeFormURL": "URLエンコード化されたフォームデータ (x-www-form-urlencoded)", "contentTypeJSON": "JSON", "contentTypeNone": "なし", "contentTypePlainText": "テキスト", "customStatusCode": "カスタム", "customStatusCodePlaceholder": "コードを指定 (複数ある場合はカンマ区切り)", "defaultStatusCode": "デフォルト", "enterAPIContent": "Rawリクエストを入力", "enterKey": "キーを入力", "enterKeyValue": "キーを入力", "enterParameterKey": "キーを入力", "enterValueOrVariable": "値を入力 または '{{ variable }}'", "fail": "失敗した場合", "headers": "ヘッダー", "invalidCustomStatusCode": "ステータスコードの形式が正しくありません", "parameters": "クエリパラメーター", "requestURL": "リクエストURL", "requestURLPlaceholder": "リクエストURL または '{{ variable }}'", "selectMappingVariable": "変数を選択", "selectVariable": "変数を入力", "success": "成功した場合", "successStatusCode": "成功コードの設定", "title": "API", "useDecaDefaultToken": "Default DECA API Token"}, "blockNode": {"hideAllRichMenu": "すべてのリッチメニューを非表示にする", "lineRichMenu": "LINE リッチメニュー", "richMenuId": "リッチメニュー ID", "richMenuIdPlaceholder": "リッチメニュー ID を入力", "title": "ノード設定"}, "btnNodeNameTitle": "ボタンラベル", "btnNode": {"searchIntent": "ユーザー入力を許可し、発言意図を検索", "setResultToVariable": "結果を変数に設定", "setResultToVariableDescription": "ユーザーが入力した内容や、ボタンをクリックしたときのラベルを指定した変数に保存します。", "storeInputInVariable": "ユーザーの発言を変数に記憶する", "textButtons": "テキスト", "textButtonsDescription": "※LINEでは、ボタンのみを表示できません。そのため、テキスト設定がない場合はデフォルトテキストが自動送信されます。"}, "btnTitle": "ボタンテキスト", "button": "ボタン", "buttonCard": "ボタン", "buttonNamePlaceholder": "ボタンテキストを入力", "buttonNameTitle": "ボタンラベル", "buttonTitle": "ボタン", "cancel": "キャンセル", "captureInputNode": {"label": "ユーザーの自由入力を保存する変数", "placeholder": "発言内容の記憶", "selected": "{variable} に記憶", "selectMappingVariable": "変数を選択", "title": "発言内容の記憶"}, "captureIntentNode": {"intent": "発言意図", "noMatch": "一致しない場合", "searchIntent": "発言意図ノードからも検索する", "selectIntentPlaceholder": "発言意図を選択", "title": "発言意図の確認"}, "captureIntentNoMatch": "一致しない場合", "captureIntentPlaceholder": "発言意図の確認", "cardTitle": "タイトル", "carousel": "カルーセル", "conditionNode": {"condition": "条件", "conditionNameTitle": "条件", "conditionsTitle": "条件", "conditionTitle": "条件分岐", "else": "該当しない場合", "noMatch": "該当しない場合"}, "description": "メッセージ", "dragImageText": "ここに画像をドロップ\nまたはクリックでファイルを選択", "dynamicNode": {"attributeToBeSetAsTitle": "タイトルとして設定する属性", "attributeToBeSetAsVariable": "変数として設定する属性", "text": "テキスト", "textDescription": "※LINEでは、ボタンのみを表示できません。そのため、テキスト設定がない場合はデフォルトテキストが自動送信されます。", "textPlaceholder": "テキストを入力", "title": "ダイナミックボタン", "variable": "変数の抽出 (配列である必要があります)", "variablePlaceholder": "変数を選択", "variableStored": "値を保存する変数"}, "emailNode": {"bcc": "BCC", "bccPlaceholder": "メールアドレスを入力", "body": "メッセージ", "bodyPlaceholder": "メッセージを入力 (変数を使用できます)", "cc": "CC", "ccPlaceholder": "メールアドレスを入力", "emailExistedMessage": "既に入力されたEメールが重複して入力されています", "name": "カード名変更", "namePlaceholder": "Eメール送信", "nameRequired": "Label is required", "recipient": "下記の宛先にメールを送信します", "settingTitle": "Eメール送信", "title": "タイトル", "titlePlaceholder": "タイトルを入力", "to": "受信者", "toPlaceholder": "メールアドレスを入力", "toRequired": "Email is required", "validation": "Invalid email"}, "enterDesc": "メッセージを入力", "enterTitle": "タイトルを入力", "entityNode": {"confirmMessage": "プロンプトを編集すると、設定画面の項目とプロパティ名の変更が破棄されます。変更は設定画面に反映されません。", "confirmTitle": "プロンプトを編集", "enterEntity": "ユーザーの自由入力から聞き取る内容を入力", "enterPropertyName": "項目名を入力", "entity": "ユーザーの自由入力から聞き取る内容", "newEntity": "新規項目", "propertyName": "項目名 (英数字を使用してください)", "title": "AI入力分析", "variableFieldLabel": "ユーザーの入力を分析した結果を保存する変数", "variableFieldPlaceholder": "変数を選択"}, "formNode": {"inputFieldTitle": "入力フィールド", "label": "カード名変更", "mandatoryLabel": "必須", "name": "フォーム", "nameInputLabel": "ユーザーの入力を保存する変数", "optionLabel": "項目名", "optionLabelPlaceholder": "ユーザーに表示する項目名を入力", "optionValue": "値", "optionValuePlaceholder": "この項目が選択された場合、変数に保存する値を入力", "placeholder": "フォーム", "placeholderInputLabel": "プレースホルダー", "selectFieldTitle": "リスト項目", "selectLabel": "フォーム項目名", "selectName": "ユーザーの入力を保存する変数", "selectType": "ドロップダウンリスト", "submitButtonLabel": "送信ボタンのテキスト", "submitButtonPlaceholder": "ボタンテキストを入力", "textAreaLabel": "フォーム項目名", "textAreaType": "テキストエリア", "textInputLabel": "フォーム項目名", "textType": "一行テキスト", "typeInputLabel": "入力タイプ", "validationEmailOption": "Eメール", "validationNumberOption": "数字", "validationStringOption": "テキスト", "variableFieldLabel": "ユーザーの入力を保存する変数 (フォーム全体)", "variableFieldPlaceholder": "変数を選択"}, "genAi": {"fallbackPath": "生成に失敗した場合", "frequencyPenalty": "Frequency Penalty", "json": "JSON", "jsonPlaceholder": "JSONの出力例を指定してください", "outputType": "出力タイプ", "presencePenalty": "Presence penalty", "prompt": "プロンプト", "promptError": "最大 {max} 文字", "promptPlaceholder": "プロンプトを入力", "showAdvancedSetting": "詳細設定を表示", "streamingSupport": "ストリーミングサポート", "temperature": "Temperature", "text": "テキスト", "title": "生成AI", "topP": "Top P", "validate": "最大 {max} 文字 (残り {remained} 文字)", "variable": "出力結果を保存する変数", "variablePlaceholder": "変数を選択"}, "gotoBlockAction": {"block": "ノード", "flow": "会話フロー", "noBlocksFound": "一致するノードが見つかりません", "noFlowsFound": "一致する会話フローが見つかりません", "selectBlock": "ノードを選択", "selectFlow": "フローを選択"}, "inputError": "有効な値を入力してください", "intentNode": {"globalScope": "チャットボット全体", "intentScope": "発言意図ノードの適用範囲", "localScope": "この会話フロー内に限定", "placeholder": "発言意図", "title": "発言意図ノード", "utterances": "発話例"}, "kbNode": {"accuracyThresholdSetting": "精度のしきい値設定", "action": "アクション", "answerType": "応答タイプ", "article": "記事", "ascendants": "昇順", "back": "戻る", "cancel": "キャンセル", "confirm": "選択", "customDataField": "カスタム項目", "customDataKey": "カスタム項目の値", "descendants": "降順", "document": "ドキュメント", "enterCustomDataKey": "カスタム項目の値を入力", "enterId": "IDを入力", "enterKbId": "ナレッジベース ID (または変数) を入力してください", "enterValue": "値を入力", "foldersAndKbs": "フォルダとナレッジベース", "foldersKnowledgeBasesAndDocuments": "フォルダ、ナレッジベース、ドキュメント", "generativeAi": "ユーザー入力から回答を生成", "kbType": "ナレッジベース", "knowledgeBase": "ナレッジベース", "maxNumberOfReferenceArticle": "参照記事の最大数", "maxNumberOfReferenceArticlePlaceholder": "記事数を入力", "next": "次へ", "noMatch": "勝ち目がない", "notFoundPath": "回答できなかった場合", "notFoundQnA": "QnAが見つかりません。", "numberRelatedArticle": "リストに表示する記事数", "numberRelatedArticlePlaceholder": "記事数を入力", "operatorFeedback": "管理者フィードバック", "preview": "プレビュー", "previewNoResult": "関連する記事が見つかりませんでした", "previewSearchResult": "検索結果をプレビュー", "prompt": "プロンプト", "promptHelpText": "必ず '{'question'}' と '{'documents'}' を含めてください", "promptPlaceholder": "プロンプトを入力", "qna": "Q&A一覧", "relatedArticle": "記事リストのタイトル", "relatedArticlePlaceholder": "記事リストのタイトルを入力", "relevance": "関連度", "search": "Q&Aを検索", "searchArticle": "記事を検索", "searchArticlePlaceholder": "表示する記事を検索するキーワードを入力", "searchFolderAndKb": "フォルダとナレッジベースを検索", "searchFoldersKnowledgeBasesAndDocuments": "フォルダ、ナレッジベース、ドキュメントを検索", "searchFromKb": "ユーザー入力から回答を検索して表示", "select": "選択", "selectArticle": "記事を選択", "selectedArticle": "表示する記事を指定", "selectedFoldersAndKbs": "選択したフォルダとナレッジベース", "selectedItems": "選択した項目", "selectedQna": "表示するQ&Aを指定", "selectFoldersKnowledgeBasesAndDocuments": "フォルダ、ナレッジベース、ドキュメントを選択", "selectKb": "ナレッジベースを選択", "selectKbsAndDocuments": "ナレッジベースとドキュメントを選択", "selectKnowledgeBase": "ナレッジベースを選択", "selectKnowledgeBasesOrDocuments": "ナレッジベースまたはドキュメントを選択", "selectQnA": "Q&Aを選択", "selectVariable": "変数を選択", "setResultToVariable": "結果を変数に設定", "setResultToVariableOnly": "結果を変数に設定のみ", "showAdvancedSetting": "詳細設定を表示", "showReferenceArticles": "参照元記事を表示する", "showRelatedArticle": "記事リストを表示", "showResultToUser": "結果をユーザーに表示", "showSelectedArticle": "表示する記事を指定", "similarityThresholdSetting": "類似度のしきい値設定", "sortBy": "ソート", "sortByPlaceholder": "表示順の設定", "streamingSupport": "ストリーミングサポート", "title": "記事ナレッジベース", "titleOfArticle": "記事のタイトル", "typeId": "ID (または変数) を入力", "typeKbId": "KB ID (または変数) を入力", "userFeedback": "ユーザーのフィードバック数", "useVariableForGenerating": "変数の値で生成する", "useVariableForSearching": "変数の値で検索する", "value": "値", "variable": "変数", "viewCount": "ユーザーの閲覧数"}, "liveChatNode": {"enableTeamSelection": "チーム選択を有効にする", "exceedingQueue": "キューを超えています", "failedToConnect": "接続できなかった場合", "finishingLivechat": "終了時", "searchTeamName": "チーム名を検索", "selectAll": "すべて選択", "selectTeamToShow": "表示するチームを選択", "selectTeamToShowDescription": "チーム未選択時は『接続失敗』に進みます", "storeHistory": "有人チャットの会話履歴をシステム変数に保存する", "successfulConnected": "有人チャット接続", "teamSelectionText": "テキスト", "teamSelectionTextDescription": "※LINEでは、ボタンのみを表示できません。そのため、テキスト設定がない場合はデフォルトテキストが自動送信されます。", "teamSelectionTextPlaceholder": "チームを選択してください", "title": "有人チャット接続"}, "save": "保存", "scriptNode": {"default": "デフォルト", "fail": "失敗", "pathNameMustBeUnique": "パス名は一意でなければなりません", "paths": "パス", "script": "スクリプト", "variable": "変数", "variablePlaceholder": "変数を選択"}, "setVariableNode": {"enterValue": "値を入力", "label": "カード名変更", "name": "変数に入力する値を設定", "placeholder": "変数に入力", "selectVariable": "変数を選択", "value": "値", "variable": "変数", "variableLabel": "変数"}, "tableNode": {"base": "ベース", "basePlaceholder": "ベースを選択", "column": "項目名", "columnTable": "項目ID", "decimalPlaces": "{places} 桁の小数を入力してください", "nameTable": "項目名", "notFoundRecords": "テーブル詳細が見つかりません", "orderBy": "ソート (Order By)", "orderByPlaceholder": "ソート条件を入力", "recordId": "レコードID", "recordIdPlaceholder": "IDまたは変数を入力", "searchCondition": "検索条件 (JSON形式)", "searchConditionPlaceholder": "検索条件を入力", "searchPlaceholder": "項目を検索", "table": "テーブル", "tableDetail": "テーブル詳細", "tablePlaceholder": "テーブルを選択", "updateProperty": "更新する項目", "validate": "設定内容の検証", "variable": "変数に値を入力", "variablePlaceholder": "変数を選択"}, "templateNode": {"altText": "代替テキスト", "altTextPlaceholder": "端末の通知やトークリスト、引用メッセージでFlex Messageの代替として表示されます。", "customTemplate": "カスタムテンプレート", "customTemplatePlaceholder": "カスタムテンプレートを入力", "fallbackPath": "未対応プラットフォームの場合", "linePlatform": "LINE Flexメッセージ", "linePlatformDecs": "Flex Message Simulatorで作成・テストしたデータを\nここに貼り付けてください。", "platform": "プラットフォーム選択", "platformPlaceholder": "プラットフォームを選択", "title": "カスタムテンプレート"}, "text": "テキストメッセージ", "textPlaceholder": "ユーザーに表示するテキストメッセージを入力", "title": "タイトル", "tree": {"root": "すべてのナレッジベース"}, "validationMessages": {"invalidURL": "無効なURL形式です。有効なURLを入力してください。プロトコル（例：http://またはhttps://）と正しいドメインを含めてください"}}