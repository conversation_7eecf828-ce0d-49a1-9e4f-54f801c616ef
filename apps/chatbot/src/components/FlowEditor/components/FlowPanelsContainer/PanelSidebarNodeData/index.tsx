import { rem } from '@mantine/core';
import { createStyles } from '@mantine/emotion';
import { useElementSize } from '@mantine/hooks';
import { useCallback, useRef, useState } from 'react';

import { FLOW_OFFSET_LEFT, FLOW_OFFSET_TOP } from '@/constants/flow';
import { useFlowContext } from '@/contexts/FlowContext';
import { ENodeType, type TReactFlowNode } from '@/types';
import {
  APINodeSettings,
  ActionNodeSettings,
  BlockNodeSettings,
  ButtonNodeSettings,
  CaptureInputNodeSettings,
  CaptureIntentNodeSettings,
  CarouselNodeSettings,
  ConditionNodeSettings,
  CustomTemplateNodeSettings,
  DynamicNodeSettings,
  EmailNodeSettings,
  EntityNodeSettings,
  FormNodeSettings,
  GaiNodeSettings,
  IntentNodeSettings,
  LivechatNodeSettings,
  QnANodeSettings,
  ScriptNodeSettings,
  SetVariableNodeSettings,
  TableNodeSettings,
  TextNodeSettings,
} from '../../../nodes';
import SettingsSkeleton from './SettingsSkeleton';

const useStyles = createStyles((theme) => ({
  container: {
    marginLeft: rem(FLOW_OFFSET_LEFT),
    marginTop: rem(FLOW_OFFSET_TOP),
    position: 'absolute',
    right: 0,
    height: 'inherit',
    backgroundColor: theme.colors.silverFox[0],
    transform: 'translateX(100%)',
    transition: 'transform 0.2s ease-in-out',
    padding: rem(16),
    scrollbarWidth: 'thin',
    boxShadow: theme.shadows.sm,
  },
  open: {
    transform: 'translateX(0)',
  },
  resizeHandle: {
    width: rem(2),
    cursor: ' col-resize' /* The key for UX! */,
    flexShrink: 0,
    height: '100%',
    position: 'absolute',
    left: 0,
    bottom: 0,
    zIndex: 1000,
    transition: 'background-color 0.4s ease',
    '&:hover': {
      backgroundColor: theme.colors.decaBlue?.[5],
    },
  },
}));
const MIN_WIDTH = 360;
const MAX_WIDTH = 600;

const PanelSidebarNodeData = () => {
  const { classes, cx } = useStyles();
  const { ref: panelRef, height: panelHeight } = useElementSize();
  const [sidebarWidth, setSidebarWidth] = useState(MIN_WIDTH);
  const isResizing = useRef(false);

  const { isOpeningSettingDrawer, selectedNode, reactFlowInstance, isFlowEdited } =
    useFlowContext();

  const updateStepData = useCallback(
    (data: TReactFlowNode['data']['properties']) => {
      if (!selectedNode || !reactFlowInstance) return;
      isFlowEdited.current = true;
      if (data?.setting?.color) {
        const currentNode = reactFlowInstance?.getNode(selectedNode.id);
        data.setting.color = currentNode?.data.properties.setting.color;
      }
      reactFlowInstance.updateNodeData(selectedNode.id, () => ({
        properties: data,
      }));
    },
    [selectedNode, reactFlowInstance]
  );

  const nodeSettingByType = useCallback(() => {
    if (!selectedNode?.type) return <SettingsSkeleton />;
    const NodeSettingComponent = NodeSettings[selectedNode.type];
    return (
      NodeSettingComponent && (
        <NodeSettingComponent
          node={selectedNode}
          panelHeight={panelHeight}
          submit={updateStepData}
        />
      )
    );
  }, [selectedNode, panelHeight, updateStepData]);

  const handleMouseMove = useCallback((e) => {
    if (!isResizing.current) {
      return;
    }

    setSidebarWidth((prev) => Math.max(MIN_WIDTH, Math.min(MAX_WIDTH, prev - e.movementX)));
  }, []);

  const handleMouseUp = useCallback(() => {
    isResizing.current = false;
    window.removeEventListener('mousemove', handleMouseMove);
    window.removeEventListener('mouseup', handleMouseUp);
  }, [handleMouseMove]);

  const handleMouseDown = useCallback(
    (e: React.MouseEvent) => {
      e.preventDefault();
      e.stopPropagation();
      isResizing.current = true;
      window.addEventListener('mousemove', handleMouseMove);
      window.addEventListener('mouseup', handleMouseUp);
    },
    [handleMouseMove, handleMouseUp]
  );

  return (
    <div
      ref={panelRef}
      style={{ width: rem(sidebarWidth) }}
      className={cx(classes.container, isOpeningSettingDrawer && classes.open)}
    >
      <div className={classes.resizeHandle} onMouseDown={handleMouseDown} />
      {nodeSettingByType()}
    </div>
  );
};

export default PanelSidebarNodeData;

// Node type to settings component mapping
const NodeSettings = {
  [ENodeType.Email]: EmailNodeSettings,
  [ENodeType.Form]: FormNodeSettings,
  [ENodeType.Carousel]: CarouselNodeSettings,
  [ENodeType.Text]: TextNodeSettings,
  [ENodeType.Buttons]: ButtonNodeSettings,
  [ENodeType.Condition]: ConditionNodeSettings,
  [ENodeType.QnA]: QnANodeSettings,
  [ENodeType.Capture]: CaptureInputNodeSettings,
  [ENodeType.Variables]: SetVariableNodeSettings,
  [ENodeType.Action]: ActionNodeSettings,
  [ENodeType.Api]: APINodeSettings,
  [ENodeType.Livechat]: LivechatNodeSettings,
  [ENodeType.Intent]: IntentNodeSettings,
  [ENodeType.Table]: TableNodeSettings,
  [ENodeType.Entity]: EntityNodeSettings,
  [ENodeType.CustomTemplate]: CustomTemplateNodeSettings,
  [ENodeType.Gai]: GaiNodeSettings,
  [ENodeType.Script]: ScriptNodeSettings,
  [ENodeType.Dynamic]: DynamicNodeSettings,
  [ENodeType.Block]: BlockNodeSettings,
  [ENodeType.Choice]: CaptureIntentNodeSettings,
};
