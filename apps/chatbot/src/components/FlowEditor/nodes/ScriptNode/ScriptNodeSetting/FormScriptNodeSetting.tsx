import { useFlowContext } from '@/contexts';
import { useButtonSettingStyles } from '@/styles/useButtonSettingStyles';
import type { TReactFlowNode, TScriptStepData } from '@/types';
import { Alert, Box, Divider, Flex, Modal, rem } from '@mantine/core';
import { Editor } from '@monaco-editor/react';
import { DecaSelectRHF } from '@resola-ai/ui';
import { IconAlertCircle, IconDatabase } from '@tabler/icons-react';
import { useTranslate } from '@tolgee/react';
import { useCallback } from 'react';
import { useFormContext } from 'react-hook-form';
import PathList from './PathList';

const FormScriptNodeSetting = ({
  node,
  onSubmit,
  openModal,
  setOpenModal,
}: {
  node: TReactFlowNode;
  onSubmit: () => void;
  openModal: boolean;
  setOpenModal: (openModal: boolean) => void;
}) => {
  const { classes } = useButtonSettingStyles();
  const { t } = useTranslate('nodeSetting');
  const { variables } = useFlowContext();
  const { control, setValue, watch } = useFormContext<TScriptStepData>();
  const paths = watch('paths');
  const initialText = watch('script');

  const handleChangeScript = useCallback(
    (value?: string) => {
      setValue('script', value || '');
    },
    [setValue]
  );

  const hasDuplicateName = () => {
    const names = paths?.map((field) => field.name) || [];
    return new Set(names).size !== names.length;
  };

  const generateEditor = useCallback(
    (height: string) => {
      return (
        <Editor
          height={height}
          defaultLanguage='javascript'
          value={initialText}
          onChange={handleChangeScript}
          options={{
            minimap: {
              enabled: false,
            },
          }}
          wrapperProps={{
            onBlur: onSubmit,
          }}
        />
      );
    },
    [initialText, handleChangeScript, onSubmit]
  );

  return (
    <Box component='section' className={classes.content}>
      <Flex direction='column' gap={rem(12)}>
        {generateEditor('50vh')}
        <Divider />
        <DecaSelectRHF
          searchable
          className={classes.fieldControl}
          label={t('formNode.variableFieldLabel')}
          placeholder={t('formNode.variableFieldPlaceholder')}
          control={control}
          name={'variableName'}
          data={variables.map(({ name }) => name)}
          leftSection={<IconDatabase size={18} />}
          onBlur={() => onSubmit()}
        />
        <PathList node={node} onSubmit={onSubmit} />
        {!!paths.length && hasDuplicateName() && (
          <Alert color='red' icon={<IconAlertCircle size={16} />}>
            {t('scriptNode.pathNameMustBeUnique')}
          </Alert>
        )}
      </Flex>
      <Modal
        opened={openModal}
        onClose={() => setOpenModal(false)}
        title={t('scriptNode.script')}
        size='xl'
      >
        {generateEditor('85vh')}
      </Modal>
    </Box>
  );
};

export default FormScriptNodeSetting;
