import { FADE_RIGHT_IN } from '@/constants/flow';
import { useFlowContext } from '@/contexts';
import withNodeSettings from '@/hocs/withNodeSettings';
import { scriptSchema } from '@/schemas';
import { useButtonSettingStyles } from '@/styles/useButtonSettingStyles';
import type { ENodeType, TSidebarNodeSetting } from '@/types';
import { zodResolver } from '@hookform/resolvers/zod';
import { ActionIcon, Box, Flex, Text, Transition } from '@mantine/core';
import { IconMaximize } from '@tabler/icons-react';
import { useTranslate } from '@tolgee/react';
import { memo, useCallback, useEffect, useState } from 'react';
import { FormProvider, useForm } from 'react-hook-form';
import FormScriptNodeSetting from './FormScriptNodeSetting';

const ScriptNodeSettings: React.FC<TSidebarNodeSetting<ENodeType.Script>> = ({ node, submit }) => {
  const { classes } = useButtonSettingStyles();
  const { t } = useTranslate('nodeSetting');
  const { openSettingDrawer } = useFlowContext();
  const [openModal, setOpenModal] = useState(false);

  const methods = useForm({
    defaultValues: node.data.properties,
    mode: 'onBlur',
    resolver: zodResolver(scriptSchema),
  });
  const { getValues, reset } = methods;

  useEffect(() => {
    // Add delay to wait for new setting to be mounted, and avoid update old setting
    setTimeout(() => {
      reset({
        ...node.data.properties,
        paths: node.data.properties.paths || [],
      });
    }, 0);
  }, [node.id]);

  const handleSubmit = useCallback(() => {
    const values = getValues();

    submit(values);
  }, [getValues, submit]);

  useEffect(
    () => () => {
      handleSubmit();
    },
    []
  );

  return (
    <Transition
      mounted={openSettingDrawer}
      transition={FADE_RIGHT_IN}
      duration={300}
      timingFunction={'ease'}
    >
      {(styles) => (
        <Box component='section' className={classes.root} style={styles}>
          <FormProvider {...methods}>
            <Flex align='center' justify='space-between' className={classes.header}>
              <Flex align='center'>
                <Text className={classes.title}>{t('scriptNode.script')}</Text>
              </Flex>
              <Box>
                <ActionIcon variant='transparent' size='sm' onClick={() => setOpenModal(true)}>
                  <IconMaximize size={20} />
                </ActionIcon>
              </Box>
            </Flex>
            <FormScriptNodeSetting
              node={node}
              onSubmit={handleSubmit}
              openModal={openModal}
              setOpenModal={setOpenModal}
            />
          </FormProvider>
        </Box>
      )}
    </Transition>
  );
};

export default memo(withNodeSettings(ScriptNodeSettings));
