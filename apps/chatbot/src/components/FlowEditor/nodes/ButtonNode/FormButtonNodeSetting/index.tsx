import { useFlowContext } from '@/contexts';
import { tolgee } from '@/services/tolgee';
import { useButtonSettingStyles } from '@/styles/useButtonSettingStyles';
import { Box, Divider, Flex, Text, rem } from '@mantine/core';
import { BlockNoteMarkdown } from '@resola-ai/blocknote-editor';
import { useTranslate } from '@tolgee/react';
import { useCallback, useMemo } from 'react';
import { useFormContext } from 'react-hook-form';
import { Switch } from 'react-hook-form-mantine';
import ButtonList from '../../../components/ButtonList';
import { SetResultToVariableForm } from './SetResultToVariableForm';

const FormButtonNodeSetting = ({ node, onSubmit }) => {
  const { classes } = useButtonSettingStyles();
  const { t } = useTranslate('nodeSetting');
  const { onPortToggleElse } = useFlowContext();
  const { control, getValues, setValue, watch } = useFormContext();

  const onToggleSearchIntent = useCallback(() => {
    const values = getValues();
    if (values.canSearchIntent) return onSubmit();
    onPortToggleElse(node, onSubmit);
  }, [getValues, onSubmit, onPortToggleElse, node]);

  const onBlockNoteChange = useCallback(
    (markdown: string) => {
      setValue('text', markdown);
    },
    [setValue]
  );

  const initialText = useMemo(() => getValues('text'), [getValues]);

  const canSearchIntent = watch('canSearchIntent');

  return (
    <Box component='section' className={classes.content}>
      <Flex direction='column' gap={rem(12)}>
        <Flex direction='column' gap={rem(4)}>
          <Text fz={rem(14)} fw={500}>
            {t('btnNode.textButtons')}
          </Text>
          <Text fz={rem(12)} color='dimmed'>
            {t('btnNode.textButtonsDescription')}
          </Text>
          <BlockNoteMarkdown
            className={classes.editor}
            initialMarkdown={initialText}
            language={tolgee.getLanguage()}
            onChange={onBlockNoteChange}
            onBlur={onSubmit}
          />
        </Flex>
        <Divider />
        <ButtonList node={node} onSubmit={onSubmit} />
        <Divider />
        <Flex justify='space-between'>
          <Text fz={rem(14)} fw={500}>
            {t('btnNode.searchIntent')}
          </Text>
          <Switch name='canSearchIntent' control={control} onChange={onToggleSearchIntent} />
        </Flex>
        {canSearchIntent && <SetResultToVariableForm onSubmit={onSubmit} />}
      </Flex>
    </Box>
  );
};

export default FormButtonNodeSetting;
