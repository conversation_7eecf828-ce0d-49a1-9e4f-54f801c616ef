import { useFlowContext } from '@/contexts/FlowContext';
import { Flex, Stack, Text, rem } from '@mantine/core';
import { DecaSelectRHF } from '@resola-ai/ui';
import { IconDatabase } from '@tabler/icons-react';
import { useTranslate } from '@tolgee/react';
import { useFormContext } from 'react-hook-form';
import { Switch } from 'react-hook-form-mantine';

interface SetResultToVariableFormProps {
  onSubmit: () => void;
}

export const SetResultToVariableForm = ({ onSubmit }: SetResultToVariableFormProps) => {
  const { t } = useTranslate('nodeSetting');
  const { variables } = useFlowContext();

  const { control, watch } = useFormContext();
  const useVariable = watch('useVariable');

  return (
    <>
      <Stack gap={rem(4)}>
        <Flex justify={'space-between'} align={'center'}>
          <Text fz={rem(14)} fw={500}>
            {t('btnNode.setResultToVariable')}
          </Text>
          <Switch name='useVariable' control={control} onChange={onSubmit} />
        </Flex>

        <Text fz={rem(12)} c='dimmed'>
          {t('btnNode.setResultToVariableDescription')}
        </Text>
      </Stack>

      {useVariable && (
        <Stack gap='lg'>
          <DecaSelectRHF
            searchable
            placeholder={t('kbNode.selectVariable')}
            control={control}
            name='variableName'
            data={variables.map(({ name }) => name)}
            leftSection={<IconDatabase size={18} />}
            onChange={onSubmit}
          />
        </Stack>
      )}
    </>
  );
};
