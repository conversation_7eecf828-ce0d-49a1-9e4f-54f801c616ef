import { ActionIcon, Divider, Flex, Modal, Text, rem } from '@mantine/core';
import { createStyles } from '@mantine/emotion';
import { Editor } from '@monaco-editor/react';
import { IconMaximize, IconMinimize } from '@tabler/icons-react';
import { useTranslate } from '@tolgee/react';
import { useCallback, useState } from 'react';
import { useFormContext } from 'react-hook-form';

type FormSearchConditionProps = {
  onBlur: () => void;
};

const useStyles = createStyles((theme) => ({
  editor: {
    border: `1px solid ${theme.colors.gray[3]}`,
    borderRadius: theme.radius.md,
    padding: theme.spacing.md,
  },
}));

const FormSearchCondition = ({ onBlur }: FormSearchConditionProps) => {
  const { t } = useTranslate('nodeSetting');
  const { classes } = useStyles();
  const { setValue, watch } = useFormContext();
  const [openModal, setOpenModal] = useState(false);
  const searchCondition = watch('payload.searchCondition') || '';

  const handleChangeScript = useCallback(
    (value?: string) => {
      setValue('payload.searchCondition', value || '');
    },
    [setValue]
  );

  const generateEditor = useCallback(
    (height: string) => {
      return (
        <Editor
          className={classes.editor}
          height={height}
          defaultLanguage='json'
          value={searchCondition}
          onChange={handleChangeScript}
          options={{
            minimap: {
              enabled: false,
            },
          }}
          wrapperProps={{
            onBlur,
          }}
        />
      );
    },
    [searchCondition, handleChangeScript, onBlur]
  );

  return (
    <>
      <Flex w='100%' justify='space-between'>
        <Text fz={rem(14)} fw={'500'}>
          {t('tableNode.searchCondition')}
        </Text>
        <ActionIcon variant='transparent' size='sm' onClick={() => setOpenModal(true)}>
          <IconMaximize size={20} />
        </ActionIcon>
      </Flex>
      <Divider />
      {generateEditor('25vh')}
      <Modal
        opened={openModal}
        onClose={() => setOpenModal(false)}
        title={
          <Text fz={rem(16)} fw={'500'}>
            {t('tableNode.searchCondition')}
          </Text>
        }
        size='xl'
        closeButtonProps={{
          icon: <IconMinimize size={20} />,
          onClick: () => setOpenModal(false),
        }}
      >
        {generateEditor('85vh')}
      </Modal>
    </>
  );
};

export default FormSearchCondition;
