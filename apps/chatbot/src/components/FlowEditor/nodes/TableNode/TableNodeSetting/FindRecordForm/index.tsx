import { useFlowContext } from '@/contexts';
import { <PERSON><PERSON>, <PERSON><PERSON>r, Flex, rem } from '@mantine/core';
import { DecaSelectRHF } from '@resola-ai/ui';
import { useTranslate } from '@tolgee/react';
import { useState } from 'react';
import { useFormContext } from 'react-hook-form';
import { TextInput } from 'react-hook-form-mantine';
import TableDetailModal from '../TableDetailModal';
import ValidateModal from '../ValidateModal';
import useTableSettingForm from '../useTableSettingForm';
import FormSearchCondition from './FormSearchCondition';

type FindRecordFormProps = {
  onBlur: () => void;
};

const FindRecordForm = ({ onBlur }: FindRecordFormProps) => {
  const { t } = useTranslate('nodeSetting');
  const [openTableDetailModal, setOpenTableDetailModal] = useState(false);
  const [openValidateModal, setOpenValidateModal] = useState(false);
  const { variables } = useFlowContext();
  const { control, watch } = useFormContext();
  const watchBase = watch('baseId');
  const watchTable = watch('tableId');

  const { tables, baseOptions, tableOptions, onChangeBase, onChangeTable } = useTableSettingForm({
    onBlur,
  });

  return (
    <Flex direction='column' gap={rem(12)}>
      <DecaSelectRHF
        control={control}
        name='baseId'
        data={baseOptions}
        searchable
        label={t('tableNode.base')}
        placeholder={t('tableNode.basePlaceholder')}
        onChange={(value) => value && onChangeBase(value)}
      />
      <DecaSelectRHF
        control={control}
        name='tableId'
        data={tableOptions}
        disabled={!watchBase}
        searchable
        label={t('tableNode.table')}
        placeholder={t('tableNode.tablePlaceholder')}
        onChange={(value) => value && onChangeTable(value)}
      />
      <Button
        variant='outline'
        onClick={() => setOpenTableDetailModal(true)}
        disabled={!(watchBase && watchTable)}
      >
        {t('tableNode.tableDetail')}
      </Button>
      <Divider />
      <FormSearchCondition onBlur={onBlur} />
      <TextInput
        control={control}
        name='payload.orderBy'
        label={t('tableNode.orderBy')}
        placeholder={t('tableNode.orderByPlaceholder')}
        onBlur={onBlur}
      />
      <Divider />
      <DecaSelectRHF
        searchable
        control={control}
        name='payload.variable'
        data={variables.map(({ name }) => name)}
        label={t('tableNode.variable')}
        placeholder={t('tableNode.variablePlaceholder')}
        onBlur={onBlur}
      />
      <Button variant='outline' onClick={() => setOpenValidateModal(true)}>
        {t('tableNode.validate')}
      </Button>
      {watchBase && watchTable && (
        <TableDetailModal
          opened={openTableDetailModal}
          close={() => setOpenTableDetailModal(false)}
          tables={tables}
          tableId={watchTable}
        />
      )}
      <ValidateModal opened={openValidateModal} close={() => setOpenValidateModal(false)} />
    </Flex>
  );
};

export default FindRecordForm;
