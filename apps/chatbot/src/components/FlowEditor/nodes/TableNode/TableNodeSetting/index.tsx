import { Box, Divider, Text, Transition, rem } from '@mantine/core';
import { createStyles } from '@mantine/emotion';
import { useTranslate } from '@tolgee/react';
import { useEffect, useMemo } from 'react';
import { FormProvider, useForm } from 'react-hook-form';

import { FADE_RIGHT_IN } from '@/constants/flow';
import { useFlowContext } from '@/contexts/FlowContext';
import type { ENodeType, TSidebarNodeSetting, TTableData } from '@/types';
import { TableOperation, TextTable } from '@/types/nodes/table';
import get from 'lodash/get';
import omit from 'lodash/omit';
import DeleteRecordForm from './DeleteRecordForm';
import FindRecordForm from './FindRecordForm';
import InsertRecordForm from './InsertRecordForm';
import UpdateRecordForm from './UpdateRecordForm';

const useStyles = createStyles((theme) => ({
  container: {
    display: 'flex',
    flexDirection: 'column',
    gap: rem(12),
    overflow: 'auto',
    maxHeight: 'calc(100vh - 8rem)',
    '&::-webkit-scrollbar': {
      display: 'none',
      width: 0,
    },
    paddingBottom: rem(32),
  },
  title: {
    fontSize: theme.fontSizes.xl,
    fontWeight: 700,
    lineHeight: rem(31),
  },
}));

const generateForm = (stepData: TTableData, onBlur: () => void) => {
  const type = stepData.operation;
  switch (type) {
    case TableOperation.FindRecord:
      return <FindRecordForm onBlur={onBlur} />;
    case TableOperation.InsertRecord:
      return <InsertRecordForm onBlur={onBlur} />;
    case TableOperation.UpdateRecord:
      return <UpdateRecordForm onBlur={onBlur} />;
    case TableOperation.DeleteRecord:
      return <DeleteRecordForm onBlur={onBlur} />;

    default:
      break;
  }
};

const TableNodeSettings: React.FC<TSidebarNodeSetting<ENodeType.Table>> = ({ node, submit }) => {
  const { classes } = useStyles();
  const { t } = useTranslate('flowEditor');
  const { openSettingDrawer } = useFlowContext();

  const methods = useForm({
    mode: 'onBlur',
    defaultValues: node.data.properties,
  });
  const { getValues, reset, handleSubmit } = methods;

  const onBlur = () => {
    onSubmit();
  };

  const onSubmit = () => {
    const values = getValues();

    submit(omit(values, 'base', 'table'));
  };

  const nodeData = useMemo(() => {
    return node.data.properties as TTableData;
  }, [node.id]);

  useEffect(() => {
    // Add delay to wait for new setting to be mounted, and avoid update old setting
    setTimeout(() => {
      reset({
        ...nodeData,
        baseId: nodeData.baseId || '',
        tableId: nodeData.tableId || '',
      });
    }, 0);
  }, [nodeData]);

  useEffect(
    () => () => {
      onSubmit();
    },
    []
  );

  return (
    <Transition
      mounted={openSettingDrawer}
      transition={FADE_RIGHT_IN}
      duration={300}
      timingFunction={'ease'}
    >
      {(styles) => (
        <FormProvider {...methods}>
          <form style={styles} onSubmit={handleSubmit(onSubmit)}>
            <Box className={classes.container}>
              <Text className={classes.title}>{t(TextTable[get(nodeData, 'operation', '')])}</Text>
              <Divider />
              {generateForm(nodeData as TTableData, onBlur)}
            </Box>
          </form>
        </FormProvider>
      )}
    </Transition>
  );
};

export default TableNodeSettings;
