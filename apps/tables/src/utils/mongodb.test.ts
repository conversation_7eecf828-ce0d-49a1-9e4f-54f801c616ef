import { Field, FieldType, NumberFormatOption } from '@/types';
import * as reactQueryBuilder from 'react-querybuilder';
import { vi } from 'vitest';
import * as mongodb from './mongodb';

vi.mock('react-querybuilder', () => ({
  formatQuery: vi.fn(() => '{"rules":[]}'),
  remove: vi.fn((group, path) => {
    const newGroup = JSON.parse(JSON.stringify(group));
    if (path.length === 1) {
      newGroup.rules.splice(path[0], 1);
    }
    return newGroup;
  }),
}));

vi.mock('react-querybuilder/dist/parseMongoDB', () => ({
  parseMongoDB: vi.fn(query => ({ parsed: query })),
}));

const fieldA: Field = {
  id: 'a',
  name: 'Field A',
  description: 'Test field A',
  system: false,
  required: false,
  isPrimary: false,
  type: FieldType.TEXT,
  options: null,
};

const fieldB: Field = {
  id: 'b',
  name: 'Field B',
  description: 'Test field B',
  system: false,
  required: false,
  isPrimary: false,
  type: FieldType.NUMBER,
  options: { numberFormat: NumberFormatOption.INTEGER },
};

const allowedFields: Field[] = [fieldA, fieldB];
const restrictedFieldTypes = ['restricted'];

afterEach(() => {
  vi.restoreAllMocks();
});

describe('sanitizeRuleGroup', () => {
  it('removes rules with restricted field types', () => {
    const group = {
      combinator: 'and',
      rules: [
        { field: 'a', operator: '=', value: 1 },
        { field: 'b', operator: '=', value: 2 },
        { field: 'c', operator: '=', value: 3 }, // not in allowedFields
      ],
    };
    const result = mongodb.sanitizeRuleGroup(group, allowedFields, restrictedFieldTypes);
    expect(result.rules.length).toBe(2);
  });
  it('returns group unchanged if no rules', () => {
    const group = { combinator: 'and', rules: [] };
    expect(mongodb.sanitizeRuleGroup(group, allowedFields, restrictedFieldTypes)).toEqual(group);
  });
  it('returns group unchanged when no paths to remove', () => {
    const group = {
      combinator: 'and',
      rules: [
        { field: 'a', operator: '=', value: 1 },
        { field: 'b', operator: '=', value: 2 },
      ],
    };
    const result = mongodb.sanitizeRuleGroup(group, allowedFields, restrictedFieldTypes);
    expect(result).toEqual(group);
  });
});

describe('parseMongoDBQuery', () => {
  it('calls parseMongoDB', () => {
    const query = { foo: 1 };
    const result = mongodb.parseMongoDBQuery(query);
    expect(result).toEqual({ parsed: { foo: 1 } });
  });
});

describe('formatMongoDBQuery', () => {
  it('returns empty object if no rules', () => {
    expect(mongodb.formatMongoDBQuery({ combinator: 'and', rules: [] })).toEqual({});
  });
  it('formats query if rules exist', () => {
    const group = { combinator: 'and', rules: [{ field: 'a', operator: '=', value: 1 }] };
    (reactQueryBuilder.formatQuery as any).mockReturnValue(
      '{"rules":[{"field":"a","operator":"=","value":1}]}'
    );
    expect(mongodb.formatMongoDBQuery(group)).toEqual({
      rules: [{ field: 'a', operator: '=', value: 1 }],
    });
  });
});

describe('findInvalidRulePaths and removeEmptyRuleGroups', () => {
  it('finds invalid paths in nested groups', () => {
    const group = {
      combinator: 'and',
      rules: [
        { field: 'a', operator: '=', value: 1 },
        {
          combinator: 'or',
          rules: [
            { field: 'c', operator: '=', value: 2 },
            { field: 'b', operator: '=', value: 3 },
          ],
        },
      ],
    };
    // c is not allowed, so should find [1,0]
    const paths = mongodb.findInvalidRulePaths(group, allowedFields, restrictedFieldTypes);
    expect(paths).toContainEqual([1, 0]);
  });
  it('returns empty array when ruleGroup has no rules', () => {
    const group = { combinator: 'and', rules: [] };
    const paths = mongodb.findInvalidRulePaths(group, allowedFields, restrictedFieldTypes);
    expect(paths).toEqual([]);
  });
  it('returns empty array when ruleGroup is null/undefined', () => {
    const paths1 = mongodb.findInvalidRulePaths(null as any, allowedFields, restrictedFieldTypes);
    expect(paths1).toEqual([]);
    
    const paths2 = mongodb.findInvalidRulePaths(undefined as any, allowedFields, restrictedFieldTypes);
    expect(paths2).toEqual([]);
  });
  it('removes empty rule groups', () => {
    const group = {
      combinator: 'and',
      rules: [
        {
          combinator: 'or',
          rules: [],
        },
        { field: 'a', operator: '=', value: 1 },
      ],
    };
    const cleaned = mongodb.removeEmptyRuleGroups(group);
    expect(cleaned.rules.length).toBe(1);
    const onlyRule = cleaned.rules[0];
    expect('field' in onlyRule ? onlyRule.field : undefined).toBe('a');
  });
  it('handles null/undefined ruleGroup in removeEmptyRuleGroups', () => {
    const result1 = mongodb.removeEmptyRuleGroups(null as any);
    expect(result1).toEqual({ combinator: 'and', rules: [] });
    
    const result2 = mongodb.removeEmptyRuleGroups(undefined as any);
    expect(result2).toEqual({ combinator: 'and', rules: [] });
  });
  it('handles ruleGroup with null rules property', () => {
    const group = { combinator: 'and', rules: null } as any;
    const result = mongodb.removeEmptyRuleGroups(group);
    expect(result).toEqual({ combinator: 'and', rules: [] });
  });
  it('handles ruleGroup where all rules are filtered out', () => {
    const group = {
      combinator: 'and',
      rules: [
        {
          combinator: 'or',
          rules: [],
        },
        {
          combinator: 'and',
          rules: [],
        },
      ],
    };
    const result = mongodb.removeEmptyRuleGroups(group);
    expect(result).toEqual({ combinator: 'and', rules: [] });
  });
  it('handles ruleGroup with undefined rules after processing', () => {
    // Create a scenario where rules might become undefined
    const group = { combinator: 'and', rules: [] };
    const result = mongodb.removeEmptyRuleGroups(group);
    expect(result).toEqual({ combinator: 'and', rules: [] });
  });
  it('handles edge case where rules becomes falsy during processing', () => {
    // Create a group that will result in all rules being filtered out
    const group = {
      combinator: 'and',
      rules: [
        {
          combinator: 'or',
          rules: [],
        },
      ],
    };
    const result = mongodb.removeEmptyRuleGroups(group);
    expect(result).toEqual({ combinator: 'and', rules: [] });
  });
});

describe('formatMongoDBQuery error handling', () => {
  it('throws if JSON.parse fails', () => {
    const group = { combinator: 'and', rules: [{ field: 'a', operator: '=', value: 1 }] };
    vi.spyOn(reactQueryBuilder, 'formatQuery').mockReturnValue('not-json');
    expect(() => mongodb.formatMongoDBQuery(group)).toThrow();
  });
  it('covers error branch for JSON.parse', () => {
    const group = { combinator: 'and', rules: [{ field: 'a', operator: '=', value: 1 }] };
    vi.spyOn(reactQueryBuilder, 'formatQuery').mockReturnValue('{bad json');
    expect(() => mongodb.formatMongoDBQuery(group)).toThrow();
  });
});

describe('formatMongoDBQuery validator function', () => {
  it('calls validator with query that has rules', () => {
    const group = { combinator: 'and', rules: [{ field: 'a', operator: '=', value: 1 }] };
    const mockFormatQuery = vi.spyOn(reactQueryBuilder, 'formatQuery');
    mockFormatQuery.mockReturnValue('{"rules":[{"field":"a","operator":"=","value":1}]}');
    
    mongodb.formatMongoDBQuery(group);
    
    expect(mockFormatQuery).toHaveBeenCalledWith(
      group,
      expect.objectContaining({
        format: 'mongodb',
        validator: expect.any(Function)
      })
    );
    
    // Test the validator function directly
    const validator = mockFormatQuery.mock.calls[0][1].validator;
    expect(validator!({ rules: [{ field: 'a', operator: '=', value: 1 }] })).toBe(true);
    expect(validator!({ rules: [] })).toBe(false);
  });
});
