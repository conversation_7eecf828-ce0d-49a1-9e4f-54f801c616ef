import { ViewAPI } from '@/services/api';
import { act, renderHook } from '@testing-library/react';
import useSWR, * as swr from 'swr';
import { vi } from 'vitest';
import { useTableViewsMutation, useTableViewsQuery, createViewsKey, isViewsKey } from './useTableViews';
import { Ok, Err } from 'ts-results-es';

vi.mock('swr', () => ({
  __esModule: true,
  default: vi.fn(),
  mutate: vi.fn(),
}));
vi.mock('@/services/api', () => ({
  ViewAPI: {
    getList: vi.fn(),
    create: vi.fn(),
    update: vi.fn(),
    remove: vi.fn(),
  },
}));
vi.mock('@tolgee/react', () => ({ useTranslate: () => ({ t: (x: string) => x }) }));
vi.mock('@/components/Common', () => ({ notifications: { show: vi.fn() } }));

describe('Key utilities', () => {
  it('creates cache keys with correct structure', () => {
    const key = createViewsKey('base1', 'table1');
    expect(key).toEqual(['bases/tables/views', 'base1', 'table1']);
  });

  it('validates cache keys correctly', () => {
    const validKey = ['bases/tables/views', 'base1', 'table1'];
    expect(isViewsKey(validKey, 'base1', 'table1')).toBe(true);
    expect(isViewsKey(validKey, 'base2', 'table1')).toBe(false);
    expect(isViewsKey(['wrong', 'structure'])).toBe(false);
    
    // Edge cases
    const keyWithUndefined = ['bases/tables/views', undefined, undefined];
    expect(isViewsKey(keyWithUndefined, undefined, undefined)).toBe(true);
  });
});

describe('useTableViewsQuery', () => {
  afterEach(() => {
    vi.clearAllMocks();
  });

  it('returns views data when query succeeds', () => {
    (useSWR as any).mockReturnValue({
      data: { data: [{ id: 'view1', name: 'Test View' }] },
      isLoading: false,
      isValidating: false,
      error: undefined,
    });
    const { result } = renderHook(() => useTableViewsQuery('baseId', 'tableId'));
    
    expect(result.current.views).toEqual([{ id: 'view1', name: 'Test View' }]);
    expect(result.current.isLoading).toBe(false);
    expect(result.current.error).toBeUndefined();
  });

  it('returns empty array when no data is available', () => {
    (useSWR as any).mockReturnValue({
      data: undefined,
      isLoading: false,
      isValidating: false,
      error: undefined,
    });
    const { result } = renderHook(() => useTableViewsQuery('baseId', 'tableId'));
    
    expect(result.current.views).toEqual([]);
  });

  it('handles query errors gracefully', () => {
    const testError = new Error('Query failed');
    (useSWR as any).mockReturnValue({
      data: undefined,
      isLoading: false,
      isValidating: false,
      error: testError,
    });
    const { result } = renderHook(() => useTableViewsQuery('baseId', 'tableId'));
    
    expect(result.current.error).toBe(testError);
  });
  it('executes success callbacks when provided', () => {
    const onSuccess = vi.fn();
    (useSWR as any).mockImplementation((_key, _fetcher, opts) => {
      if (opts?.onSuccess) opts.onSuccess({ data: [{ id: 'view1' }] });
      return { data: { data: [{ id: 'view1' }] }, isLoading: false, isValidating: false, error: undefined };
    });
    
    renderHook(() => useTableViewsQuery('baseId', 'tableId', { onSuccess }));
    expect(onSuccess).toHaveBeenCalledWith({ data: [{ id: 'view1' }] });
  });

  it('handles missing parameters gracefully', () => {
    let capturedKey = null;
    (useSWR as any).mockImplementation((key, _fetcher, _opts) => {
      capturedKey = key;
      return { data: undefined, isLoading: false, isValidating: false, error: undefined };
    });
    
    renderHook(() => useTableViewsQuery(undefined, 'tableId'));
    expect(capturedKey).toBeNull();
  });

});

describe('useTableViewsMutation', () => {
  afterEach(() => {
    vi.clearAllMocks();
  });

  it('creates new views successfully', async () => {
    (ViewAPI.create as any).mockResolvedValueOnce(Ok({ data: { id: 'new-view' } }));
    const { result } = renderHook(() => useTableViewsMutation());
    
    await act(async () => {
      await result.current.createView('baseId', 'tableId', { name: 'New View' });
    });
    
    expect(ViewAPI.create).toHaveBeenCalledWith('baseId', 'tableId', { name: 'New View' });
  });

  it('updates existing views successfully', async () => {
    (ViewAPI.update as any).mockResolvedValueOnce(Ok({ data: { id: 'view1' } }));
    (swr.mutate as any).mockImplementation(async (_matcher, updateFn) => {
      if (typeof updateFn === 'function') {
        await updateFn({ data: [{ id: 'view1' }] });
      }
    });
    
    const { result } = renderHook(() => useTableViewsMutation());
    
    await act(async () => {
      await result.current.updateView('baseId', 'tableId', 'view1', { name: 'Updated' });
    });
    
    expect(ViewAPI.update).toHaveBeenCalledWith('baseId', 'tableId', 'view1', { name: 'Updated' });
  });

  it('removes views successfully', async () => {
    (ViewAPI.remove as any).mockResolvedValueOnce(Ok({}));
    const { result } = renderHook(() => useTableViewsMutation());
    
    await act(async () => {
      await result.current.removeView('baseId', 'tableId', 'view1');
    });
    
    expect(ViewAPI.remove).toHaveBeenCalledWith('baseId', 'tableId', 'view1');
  });

  it('handles API errors gracefully', async () => {
    (ViewAPI.create as any).mockResolvedValueOnce(Err('Creation failed'));
    const { result } = renderHook(() => useTableViewsMutation());
    
    await act(async () => {
      await result.current.createView('baseId', 'tableId', { name: 'New View' });
    });
    
    expect(ViewAPI.create).toHaveBeenCalled();
  });

  it('handles optimistic updates and missing data scenarios', async () => {
    (ViewAPI.update as any).mockResolvedValueOnce(Ok({ data: { id: 'view1', name: 'Updated' } }));
    (swr.mutate as any).mockImplementation(async (_matcher, updateFn, options) => {
      if (options?.optimisticData) {
        // Test with undefined currentData (lines 153-154)
        const undefinedResult = options.optimisticData(undefined);
        expect(undefinedResult).toBeUndefined();
        
        // Test with valid currentData
        const currentData = { data: [{ id: 'view1', name: 'Original' }] };
        const optimisticResult = options.optimisticData(currentData);
        expect(optimisticResult.data[0].name).toBe('Updated');
      }
      if (typeof updateFn === 'function') {
        const currentData = { data: [{ id: 'view1', name: 'Original' }] };
        return await updateFn(currentData);
      }
    });

    const { result } = renderHook(() => useTableViewsMutation());
    
    await act(async () => {
      await result.current.updateView('baseId', 'tableId', 'view1', { name: 'Updated' }, { 
        optimisticUpdate: true 
      });
    });

    expect(ViewAPI.update).toHaveBeenCalled();
  });

  it('handles missing currentData in cache update', async () => {
    (ViewAPI.create as any).mockResolvedValueOnce(Ok({ data: { id: 'new-view' } }));
    (swr.mutate as any).mockImplementation(async (_matcher, updateFn) => {
      if (typeof updateFn === 'function') {
        // Call with undefined currentData to test lines 75-76
        const result = await updateFn(undefined);
        expect(result).toBeUndefined();
      }
    });

    const { result } = renderHook(() => useTableViewsMutation());
    
    await act(async () => {
      await result.current.createView('baseId', 'tableId', {});
    });
  });




  it('handles update logic with filter and ID matching', async () => {
    (ViewAPI.update as any).mockResolvedValueOnce(Ok({ data: { id: 'view1', name: 'Updated' } }));
    (swr.mutate as any).mockImplementation(async (_matcher, updateFn) => {
      if (typeof updateFn === 'function') {
        const currentData = { 
          data: [
            { id: 'view1', name: 'Target', filter: { old: 'filter' } },
            { id: 'view2', name: 'Other' } // This should remain unchanged (line 125)
          ] 
        };
        const result = await updateFn(currentData);
        
        // Verify filter logic (line 123) and ID matching
        expect(result.data[0].filter).toEqual({});
        expect(result.data[0].name).toBe('Updated');
        expect(result.data[1]).toEqual({ id: 'view2', name: 'Other' });
        
        return result;
      }
    });

    const { result } = renderHook(() => useTableViewsMutation());
    
    await act(async () => {
      await result.current.updateView('baseId', 'tableId', 'view1', { name: 'Updated' });
    });
  });

  it('handles exception catching in updateView', async () => {
    (swr.mutate as any).mockRejectedValueOnce(new Error('Mutation failed'));

    const consoleSpy = vi.spyOn(console, 'error').mockImplementation(() => {});

    const { result } = renderHook(() => useTableViewsMutation());
    
    let res;
    await act(async () => {
      res = await result.current.updateView('baseId', 'tableId', 'view1', { name: 'Updated' });
    });

    expect(consoleSpy).toHaveBeenCalledWith('Failed to update view:', expect.any(Error));
    expect(res.isErr()).toBe(true);

    consoleSpy.mockRestore();
  });


  // Comprehensive coverage tests for remaining edge cases
  it('covers comprehensive query and mutation scenarios', async () => {
    renderHook(() => useTableViewsQuery('baseId', 'tableId'));
    const { result: mutationResult } = renderHook(() => useTableViewsMutation());

    // Test fetcher success path execution (line 43)
    let capturedFetcher: any = null;
    (useSWR as any).mockImplementation((_key, fetcher, opts) => {
      capturedFetcher = fetcher;
      // Test onSuccess with null data and error notifications
      if (opts?.onSuccess) opts.onSuccess(null);
      if (opts?.onError) opts.onError(new Error('Test error'));
      return { data: undefined, isLoading: false, isValidating: false, error: undefined };
    });
    
    renderHook(() => useTableViewsQuery('baseId', 'tableId'));
    
    if (capturedFetcher) {
      // Test successful fetcher execution
      const mockData = { data: [{ id: 'view1', name: 'Test' }] };
      (ViewAPI.getList as any).mockResolvedValueOnce(Ok(mockData));
      const result = await capturedFetcher!('test-key');
      expect(result).toEqual(mockData);

      // Test error paths
      (ViewAPI.getList as any).mockResolvedValueOnce(Err('API Error'));
      try {
        await capturedFetcher!('test-key');
      } catch (error: any) {
        expect(error.message).toBe('API Error');
      }

      // Test undefined path (lines 47-48)
      (ViewAPI.getList as any).mockResolvedValueOnce({ isOk: () => false, isErr: () => false });
      const undefinedResult = await capturedFetcher!('test-key');
      expect(undefinedResult).toBeUndefined();
    }

    // Test createView cache update logic execution (lines 91-93)
    const mockNewView = { id: 'new-view', name: 'New View' };
    (ViewAPI.create as any).mockResolvedValueOnce(Ok({ data: mockNewView }));
    
    let capturedUpdater: any;
    (swr.mutate as any).mockImplementation((_matcher, updateFn) => {
      capturedUpdater = updateFn;
    });
    
    await act(async () => {
      await mutationResult.current.createView('baseId', 'tableId', { name: 'New View' });
    });
    
    const mockCurrentData = { data: [{ id: 'existing', name: 'Existing' }] };
    const updatedData = capturedUpdater(mockCurrentData);
    expect(updatedData.data).toHaveLength(2);
    expect(updatedData.data[0]).toEqual(mockNewView);

    // Test updateView error handling when res.isErr (lines 144-147)
    const errorMessage = 'Update failed';
    (ViewAPI.update as any).mockResolvedValueOnce(Err(errorMessage));
    
    (swr.mutate as any).mockImplementation(async (_matcher, updateFn) => {
      if (typeof updateFn === 'function') {
        const currentData = { data: [{ id: 'view1', name: 'Original' }] };
        try {
          await updateFn(currentData);
        } catch (error: any) {
          expect(error.message).toBe(errorMessage);
          throw error;
        }
      }
    });

    await act(async () => {
      const res = await mutationResult.current.updateView('baseId', 'tableId', 'view1', { name: 'Updated' });
      expect(res.isErr()).toBe(true);
    });
  });

});
