import { FieldAPI } from '@/services/api';
import { Field } from '@/types';
import { act, renderHook } from '@testing-library/react';
import { vi } from 'vitest';
import { useTableFieldsMutation } from './useTableFields';
import { useTablesMutation } from './useTables';

vi.mock('./useTables', () => ({
  useTablesMutation: vi.fn(),
}));

describe('useTableFieldsMutation', () => {
  const baseId = 'base1';
  const tableId = 'table1';
  const fieldId = 'field1';
  const field: Field = { id: fieldId, name: 'Field 1', isPrimary: false } as any;
  const updatedField: Field = { ...field, name: 'Updated Field', isPrimary: false };
  const newField: Field = { id: 'field2', name: 'Field 2', isPrimary: false } as any;

  let updateQueryCache: ReturnType<typeof vi.fn>;

  beforeEach(() => {
    updateQueryCache = vi.fn();
    (useTablesMutation as unknown as ReturnType<typeof vi.fn>).mockReturnValue({
      updateQueryCache,
    });
    vi.clearAllMocks();
  });

  afterEach(() => {
    vi.restoreAllMocks();
  });

  function okRes(value: any) {
    const obj = {
      isOk: function (this: any): this is typeof obj {
        return true;
      },
      isErr: function (this: any): this is typeof obj {
        return false;
      },
      value,
      expectErr: vi.fn(),
      unwrap: vi.fn(() => value),
      unwrapErr: vi.fn(),
      mapOr: vi.fn(),
      mapOrElse: vi.fn(),
      or: vi.fn(),
      toOption: vi.fn(),
      safeUnwrap: vi.fn(),
      toAsyncResult: vi.fn(),
      else: vi.fn(),
      unwrapOr: vi.fn(),
      expect: vi.fn(),
      map: vi.fn(),
      mapErr: vi.fn(),
      andThen: vi.fn(),
      orElse: vi.fn(),
      match: vi.fn(),
      _stack: '',
      [Symbol.iterator]: function* () {},
    };
    return obj;
  }
  function errRes(error: any) {
    const obj = {
      isOk: function (this: any): this is typeof obj {
        return false;
      },
      isErr: function (this: any): this is typeof obj {
        return true;
      },
      error,
      expectErr: vi.fn(),
      unwrap: vi.fn(),
      unwrapErr: vi.fn(() => error),
      mapOr: vi.fn(),
      mapOrElse: vi.fn(),
      or: vi.fn(),
      toOption: vi.fn(),
      safeUnwrap: vi.fn(),
      toAsyncResult: vi.fn(),
      stack: '',
      else: vi.fn(),
      unwrapOr: vi.fn(),
      expect: vi.fn(),
      map: vi.fn(),
      mapErr: vi.fn(),
      andThen: vi.fn(),
      orElse: vi.fn(),
      match: vi.fn(),
      _stack: '',
      [Symbol.iterator]: function* () {},
    };
    return obj;
  }


  it('handleTableFieldUpdateEvent updates cache', () => {
    const { result } = renderHook(() => useTableFieldsMutation());
    act(() => {
      result.current.handleTableFieldUpdateEvent(baseId, tableId, updatedField);
    });
    expect(updateQueryCache).toHaveBeenCalledWith(baseId, expect.any(Function));
  });

  it('handleTableFieldUpdateEvent with updateOtherFields', () => {
    const { result } = renderHook(() => useTableFieldsMutation());
    const updateOtherFields = vi.fn().mockReturnValue({ isPrimary: false });
    // Simulate updateQueryCache with a table that has two fields: one matching updatedField.id, one not
    updateQueryCache.mockImplementation((_baseId, updater) => {
      const currentData = {
        data: [
          {
            id: tableId,
            fields: [
              { id: updatedField.id, name: 'Updated Field', isPrimary: false },
              { id: 'otherField', name: 'Other Field', isPrimary: true },
            ],
          },
        ],
      };
      updater(currentData);
    });
    act(() => {
      result.current.handleTableFieldUpdateEvent(baseId, tableId, updatedField, {
        updateOtherFields,
      });
    });
    expect(updateQueryCache).toHaveBeenCalledWith(baseId, expect.any(Function));
    expect(updateOtherFields).toHaveBeenCalled();
  });



  it('createField calls FieldAPI.create and updates cache on success', async () => {
    vi.spyOn(FieldAPI, 'create').mockResolvedValueOnce(okRes({ data: newField }));
    const { result } = renderHook(() => useTableFieldsMutation());
    let response;
    await act(async () => {
      response = await result.current.createField(baseId, tableId, { name: 'Field 2' } as any);
    });
    expect(FieldAPI.create).toHaveBeenCalledWith(baseId, tableId, {
      name: 'Field 2',
      isPrimary: false,
    });
    expect(updateQueryCache).toHaveBeenCalled();
    expect(response.res.value.data).toBe(newField);
  });

  it('createField does not update cache if updateCacheOnSuccess is false', async () => {
    vi.spyOn(FieldAPI, 'create').mockResolvedValueOnce(okRes({ data: newField }));
    const { result } = renderHook(() => useTableFieldsMutation());
    await act(async () => {
      await result.current.createField(baseId, tableId, { name: 'Field 2' } as any, {
        updateCacheOnSuccess: false,
      });
    });
    expect(updateQueryCache).not.toHaveBeenCalled();
  });

  it('createField with empty payload does not throw', async () => {
    vi.spyOn(FieldAPI, 'create').mockResolvedValueOnce(okRes({ data: undefined }));
    const { result } = renderHook(() => useTableFieldsMutation());
    await act(async () => {
      await result.current.createField(baseId, tableId, {} as any);
    });
    expect(FieldAPI.create).toHaveBeenCalled();
  });

  it('updateField calls FieldAPI.update and updates cache on success', async () => {
    vi.spyOn(FieldAPI, 'update').mockResolvedValueOnce(okRes({ data: updatedField }));
    const { result } = renderHook(() => useTableFieldsMutation());
    await act(async () => {
      await result.current.updateField(baseId, tableId, fieldId, { name: 'Updated Field' } as any);
    });
    expect(FieldAPI.update).toHaveBeenCalledWith(baseId, tableId, fieldId, {
      name: 'Updated Field',
    });
    expect(updateQueryCache).toHaveBeenCalled();
  });

  it('updateField does not update cache if FieldAPI.update fails', async () => {
    vi.spyOn(FieldAPI, 'update').mockResolvedValueOnce(errRes('fail'));
    const { result } = renderHook(() => useTableFieldsMutation());
    await act(async () => {
      await result.current.updateField(baseId, tableId, fieldId, { name: 'Updated Field' } as any);
    });
    expect(updateQueryCache).not.toHaveBeenCalled();
  });

  it('removeField calls FieldAPI.remove and updates cache on success', async () => {
    vi.spyOn(FieldAPI, 'remove').mockResolvedValueOnce(okRes(undefined));
    const { result } = renderHook(() => useTableFieldsMutation());
    await act(async () => {
      await result.current.removeField(baseId, tableId, fieldId);
    });
    expect(FieldAPI.remove).toHaveBeenCalledWith(baseId, tableId, fieldId);
    expect(updateQueryCache).toHaveBeenCalled();
  });

  it('removeField does not update cache if FieldAPI.remove fails', async () => {
    vi.spyOn(FieldAPI, 'remove').mockResolvedValueOnce(errRes('fail'));
    const { result } = renderHook(() => useTableFieldsMutation());
    await act(async () => {
      await result.current.removeField(baseId, tableId, fieldId);
    });
    expect(updateQueryCache).not.toHaveBeenCalled();
  });

  it('successfully sets a field as primary and updates cache', async () => {
    vi.spyOn(FieldAPI, 'update').mockResolvedValueOnce(
      okRes({ data: { ...field, isPrimary: true } })
    );
    const { result } = renderHook(() => useTableFieldsMutation());
    
    await act(async () => {
      await result.current.setPrimaryField(baseId, tableId, fieldId, { name: 'Primary' } as any);
    });
    
    expect(FieldAPI.update).toHaveBeenCalledWith(baseId, tableId, fieldId, {
      name: 'Primary',
      isPrimary: true,
    });
    expect(updateQueryCache).toHaveBeenCalled();
  });

  it('does not update cache when setting primary field fails', async () => {
    vi.spyOn(FieldAPI, 'update').mockResolvedValueOnce(errRes('fail'));
    const { result } = renderHook(() => useTableFieldsMutation());
    
    await act(async () => {
      await result.current.setPrimaryField(baseId, tableId, fieldId, { name: 'Primary' } as any);
    });
    
    expect(updateQueryCache).not.toHaveBeenCalled();
  });


  // Comprehensive cache update logic tests
  it('cache update operations handle all scenarios correctly', () => {
    const { result } = renderHook(() => useTableFieldsMutation());
    let updateUpdater: any, createUpdater: any, deleteUpdater: any;
    
    const mockCurrentData = {
      data: [
        {
          id: tableId,
          fields: [
            { id: fieldId, name: 'To Be Updated/Deleted' },
            { id: 'field2', name: 'Other Field' },
          ],
        },
        {
          id: 'othertable',
          fields: [{ id: 'field3', name: 'Unchanged' }],
        },
      ],
    };

    // Test update event with no matching field (covers lines 39-40)
    updateQueryCache.mockImplementation((_baseId, updater) => { updateUpdater = updater; });
    act(() => {
      result.current.handleTableFieldUpdateEvent(baseId, tableId, { id: 'nonexistent', name: 'Test' } as any);
    });
    const updateResult = updateUpdater(mockCurrentData);
    expect(updateResult.data[0].fields[0].name).toBe('To Be Updated/Deleted'); // Unchanged
    expect(updateResult.data[1]).toBe(mockCurrentData.data[1]); // Other table unchanged

    // Test create event (covers lines 55-61)  
    updateQueryCache.mockImplementation((_baseId, updater) => { createUpdater = updater; });
    act(() => {
      result.current.handleTableFieldCreateEvent(baseId, tableId, newField);
    });
    const createResult = createUpdater(mockCurrentData);
    expect(createResult.data[0].fields).toHaveLength(3);
    expect(createResult.data[0].fields[2]).toBe(newField);

    // Test delete event (covers lines 69-75)
    updateQueryCache.mockImplementation((_baseId, updater) => { deleteUpdater = updater; });
    act(() => {
      result.current.handleTableFieldDeleteEvent(baseId, tableId, fieldId);
    });
    const deleteResult = deleteUpdater(mockCurrentData);
    expect(deleteResult.data[0].fields).toHaveLength(1);
    expect(deleteResult.data[0].fields[0].id).toBe('field2');
  });

  it('setPrimaryField covers updateOtherFields callback logic', async () => {
    const { result } = renderHook(() => useTableFieldsMutation());
    let actualUpdater: any;
    
    vi.spyOn(FieldAPI, 'update').mockResolvedValueOnce(
      okRes({ data: { ...field, isPrimary: true } })
    );
    
    updateQueryCache.mockImplementation((_baseId, updater) => {
      actualUpdater = updater;
    });
    
    await act(async () => {
      await result.current.setPrimaryField(baseId, tableId, fieldId, { name: 'Primary' } as any);
    });
    
    // Execute the actual cache updater to cover lines 138-139
    const mockCurrentData = {
      data: [
        {
          id: tableId,
          fields: [
            { id: fieldId, name: 'Field 1', isPrimary: false },
            { id: 'field2', name: 'Field 2', isPrimary: true }, // Will be set to false
          ],
        },
      ],
    };
    
    const result_data = actualUpdater(mockCurrentData);
    expect(result_data.data[0].fields[0].isPrimary).toBe(true); // Updated field
    expect(result_data.data[0].fields[1].isPrimary).toBe(false); // Other field set to false
  });

});
