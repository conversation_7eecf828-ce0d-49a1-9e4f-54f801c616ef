import { act, renderHook } from '@testing-library/react';
import { useEntities } from './useEntities';

type Entity = { id: string; value?: string };

describe('useEntities', () => {
  it('initializes with empty state by default', () => {
    const { result } = renderHook(() => useEntities<Entity>());
    expect(result.current.ids).toEqual([]);
    expect(result.current.entities).toEqual({});
  });

  it('initializes with provided data when given', () => {
    const initialIds = ['a'];
    const initialEntities = { a: { id: 'a', value: 'foo' } };
    const { result } = renderHook(() => useEntities<Entity>(initialIds, initialEntities));
    expect(result.current.ids).toEqual(['a']);
    expect(result.current.entities).toEqual({ a: { id: 'a', value: 'foo' } });
  });

  it('handles complete entity lifecycle: create, update, and delete', () => {
    const { result } = renderHook(() => useEntities<Entity>());
    
    act(() => {
      result.current.createEntity('a', { id: 'a', value: 'foo' });
    });
    expect(result.current.ids).toEqual(['a']);
    expect(result.current.entities.a).toEqual({ id: 'a', value: 'foo' });

    act(() => {
      result.current.updateEntity('a', { value: 'bar' });
    });
    expect(result.current.entities.a).toEqual({ id: 'a', value: 'bar' });

    act(() => {
      result.current.deleteEntity('a');
    });
    expect(result.current.ids).toEqual([]);
    expect(result.current.entities).toEqual({});
  });

  it('prevents creating duplicate entities', () => {
    const { result } = renderHook(() => useEntities<Entity>());
    
    act(() => {
      result.current.createEntity('a', { id: 'a', value: 'original' });
    });
    
    expect(result.current.entities.a).toEqual({ id: 'a', value: 'original' });
    expect(result.current.ids).toEqual(['a']);
    
    // Try to create the same entity again - should be prevented
    act(() => {
      result.current.createEntity('a', { id: 'a', value: 'should-not-change' });
    });
    
    // The entity should remain unchanged, and no duplicate ID should be added
    expect(result.current.entities.a).toEqual({ id: 'a', value: 'original' });
    expect(result.current.ids).toEqual(['a']); // No duplicate
  });

  it('gracefully handles operations on non-existent entities', () => {
    const { result } = renderHook(() => useEntities<Entity>());
    
    act(() => {
      result.current.updateEntity('missing', { value: 'bar' });
      result.current.deleteEntity('missing');
    });
    
    expect(result.current.ids).toEqual([]);
    expect(result.current.entities).toEqual({});
  });

  it('replaces all entities when using setAll', () => {
    const { result } = renderHook(() => useEntities<Entity>());
    
    act(() => {
      result.current.setAll([
        { id: 'a', value: 'foo' },
        { id: 'b', value: 'bar' },
      ]);
    });
    
    expect(result.current.ids).toEqual(['a', 'b']);
    expect(result.current.entities).toEqual({
      a: { id: 'a', value: 'foo' },
      b: { id: 'b', value: 'bar' },
    });
  });

  it('adds multiple entities at once', () => {
    const { result } = renderHook(() => useEntities<Entity>());
    
    act(() => {
      result.current.createEntity('a', { id: 'a', value: 'foo' });
      result.current.addMany([
        { id: 'b', value: 'bar' },
        { id: 'c', value: 'baz' },
      ]);
    });
    
    expect(result.current.ids).toEqual(['a', 'b', 'c']);
    expect(result.current.entities.a).toEqual({ id: 'a', value: 'foo' });
    expect(result.current.entities.b).toEqual({ id: 'b', value: 'bar' });
    expect(result.current.entities.c).toEqual({ id: 'c', value: 'baz' });
  });

  it('clears all entities when using removeAll', () => {
    const { result } = renderHook(() => useEntities<Entity>());
    
    act(() => {
      result.current.setAll([
        { id: 'a', value: 'foo' },
        { id: 'b', value: 'bar' },
      ]);
      result.current.removeAll();
    });
    
    expect(result.current.ids).toEqual([]);
    expect(result.current.entities).toEqual({});
  });

  it('updates multiple existing entities efficiently', () => {
    const { result } = renderHook(() => useEntities<Entity>());
    
    act(() => {
      result.current.setAll([{ id: 'a', value: 'foo' }]);
    });
    
    // Verify entity exists first
    expect(result.current.entities.a).toEqual({ id: 'a', value: 'foo' });
    
    act(() => {
      result.current.updateMany([{ id: 'a', value: 'updated' }]);
    });
    
    expect(result.current.entities.a).toEqual({ id: 'a', value: 'updated' });
  });

  it('removes multiple entities by their IDs', () => {
    const { result } = renderHook(() => useEntities<Entity>());
    
    act(() => {
      result.current.createEntity('a', { id: 'a', value: 'foo' });
      result.current.createEntity('b', { id: 'b', value: 'bar' });
      result.current.createEntity('c', { id: 'c', value: 'baz' });
    });
    
    expect(result.current.ids).toEqual(['a', 'b', 'c']);
    
    act(() => {
      result.current.removeMany(['a', 'c']);
    });
    
    expect(result.current.ids).toEqual(['b']);
    expect(result.current.entities.a).toBeUndefined();
    expect(result.current.entities.b).toEqual({ id: 'b', value: 'bar' });
    expect(result.current.entities.c).toBeUndefined();
  });

  it('retrieves entities by ID or returns null if not found', () => {
    const { result } = renderHook(() => useEntities<Entity>());
    
    act(() => {
      result.current.createEntity('a', { id: 'a', value: 'foo' });
    });
    
    expect(result.current.getEntity('a')).toEqual({ id: 'a', value: 'foo' });
    expect(result.current.getEntity('missing')).toBeNull();
  });

  it('handles empty operations gracefully', () => {
    const { result } = renderHook(() => useEntities<Entity>());
    
    act(() => {
      result.current.setAll([{ id: 'a', value: 'foo' }]);
      // These should all be no-ops
      result.current.updateMany([]);
      result.current.addMany([]);
      result.current.removeMany([]);
    });
    
    expect(result.current.ids).toEqual(['a']);
    expect(result.current.entities.a).toEqual({ id: 'a', value: 'foo' });
  });

  it('clears all entities when setAll is called with empty array', () => {
    const { result } = renderHook(() => useEntities<Entity>());
    
    act(() => {
      result.current.setAll([{ id: 'a', value: 'foo' }]);
      result.current.setAll([]);
    });
    
    expect(result.current.ids).toEqual([]);
    expect(result.current.entities).toEqual({});
  });

});
