import { renderHook } from '@testing-library/react';
import { vi } from 'vitest';
import { useTableQuery } from './useTable';

// Mock TableAPI
vi.mock('@/services/api/table', () => ({
  TableAPI: {
    get: vi.fn(),
  },
}));

// Mock SWR for all tests in this file
vi.mock('swr', () => ({
  __esModule: true,
  default: vi.fn(),
}));

// Import the mocked modules after mocking
import mockSWR from 'swr';
import { TableAPI as MockTableAPI } from '@/services/api/table';

describe('useTableQuery (unit, SWR mocked)', () => {
  afterEach(() => {
    vi.clearAllMocks();
  });

  it('returns data on success', () => {
    const mockData = { data: [1] };
    (mockSWR as any).mockReturnValue({
      data: mockData,
      isLoading: false,
      isValidating: false,
      mutate: vi.fn(),
      error: undefined,
    });
    const { result } = renderHook(() => useTableQuery('b1', 't1'));
    expect(result.current.table).toEqual([1]);
    expect(result.current.isLoading).toBe(false);
    expect(result.current.isValidating).toBe(false);
    expect(result.current.error).toBeUndefined();
    expect(result.current.key).toBeDefined();
  });

  it('returns null if no data', () => {
    (mockSWR as any).mockReturnValue({
      data: undefined,
      isLoading: false,
      isValidating: false,
      mutate: vi.fn(),
      error: undefined,
    });
    const { result } = renderHook(() => useTableQuery('b1', 't1'));
    expect(result.current.table).toBeNull();
  });

  it('returns null if baseId or tableId is missing', () => {
    (mockSWR as any).mockReturnValue({
      data: undefined,
      isLoading: false,
      isValidating: false,
      mutate: vi.fn(),
      error: undefined,
    });
    const { result } = renderHook(() => useTableQuery(undefined, 't1'));
    expect(result.current.table).toBeNull();
    const { result: result2 } = renderHook(() => useTableQuery('b1', undefined));
    expect(result2.current.table).toBeNull();
  });

  it('returns error if present', () => {
    (mockSWR as any).mockReturnValue({
      data: undefined,
      isLoading: false,
      isValidating: false,
      mutate: vi.fn(),
      error: new Error('fail'),
    });
    const { result } = renderHook(() => useTableQuery('b1', 't1'));
    expect(result.current.error).toBeInstanceOf(Error);
  });

  it('calls onSuccess if data is present', () => {
    const onSuccess = vi.fn();
    const mockData = { data: [1] };
    (mockSWR as any).mockImplementation((_key, _fetcher, options) => {
      options?.onSuccess?.(mockData);
      return {
        data: mockData,
        isLoading: false,
        isValidating: false,
        mutate: vi.fn(),
        error: undefined,
      };
    });
    renderHook(() => useTableQuery('b1', 't1', { onSuccess }));
    expect(onSuccess).toHaveBeenCalledWith(mockData);
  });

  it('does not call onSuccess if data is undefined', () => {
    const onSuccess = vi.fn();
    (mockSWR as any).mockImplementation((_key, _fetcher, options) => {
      options?.onSuccess?.(undefined);
      return {
        data: undefined,
        isLoading: false,
        isValidating: false,
        mutate: vi.fn(),
        error: undefined,
      };
    });
    renderHook(() => useTableQuery('b1', 't1', { onSuccess }));
    expect(onSuccess).not.toHaveBeenCalled();
  });

  it('returns all expected properties in the result', () => {
    (mockSWR as any).mockReturnValue({
      data: { data: [1] },
      isLoading: true,
      isValidating: true,
      mutate: vi.fn(),
      error: undefined,
    });
    const { result } = renderHook(() => useTableQuery('b1', 't1'));
    expect(result.current).toHaveProperty('table');
    expect(result.current).toHaveProperty('isLoading');
    expect(result.current).toHaveProperty('isValidating');
    expect(result.current).toHaveProperty('mutate');
    expect(result.current).toHaveProperty('error');
    expect(result.current).toHaveProperty('key');
  });

  it('handles loading state correctly', () => {
    (mockSWR as any).mockReturnValue({
      data: undefined,
      isLoading: true,
      isValidating: false,
      mutate: vi.fn(),
      error: undefined,
    });
    const { result } = renderHook(() => useTableQuery('b1', 't1'));
    expect(result.current.isLoading).toBe(true);
    expect(result.current.isValidating).toBe(false);
  });

  it('handles validating state correctly', () => {
    (mockSWR as any).mockReturnValue({
      data: { data: [1] },
      isLoading: false,
      isValidating: true,
      mutate: vi.fn(),
      error: undefined,
    });
    const { result } = renderHook(() => useTableQuery('b1', 't1'));
    expect(result.current.isLoading).toBe(false);
    expect(result.current.isValidating).toBe(true);
  });

  it('returns null key when baseId or tableId is missing', () => {
    (mockSWR as any).mockReturnValue({
      data: undefined,
      isLoading: false,
      isValidating: false,
      mutate: vi.fn(),
      error: undefined,
    });
    const { result: result1 } = renderHook(() => useTableQuery(undefined, 't1'));
    expect(result1.current.key).toBeNull();
    
    const { result: result2 } = renderHook(() => useTableQuery('b1', undefined));
    expect(result2.current.key).toBeNull();
  });

  it('generates key with correct format for baseId and tableId', () => {
    (mockSWR as any).mockReturnValue({
      data: undefined,
      isLoading: false,
      isValidating: false,
      mutate: vi.fn(),
      error: undefined,
    });
    
    const { result } = renderHook(() => useTableQuery('b1', 't1'));
    
    expect(result.current.key).toContain('b1');
    expect(result.current.key).toContain('t1');
    expect(Array.isArray(result.current.key)).toBe(true);
    expect(result.current.key).toHaveLength(4);
  });
});

describe('useTableQuery integration tests (actual fetcher execution)', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('executes fetcher with success response', async () => {
    const mockResponse = {
      isOk: () => true,
      isErr: () => false,
      value: { data: { id: 't1', name: 'Test Table' } },
    };
    (MockTableAPI.get as any).mockResolvedValue(mockResponse);

    // Mock SWR to actually call the fetcher
    (mockSWR as any).mockImplementation((key, fetcher) => {
      if (key && fetcher) {
        // Execute the fetcher to get coverage
        fetcher().catch(() => {}); // Catch any errors to prevent unhandled rejections
      }
      return {
        data: mockResponse.value,
        isLoading: false,
        isValidating: false,
        mutate: vi.fn(),
        error: undefined,
      };
    });

    const { result } = renderHook(() => useTableQuery('b1', 't1'));
    expect(MockTableAPI.get).toHaveBeenCalledWith('b1', 't1');
    expect(result.current.table).toEqual({ id: 't1', name: 'Test Table' });
  });

  it('executes fetcher with error response', async () => {
    const mockErrorResponse = {
      isOk: () => false,
      isErr: () => true,
      error: 'API Error',
    };
    (MockTableAPI.get as any).mockResolvedValue(mockErrorResponse);

    (mockSWR as any).mockImplementation((key, fetcher) => {
      if (key && fetcher) {
        fetcher().catch(() => {}); // Catch errors
      }
      return {
        data: undefined,
        isLoading: false,
        isValidating: false,
        mutate: vi.fn(),
        error: new Error('API Error'),
      };
    });

    const { result } = renderHook(() => useTableQuery('b1', 't1'));
    expect(MockTableAPI.get).toHaveBeenCalledWith('b1', 't1');
    expect(result.current.error).toBeInstanceOf(Error);
  });

  it('executes fetcher with unknown response', async () => {
    const mockUnknownResponse = {
      isOk: () => false,
      isErr: () => false,
    };
    (MockTableAPI.get as any).mockResolvedValue(mockUnknownResponse);

    (mockSWR as any).mockImplementation((key, fetcher) => {
      if (key && fetcher) {
        fetcher().catch(() => {}); // Catch errors
      }
      return {
        data: undefined,
        isLoading: false,
        isValidating: false,
        mutate: vi.fn(),
        error: undefined,
      };
    });

    const { result } = renderHook(() => useTableQuery('b1', 't1'));
    expect(MockTableAPI.get).toHaveBeenCalledWith('b1', 't1');
    expect(result.current.table).toBeNull();
  });
});
