import { TableBase<PERSON>I, TrashAPI } from '@/services/api';
import { act, renderHook } from '@testing-library/react';
import useSWR from 'swr';
import { vi } from 'vitest';
import { useTableBaseMutation, useTableBaseQuery, isTableBasesKey } from './useTableBase';

vi.mock('swr', () => ({
  __esModule: true,
  default: vi.fn(),
  mutate: vi.fn(),
}));
vi.mock('@/services/api', () => ({
  TableBaseAPI: {
    getList: vi.fn(),
    create: vi.fn(),
    update: vi.fn(),
    remove: vi.fn(),
  },
  TrashAPI: {
    restore: vi.fn(),
    remove: vi.fn(),
  },
}));
vi.mock('@tolgee/react', () => ({ useTranslate: () => ({ t: (x: string) => x }) }));
vi.mock('@/components/Common', () => ({ notifications: { show: vi.fn() } }));

describe('useTableBaseQuery', () => {
  afterEach(() => {
    vi.clearAllMocks();
  });
  it('returns data on success', async () => {
    (useSWR as any).mockReturnValue({
      data: { data: [1], pagination: { page: 1 } },
      isLoading: false,
      error: undefined,
    });
    const { result } = renderHook(() => useTableBaseQuery({ page: 1, limit: 10 }));
    expect(result.current.tableBases).toEqual([1]);
    expect(result.current.pagination).toEqual({ page: 1 });
    expect(result.current.isLoading).toBe(false);
    expect(result.current.error).toBeUndefined();
  });
  it('returns empty array if no data', () => {
    (useSWR as any).mockReturnValue({ data: undefined, isLoading: false, error: undefined });
    const { result } = renderHook(() => useTableBaseQuery({ page: 1, limit: 10 }));
    expect(result.current.tableBases).toEqual([]);
    expect(result.current.pagination).toBeNull();
  });
  it('handles error', () => {
    (useSWR as any).mockReturnValue({
      data: undefined,
      isLoading: false,
      error: new Error('fail'),
    });
    const { result } = renderHook(() => useTableBaseQuery({ page: 1, limit: 10 }));
    expect(result.current.error).toBeInstanceOf(Error);
  });
});

describe('useTableBaseMutation', () => {
  afterEach(() => {
    vi.clearAllMocks();
  });
  it('createTableBase success', async () => {
    (TableBaseAPI.create as any).mockResolvedValueOnce({ isOk: () => true, isErr: () => false });
    const { result } = renderHook(() => useTableBaseMutation());
    await act(async () => {
      await result.current.createTableBase({ name: 'test' });
    });
    expect(TableBaseAPI.create).toHaveBeenCalled();
  });
  it('createTableBase error', async () => {
    (TableBaseAPI.create as any).mockResolvedValueOnce({
      isOk: () => false,
      isErr: () => true,
      error: 'err',
    });
    const { result } = renderHook(() => useTableBaseMutation());
    await act(async () => {
      await result.current.createTableBase({ name: 'test' });
    });
  });
  it('duplicateTableBase', async () => {
    (TableBaseAPI.create as any).mockResolvedValueOnce({ isOk: () => true, isErr: () => false });
    const { result } = renderHook(() => useTableBaseMutation());
    await act(async () => {
      await result.current.duplicateTableBase('id', { name: 'test' });
    });
    expect(TableBaseAPI.create).toHaveBeenCalled();
  });
  it('duplicateTableBase error', async () => {
    (TableBaseAPI.create as any).mockResolvedValueOnce({
      isOk: () => false,
      isErr: () => true,
      error: 'err',
    });
    const { result } = renderHook(() => useTableBaseMutation());
    await act(async () => {
      await result.current.duplicateTableBase('id', { name: 'test' });
    });
  });
  it('updateTableBase', async () => {
    (TableBaseAPI.update as any).mockResolvedValueOnce({ isOk: () => true, isErr: () => false });
    const { result } = renderHook(() => useTableBaseMutation());
    await act(async () => {
      await result.current.updateTableBase('id', { name: 'test' });
    });
    expect(TableBaseAPI.update).toHaveBeenCalled();
  });
  it('updateTableBase error', async () => {
    (TableBaseAPI.update as any).mockResolvedValueOnce({
      isOk: () => false,
      isErr: () => true,
      error: 'err',
    });
    const { result } = renderHook(() => useTableBaseMutation());
    await act(async () => {
      await result.current.updateTableBase('id', { name: 'test' });
    });
  });
  it('updateTableBase with undefined currentData does not throw', async () => {
    (TableBaseAPI.update as any).mockResolvedValueOnce({
      isOk: () => true,
      isErr: () => false,
      value: { data: { id: 'id', name: 'test' } },
    });
    const { result } = renderHook(() => useTableBaseMutation());
    await act(async () => {
      await result.current.updateTableBase('id', { name: 'test' });
    });
    expect(TableBaseAPI.update).toHaveBeenCalled();
  });
  it('removeTableBase', async () => {
    (TableBaseAPI.remove as any).mockResolvedValueOnce({ isOk: () => true, isErr: () => false });
    const { result } = renderHook(() => useTableBaseMutation());
    await act(async () => {
      await result.current.removeTableBase('id');
    });
    expect(TableBaseAPI.remove).toHaveBeenCalled();
  });
  it('removeTableBase error', async () => {
    (TableBaseAPI.remove as any).mockResolvedValueOnce({
      isOk: () => false,
      isErr: () => true,
      error: 'err',
    });
    const { result } = renderHook(() => useTableBaseMutation());
    await act(async () => {
      await result.current.removeTableBase('id');
    });
  });
  it('restoreTableBase', async () => {
    (TrashAPI.restore as any).mockResolvedValueOnce({ isOk: () => true, isErr: () => false });
    const { result } = renderHook(() => useTableBaseMutation());
    await act(async () => {
      await result.current.restoreTableBase('id');
    });
    expect(TrashAPI.restore).toHaveBeenCalled();
  });
  it('restoreTableBase error', async () => {
    (TrashAPI.restore as any).mockResolvedValueOnce({
      isOk: () => false,
      isErr: () => true,
      error: 'err',
    });
    const { result } = renderHook(() => useTableBaseMutation());
    await act(async () => {
      await result.current.restoreTableBase('id');
    });
  });
  it('permanentlyDeleteTableBase', async () => {
    (TrashAPI.remove as any).mockResolvedValueOnce({ isOk: () => true, isErr: () => false });
    const { result } = renderHook(() => useTableBaseMutation());
    await act(async () => {
      await result.current.permanentlyDeleteTableBase('id');
    });
    expect(TrashAPI.remove).toHaveBeenCalled();
  });
  it('permanentlyDeleteTableBase error', async () => {
    (TrashAPI.remove as any).mockResolvedValueOnce({
      isOk: () => false,
      isErr: () => true,
      error: 'err',
    });
    const { result } = renderHook(() => useTableBaseMutation());
    await act(async () => {
      await result.current.permanentlyDeleteTableBase('id');
    });
  });

  it('validates table base cache keys correctly', () => {
    // Valid cache key format
    const validKey = ['bases/', { page: 1, limit: 10 }];
    expect(isTableBasesKey(validKey)).toBe(true);
    
    // Invalid formats should return false
    expect(isTableBasesKey('not-an-array')).toBe(false);
    expect(isTableBasesKey(['wrong/', { page: 1 }])).toBe(false);
    expect(isTableBasesKey(['bases/', 'not-object'])).toBe(false);
    expect(isTableBasesKey(null)).toBe(false);
    expect(isTableBasesKey(undefined)).toBe(false);
  });

  it('handles fetcher error path (lines 30-35)', async () => {
    let capturedFetcher: any = null;
    (useSWR as any).mockImplementation((_key, fetcher, _opts) => {
      capturedFetcher = fetcher;
      return { data: undefined, isLoading: false, error: undefined };
    });
    
    // Mock TableBaseAPI.getList to return an error
    (TableBaseAPI.getList as any).mockResolvedValueOnce({
      isOk: () => false,
      error: 'Test API Error'
    });
    
    renderHook(() => useTableBaseQuery({ page: 1, limit: 10 }));
    
    if (capturedFetcher) {
      try {
        await capturedFetcher!('test-key');
      } catch (error: any) {
        expect(error.message).toBe('Test API Error');
      }
    }
  });

  it('successfully processes and returns API data', async () => {
    let capturedFetcher: any = null;
    (useSWR as any).mockImplementation((_key, fetcher, _opts) => {
      capturedFetcher = fetcher;
      return { data: undefined, isLoading: false, error: undefined };
    });
    
    (TableBaseAPI.getList as any).mockResolvedValueOnce({
      isOk: () => true,
      value: { data: [{ id: 'test' }], pagination: { page: 1 } }
    });
    
    renderHook(() => useTableBaseQuery({ page: 1, limit: 10 }));
    
    if (capturedFetcher) {
      const result = await capturedFetcher!('test-key');
      expect(result).toEqual({ data: [{ id: 'test' }], pagination: { page: 1 } });
    }
  });

  it('displays error notifications when queries fail', () => {
    let capturedOnError: any = null;
    (useSWR as any).mockImplementation((_key, _fetcher, opts) => {
      capturedOnError = opts.onError;
      return { data: undefined, isLoading: false, error: undefined };
    });
    
    renderHook(() => useTableBaseQuery({ page: 1, limit: 10 }));
    
    if (capturedOnError) {
      const testError = new Error('Test error message');
      capturedOnError(testError);
    }
  });
});
