import { RecordAPI } from '@/services/api';
import { notifications } from '@/components/Common';
import { act, renderHook } from '@testing-library/react';
import useSWR, { mutate as globalMutate } from 'swr';
import { vi } from 'vitest';
import { useTableRecordsMutation, useTableRecordsStream, isTableRecordsKey } from './useTableRecords';

vi.mock('swr', () => ({
  __esModule: true,
  default: vi.fn(),
  mutate: vi.fn(),
}));
vi.mock('@/services/api', () => ({
  RecordAPI: {
    stream: vi.fn(),
    create: vi.fn(),
    update: vi.fn(),
    remove: vi.fn(),
    removeMany: vi.fn(),
    clearData: vi.fn(),
  },
}));
vi.mock('@tolgee/react', () => ({ useTranslate: () => ({ t: (x: string) => x }) }));
vi.mock('@/components/Common', () => ({ notifications: { show: vi.fn() } }));

describe('useTableRecordsStream', () => {
  afterEach(() => {
    vi.clearAllMocks();
  });
  it('calls onSuccess on stream complete', () => {
    const onSuccess = vi.fn();
    const stream = { abort: vi.fn(), onComplete: vi.fn((cb) => cb('data')), onError: vi.fn() };
    (RecordAPI.stream as any).mockReturnValue(stream);
    (useSWR as any).mockImplementation((_key, fetcher) => {
      fetcher();
      return {
        data: undefined,
        error: undefined,
        isLoading: false,
        isValidating: false,
        mutate: vi.fn(),
      };
    });
    renderHook(() => useTableRecordsStream('b', 't', { enabled: true, onSuccess }));
    expect(onSuccess).toHaveBeenCalledWith('data');
  });
});

describe('useTableRecordsMutation', () => {
  afterEach(() => {
    vi.clearAllMocks();
  });
  it('createRecords error', async () => {
    (RecordAPI.create as any).mockResolvedValueOnce({
      isOk: () => false,
      isErr: () => true,
      error: 'err',
    });
    const { result } = renderHook(() => useTableRecordsMutation());
    await act(async () => {
      await result.current.createRecords('b', 't', { records: [] });
    });
  });
  it('duplicateRecord error', async () => {
    (RecordAPI.create as any).mockResolvedValueOnce({
      isOk: () => false,
      isErr: () => true,
      error: 'err',
    });
    const { result } = renderHook(() => useTableRecordsMutation());
    await act(async () => {
      await result.current.duplicateRecord('b', 't', 'r', { sortId: 1, records: [] });
    });
  });
  it('updateRecord error', async () => {
    (RecordAPI.update as any).mockResolvedValueOnce({
      isOk: () => false,
      isErr: () => true,
      error: 'err',
    });
    const { result } = renderHook(() => useTableRecordsMutation());
    await act(async () => {
      await result.current.updateRecord('b', 't', 'r', {});
    });
  });
  it('removeRecord error', async () => {
    (RecordAPI.remove as any).mockResolvedValueOnce({
      isOk: () => false,
      isErr: () => true,
      error: 'err',
    });
    const { result } = renderHook(() => useTableRecordsMutation());
    await act(async () => {
      await result.current.removeRecord('b', 't', 'r');
    });
  });
  it('removeRecords with empty array does nothing', async () => {
    (RecordAPI.removeMany as any).mockResolvedValueOnce({ isOk: () => true, isErr: () => false });
    const { result } = renderHook(() => useTableRecordsMutation());
    await act(async () => {
      await result.current.removeRecords('b', 't', []);
    });
    expect(RecordAPI.removeMany).toHaveBeenCalledWith('b', 't', []);
  });
  it('clearRecords success triggers revalidateQuery', async () => {
    (RecordAPI.clearData as any).mockResolvedValueOnce({ isOk: () => true, isErr: () => false });
    const { result } = renderHook(() => useTableRecordsMutation());
    await act(async () => {
      await result.current.clearRecords('b', 't');
    });
    expect(RecordAPI.clearData).toHaveBeenCalledWith('b', 't');
    expect(globalMutate).toHaveBeenCalled();
  });

  it('clearRecords error', async () => {
    (RecordAPI.clearData as any).mockResolvedValueOnce({
      isOk: () => false,
      isErr: () => true,
      error: 'err',
    });
    const { result } = renderHook(() => useTableRecordsMutation());
    await act(async () => {
      await result.current.clearRecords('b', 't');
    });
  });

  // Comprehensive cache update logic tests
  it('covers all cache update scenarios for record operations', async () => {
    const { result } = renderHook(() => useTableRecordsMutation());
    let capturedUpdater: any;
    
    const mockCurrentData = { 
      data: [
        { id: 'record-1', name: 'Record 1' },
        { id: 'record-2', name: 'Record 2' },
        { id: 'record-3', name: 'Record 3' }
      ] 
    };

    // Mock globalMutate to capture updater functions
    (globalMutate as any).mockImplementation((_key, updateFn) => {
      if (typeof updateFn === 'function') {
        capturedUpdater = updateFn;
      }
    });

    // Test createRecords cache logic
    (RecordAPI.create as any).mockResolvedValueOnce({
      isOk: () => true, isErr: () => false,
      value: { data: { id: 'new-record', name: 'New Record' } },
    });
    await act(async () => {
      await result.current.createRecords('b', 't', { records: { name: 'New Record' } } as any);
    });
    let updatedData = capturedUpdater(mockCurrentData);
    expect(updatedData.data).toHaveLength(4);
    expect(updatedData.data[3]).toEqual({ id: 'new-record', name: 'New Record' });

    // Test duplicateRecord cache logic
    (RecordAPI.create as any).mockResolvedValueOnce({
      isOk: () => true, isErr: () => false,
      value: { data: { id: 'duplicated-record', name: 'Duplicated' } },
    });
    await act(async () => {
      await result.current.duplicateRecord('b', 't', 'record-1', { sortId: 1, records: {} } as any);
    });
    updatedData = capturedUpdater(mockCurrentData);
    expect(updatedData.data).toHaveLength(4);
    expect(updatedData.data[3]).toEqual({ id: 'duplicated-record', name: 'Duplicated' });

    // Test updateRecord cache logic
    (RecordAPI.update as any).mockResolvedValueOnce({
      isOk: () => true, isErr: () => false,
      value: { data: { name: 'Updated Name' } },
    });
    await act(async () => {
      await result.current.updateRecord('b', 't', 'record-1', { records: { name: 'Updated Name' } } as any);
    });
    updatedData = capturedUpdater(mockCurrentData);
    expect(updatedData.data[0]).toEqual({ id: 'record-1', name: 'Updated Name' });

    // Test removeRecord cache logic
    (RecordAPI.remove as any).mockResolvedValueOnce({ isOk: () => true, isErr: () => false });
    await act(async () => {
      await result.current.removeRecord('b', 't', 'record-2');
    });
    updatedData = capturedUpdater(mockCurrentData);
    expect(updatedData.data).toHaveLength(2);
    expect(updatedData.data.map(r => r.id)).toEqual(['record-1', 'record-3']);

    // Test removeRecords cache logic
    (RecordAPI.removeMany as any).mockResolvedValueOnce({ isOk: () => true, isErr: () => false });
    await act(async () => {
      await result.current.removeRecords('b', 't', ['record-1', 'record-3']);
    });
    updatedData = capturedUpdater(mockCurrentData);
    expect(updatedData.data).toHaveLength(1);
    expect(updatedData.data[0].id).toBe('record-2');
  });

});

describe('isTableRecordsKey', () => {
  it('validates table records keys correctly', () => {
    // Valid key
    expect(isTableRecordsKey(['bases/tables/records', 'base123', 'table456'], 'base123', 'table456')).toBe(true);
    
    // Invalid formats
    expect(isTableRecordsKey('not-an-array', 'base123', 'table456')).toBe(false);
    expect(isTableRecordsKey(['bases/tables/records', 'base123'], 'base123', 'table456')).toBe(false);
    expect(isTableRecordsKey(['wrong/key/type', 'base123', 'table456'], 'base123', 'table456')).toBe(false);
    
    // Mismatched IDs
    expect(isTableRecordsKey(['bases/tables/records', 'different-base', 'table456'], 'base123', 'table456')).toBe(false);
    expect(isTableRecordsKey(['bases/tables/records', 'base123', 'different-table'], 'base123', 'table456')).toBe(false);
  });
});

describe('useTableRecordsStream error handling', () => {
  afterEach(() => {
    vi.clearAllMocks();
  });

  it('handles stream onError callback', () => {
    let streamErrorHandler: any;
    const stream = { 
      abort: vi.fn(), 
      onComplete: vi.fn(), 
      onError: vi.fn((cb) => { streamErrorHandler = cb; }) 
    };
    (RecordAPI.stream as any).mockReturnValue(stream);
    
    (useSWR as any).mockImplementation((_key, fetcher, options) => {
      try {
        fetcher();
        // Simulate stream error after stream setup
        if (streamErrorHandler) {
          expect(() => streamErrorHandler('Stream error')).toThrow('Stream error');
        }
      } catch (error) {
        // Handle the expected error to cover lines 49-50
        if (options?.onError) {
          options.onError(error);
        }
      }
      return {
        data: undefined,
        error: undefined,
        isLoading: false,
        isValidating: false,
        mutate: vi.fn(),
      };
    });

    renderHook(() => useTableRecordsStream('b', 't', { enabled: true }));
    
    // The stream.onError should have been called
    expect(stream.onError).toHaveBeenCalled();
  });

  it('handles SWR onError callback', () => {
    const mockError = new Error('SWR error');
    const stream = { abort: vi.fn(), onComplete: vi.fn(), onError: vi.fn() };
    (RecordAPI.stream as any).mockReturnValue(stream);
    
    (useSWR as any).mockImplementation((_key, _fetcher, options) => {
      // Trigger the onError callback to cover lines 54-55
      if (options?.onError) {
        options.onError(mockError);
      }
      return {
        data: undefined,
        error: mockError,
        isLoading: false,
        isValidating: false,
        mutate: vi.fn(),
      };
    });

    renderHook(() => useTableRecordsStream('b', 't', { enabled: true }));
    
    // The onError should have been called, triggering notifications.show
    expect(notifications.show).toHaveBeenCalledWith({
      message: 'SWR error',
    });
  });
});
