import { notifications } from '@/components/Common';
import {
  TableBaseAPI,
  TableBaseAPICreatePayload,
  TableBaseAPIGetListPayload,
  TableBaseAPIUpdatePayload,
  TrashAPI,
} from '@/services/api';
import { useTranslate } from '@tolgee/react';
import { useCallback } from 'react';
import useSWR, { mutate as globalMutate } from 'swr';
// import { TableBase } from '@/types';
// import { ISuccessListResponse } from '@resola-ai/models';

type UseTableBaseOptions = TableBaseAPIGetListPayload;

// type TableBaseQueryData = ISuccessListResponse<TableBase>;

const createTableBasesKey = (options: UseTableBaseOptions) => ['bases/', options];

const isTableBasesKey = (key: unknown) =>
  Array.isArray(key) && key[0] === 'bases/' && typeof key[1] === 'object';

const useTableBaseQuery = (options: UseTableBaseOptions) => {
  const { t } = useTranslate('common');

  const { data, error, isLoading } = useSWR(
    options ? createTableBasesKey(options) : null,
    async () => {
      const res = await TableBaseAPI.getList(options);
      if (res.isOk()) {
        return res.value;
      }
      throw new Error(res.error ? String(res.error) : 'error.base.getList');
    },
    {
      onError: (error: Error) => {
        notifications.show({ message: t(error.message) });
      },
    }
  );

  return {
    tableBases: data?.data ?? [],
    pagination: data?.pagination ?? null,
    isLoading,
    error,
  };
};

const useTableBaseMutation = () => {
  const { t } = useTranslate('common');

  // only update the cache for existing table base query, no revalidation (use for update)
  // const updateQueryCache = useCallback(
  //   (updateFn: (data: TableBaseQueryData) => TableBaseQueryData) => {
  //     globalMutate(
  //       isTableBasesKey,
  //       (currentData?: TableBaseQueryData) => {
  //         if (!currentData) return currentData;
  //         return updateFn(currentData);
  //       },
  //       false
  //     );
  //   },
  //   []
  // );

  const createTableBase = useCallback(
    async (payload: TableBaseAPICreatePayload) => {
      const res = await TableBaseAPI.create(payload);
      if (res.isOk()) {
        globalMutate(isTableBasesKey, undefined);
      }
      if (res.isErr()) {
        notifications.show({ message: t(res.error || 'error.base.create') });
      }
      return res;
    },
    [t]
  );

  const duplicateTableBase = useCallback(
    async (baseId: string, payload: TableBaseAPICreatePayload) => {
      const res = await TableBaseAPI.create({ ...payload, duplicateFromId: baseId });
      if (res.isOk()) {
        globalMutate(isTableBasesKey, undefined);
      }
      if (res.isErr()) {
        notifications.show({ message: t(res.error || 'error.base.duplicate') });
      }
      return res;
    },
    [t]
  );

  const updateTableBase = useCallback(
    async (baseId: string, payload: TableBaseAPIUpdatePayload) => {
      const res = await TableBaseAPI.update(baseId, payload);
      if (res.isOk()) {
        // @TODO: wait for BE return the updated table base in response
        // only update the cache for existing table query, no revalidation
        // const updatedTableBase = res.value.data;
        // updateQueryCache(currentData => ({
        //   ...currentData,
        //   data: currentData.data.map(base => (base.id === baseId ? updatedTableBase : base)),
        // }));
        globalMutate(isTableBasesKey, undefined);
      }
      if (res.isErr()) {
        notifications.show({ message: t(res.error || 'error.base.update') });
      }
      return res;
    },
    [t]
  );

  const removeTableBase = useCallback(
    async (baseId: string) => {
      const res = await TableBaseAPI.remove(baseId);
      if (res.isOk()) {
        globalMutate(isTableBasesKey, undefined);
      }
      if (res.isErr()) {
        notifications.show({ message: t(res.error || 'error.base.remove') });
      }
      return res;
    },
    [t]
  );

  const restoreTableBase = useCallback(
    async (baseId: string) => {
      const res = await TrashAPI.restore(baseId);

      if (res.isOk()) {
        globalMutate(isTableBasesKey, undefined);
      }
      if (res.isErr()) {
        notifications.show({ message: t(res.error || 'error.base.restore') });
      }
      return res;
    },
    [t]
  );

  const permanentlyDeleteTableBase = useCallback(
    async (baseId: string) => {
      const res = await TrashAPI.remove(baseId);

      if (res.isOk()) {
        globalMutate(isTableBasesKey, undefined);
      }
      if (res.isErr()) {
        notifications.show({ message: t(res.error || 'error.base.permanentlyDelete') });
      }
      return res;
    },
    [t]
  );

  return {
    createTableBase,
    updateTableBase,
    removeTableBase,
    restoreTableBase,
    permanentlyDeleteTableBase,
    duplicateTableBase,
  };
};

export { useTableBaseQuery, useTableBaseMutation, isTableBasesKey };
