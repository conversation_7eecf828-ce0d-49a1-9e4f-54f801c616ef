import { TableLayoutType } from '@/constants/table-base';
import { AppContextProvider } from '@/contexts/AppContext';
import { renderWithRouter } from '@/utils/test';
import { fireEvent, screen } from '@testing-library/react';
import { vi, describe, it, expect, beforeEach, afterEach } from 'vitest';
import React from 'react';
import TableBaseTrashPage from './index';

// Mock dependencies
const mockHooks = vi.hoisted(() => ({
  mockUseTableBaseQuery: vi.fn(),
  mockUseTableBaseMutation: vi.fn(),
  mockUsePaginationSearchParams: vi.fn(),
  mockUseWorkspaceTrashActionMenu: vi.fn(),
}));

vi.mock('@/hooks', () => ({
  useTableBaseQuery: mockHooks.mockUseTableBaseQuery,
  useTableBaseMutation: mockHooks.mockUseTableBaseMutation,
  usePaginationSearchParams: mockHooks.mockUsePaginationSearchParams,
  useWorkspaceTrashActionMenu: mockHooks.mockUseWorkspaceTrashActionMenu,
}));

const mockContexts = vi.hoisted(() => ({
  mockUseAppContext: vi.fn(),
}));

vi.mock('@/contexts', () => ({
  useAppContext: mockContexts.mockUseAppContext,
}));

const mockNotifications = vi.hoisted(() => ({
  show: vi.fn(),
}));

vi.mock('@/components/Common', () => ({
  notifications: mockNotifications,
  CreateModal: ({ opened, onSubmit, onCancel, defaultValues }: any) =>
    opened ? (
      <div data-testid="create-modal">
        <div data-testid="modal-default-values">{JSON.stringify(defaultValues)}</div>
        <button
          data-testid="modal-submit"
          onClick={() => onSubmit({ formData: { name: 'Test Base' }, closeModal: vi.fn() })}
        >
          Submit
        </button>
        <button data-testid="modal-cancel" onClick={onCancel}>
          Cancel
        </button>
      </div>
    ) : null,
  ClearanceTrashNotification: ({ amountDeletedToday, amountDeletedNext3Days, locale }: any) => (
    <div data-testid="clearance-trash-notification">
      <div data-testid="deleted-today">{amountDeletedToday}</div>
      <div data-testid="deleted-next-3-days">{amountDeletedNext3Days}</div>
      <div data-testid="locale">{locale}</div>
    </div>
  ),
  SearchBox: ({ placeholder }: any) => (
    <div data-testid="search-box">
      <input placeholder={placeholder} />
    </div>
  ),
  TableTooltip: ({ children, label }: any) => (
    <div data-testid="table-tooltip" title={label}>
      {React.cloneElement(children, { 'aria-label': label })}
    </div>
  ),
}));


vi.mock('@tolgee/react', () => ({
  useTranslate: () => ({ t: (key: string) => key }),
  Tolgee: vi.fn(() => ({
    use: vi.fn().mockReturnThis(),
    init: vi.fn().mockResolvedValue(undefined),
    t: vi.fn((key: string) => key),
  })),
  FormatSimple: vi.fn(),
  InContextTools: vi.fn(),
}));

vi.mock('@/tolgee', () => ({
  tolgee: {
    t: vi.fn((key: string) => key),
  },
}));

// Mock utility functions
vi.mock('@/utils', () => ({
  isPermanentDeleteToday: vi.fn((date) => date === 'today'),
  isPermanentDeleteNext3Days: vi.fn((date) => date === 'next3days'),
  ensureTrailingSlash: vi.fn((path) => path.endsWith('/') ? path : path + '/'),
  createPathWithLngParam: vi.fn((path) => path),
}));

// Mock dayjs
vi.mock('dayjs', () => {
  const mockDayjs = vi.fn((date) => ({
    toISOString: () => date ? new Date(date).toISOString() : new Date().toISOString(),
    format: (_format: string) => date ? new Date(date).toISOString() : new Date().toISOString(),
    utc: () => mockDayjs,
    extend: vi.fn(),
  }));
  (mockDayjs as any).extend = vi.fn();
  return { default: mockDayjs };
});

// Mock @mantine/hooks
const mockHooksMantine = vi.hoisted(() => ({
  mockUseDisclosure: vi.fn(() => [false, { open: vi.fn(), close: vi.fn() }]),
}));

vi.mock('@mantine/hooks', () => ({
  useDisclosure: mockHooksMantine.mockUseDisclosure,
}));

// Mock main components
vi.mock('@/components', () => ({
  GridLayout: ({ children }: any) => <div data-testid="grid-layout">{children}</div>,
  TableEmptyStatus: ({ bases, isLoading, onCreateButton }: any) => (
    <div data-testid="table-empty-status">
      {!isLoading && bases.length === 0 && (
        <div>
          <p>No bases found</p>
          <button data-testid="create-base-btn" onClick={onCreateButton}>Create Base</button>
        </div>
      )}
    </div>
  ),
  TableTrashLayout: ({ bases, isLoading, onMenuActionClick }: any) => (
    <div data-testid="table-trash-layout">
      {isLoading ? (
        <div>Loading...</div>
      ) : (
        bases.map((base: any) => (
          <div key={base.id} data-testid={`base-${base.id}`}>
            {base.name}
            <button 
              data-testid={`trash-menu-action-${base.id}`}
              onClick={(event) => onMenuActionClick({ base, event, position: { x: 0, y: 0 } })}
            >
              Menu
            </button>
          </div>
        ))
      )}
    </div>
  ),
  WorkspaceTrashActionMenu: ({ onRestoreTableBase, onPermanentlyDeleteTableBase }: any) => (
    <div data-testid="workspace-trash-action-menu">
      <button
        data-testid="restore-action"
        onClick={() => onRestoreTableBase({ id: '1', name: 'Test Base', deletedAt: '2023-01-01' })}
      >
        Restore
      </button>
      <button
        data-testid="permanently-delete-action"
        onClick={() => onPermanentlyDeleteTableBase({ id: '1', name: 'Test Base', deletedAt: '2023-01-01' })}
      >
        Permanently Delete
      </button>
    </div>
  ),
  useWorkspaceTrashActionMenu: vi.fn(() => ({ show: vi.fn() })),
  TableTooltip: ({ children, label }: any) => (
    <div data-testid="table-tooltip" title={label}>
      {React.cloneElement(children, { 'aria-label': label })}
    </div>
  ),
}));

// Mock components


vi.mock('@/components/NavBar', () => ({
  TableBaseNavigation: ({ onOpenCreateTableBaseModal }: any) => (
    <div data-testid="table-base-navigation">
      <button data-testid="create-base-btn" onClick={onOpenCreateTableBaseModal}>
        Create Base
      </button>
    </div>
  ),
}));


vi.mock('@/components/WorkspaceList/GridLayout', () => ({
  default: ({ bases, isLoading, onMenuActionClick }: any) => (
    <div data-testid="grid-layout">
      {isLoading ? (
        <div>Loading...</div>
      ) : (
        bases.map((base: any) => (
          <div key={base.id} data-testid={`grid-base-${base.id}`}>
            {base.name}
            <button
              data-testid={`grid-menu-action-${base.id}`}
              onClick={(e) => onMenuActionClick({ base, event: e, position: { x: 0, y: 0 } })}
            >
              Menu
            </button>
          </div>
        ))
      )}
    </div>
  ),
}));

vi.mock('@/components/WorkspaceList/TableEmptyStatus', () => ({
  default: ({ bases, isLoading, search, onCreateButton }: any) => (
    <div data-testid="table-empty-status">
      {isLoading ? (
        <div>Loading...</div>
      ) : bases.length === 0 ? (
        <div>
          {search ? (
            <div data-testid="no-search-results">No results found</div>
          ) : (
            <div>
              <div data-testid="no-bases">No bases found</div>
              <button data-testid="create-empty-btn" onClick={onCreateButton}>
                Create Base
              </button>
            </div>
          )}
        </div>
      ) : null}
    </div>
  ),
}));


const mockTableBases = [
  { id: '1', name: 'Base 1', owner: { name: 'User 1' }, deletedAt: 'today' },
  { id: '2', name: 'Base 2', owner: { name: 'User 2' }, deletedAt: 'next3days' },
  { id: '3', name: 'Base 3', owner: { name: 'User 3' }, deletedAt: '2023-01-01' },
];

describe('TableBaseTrashPage', () => {
  const mockOpenConfirmModal = vi.fn();

  beforeEach(() => {
    vi.clearAllMocks();
    
    // Setup default mocks
    mockHooks.mockUseTableBaseQuery.mockReturnValue({
      isLoading: false,
      tableBases: mockTableBases,
    });
    
    mockHooks.mockUseTableBaseMutation.mockReturnValue({
      createTableBase: vi.fn().mockResolvedValue({ isOk: () => true }),
      restoreTableBase: vi.fn().mockResolvedValue({ isOk: () => true }),
      permanentlyDeleteTableBase: vi.fn().mockResolvedValue({ isOk: () => true }),
    });
    
    mockHooks.mockUsePaginationSearchParams.mockReturnValue({ page: 1, search: '' });
    
    mockHooks.mockUseWorkspaceTrashActionMenu.mockReturnValue({
      show: vi.fn(),
    });
    
    mockContexts.mockUseAppContext.mockReturnValue({
      openConfirmModal: mockOpenConfirmModal,
      setTableDisplayLayout: vi.fn(),
      tableDisplayLayout: TableLayoutType.TABLE,
    });
    
    // Reset useDisclosure mock to default closed state
    mockHooksMantine.mockUseDisclosure.mockReturnValue([false, { open: vi.fn(), close: vi.fn() }]);
  });

  afterEach(() => {
    vi.clearAllMocks();
  });

  describe('Rendering', () => {
    it('should render all components correctly', () => {
      const { container } = renderWithRouter(
        <AppContextProvider>
          <TableBaseTrashPage />
        </AppContextProvider>
      );
      
      // Check main UI elements
      expect(screen.getByText('header.title')).toBeInTheDocument();
      expect(screen.getByTestId('table-base-navigation')).toBeInTheDocument();
      expect(screen.getByText('tabs.all')).toBeInTheDocument();
      expect(screen.getByText('tabs.myBase')).toBeInTheDocument();
      expect(screen.getByTestId('table-trash-layout')).toBeInTheDocument();
      
      // Check that styles are applied (triggers useStyles execution)
      const containerElement = container.querySelector('[data-page-scroll=""]');
      expect(containerElement).toBeInTheDocument();
      expect(containerElement).toHaveClass(/css-/);
    });

    it('should render with loading state', () => {
      mockHooks.mockUseTableBaseQuery.mockReturnValue({
        isLoading: true,
        tableBases: [],
      });

      renderWithRouter(
        <AppContextProvider>
          <TableBaseTrashPage />
        </AppContextProvider>
      );
      
      expect(screen.getByText('Loading...')).toBeInTheDocument();
    });
  });

  describe('Tab Functionality', () => {
    it('should switch between tabs', () => {
      renderWithRouter(
        <AppContextProvider>
          <TableBaseTrashPage />
        </AppContextProvider>
      );
      
      const myWorkspacesTab = screen.getByText('tabs.myBase');
      fireEvent.click(myWorkspacesTab);
      
      expect(myWorkspacesTab).toBeInTheDocument();
    });

    it('should call useTableBaseQuery with correct parameters', () => {
      renderWithRouter(
        <AppContextProvider>
          <TableBaseTrashPage />
        </AppContextProvider>
      );
      
      // Test ALL tab (default)
      expect(mockHooks.mockUseTableBaseQuery).toHaveBeenCalledWith({
        page: 1,
        limit: 200,
        search: '',
        deleted: true,
        me: false,
      });
    });
  });

  describe('Layout Management', () => {
    it('should handle layout switching and display', () => {
      const setTableDisplayLayout = vi.fn();
      mockContexts.mockUseAppContext.mockReturnValue({
        openConfirmModal: mockOpenConfirmModal,
        setTableDisplayLayout,
        tableDisplayLayout: TableLayoutType.TABLE,
      });

      const { container } = renderWithRouter(
        <AppContextProvider>
          <TableBaseTrashPage />
        </AppContextProvider>
      );
      
      // Test that TABLE layout displays the correct component
      expect(screen.getByTestId('table-trash-layout')).toBeInTheDocument();
      
      // Test layout switching buttons
      const layoutButtons = container.querySelectorAll('[data-testid="table-tooltip"] button');
      expect(layoutButtons).toHaveLength(2);
      
      // Test switching to grid layout
      fireEvent.click(layoutButtons[0]);
      expect(setTableDisplayLayout).toHaveBeenCalledWith(TableLayoutType.GRID);
      
      // Test switching to table layout
      fireEvent.click(layoutButtons[1]);
      expect(setTableDisplayLayout).toHaveBeenCalledWith(TableLayoutType.TABLE);
    });

    it('should display grid layout when tableDisplayLayout is GRID', () => {
      mockContexts.mockUseAppContext.mockReturnValue({
        openConfirmModal: mockOpenConfirmModal,
        setTableDisplayLayout: vi.fn(),
        tableDisplayLayout: TableLayoutType.GRID,
      });

      renderWithRouter(
        <AppContextProvider>
          <TableBaseTrashPage />
        </AppContextProvider>
      );
      
      expect(screen.getByTestId('grid-layout')).toBeInTheDocument();
    });
  });

  describe('Modal Operations', () => {
    it('should initialize modal functionality', () => {
      const mockOpen = vi.fn();
      const mockClose = vi.fn();
      
      // Test modal hook initialization
      mockHooksMantine.mockUseDisclosure.mockReturnValue([false, { open: mockOpen, close: mockClose }]);
      
      renderWithRouter(
        <AppContextProvider>
          <TableBaseTrashPage />
        </AppContextProvider>
      );
      
      // Verify useDisclosure hook is used correctly
      expect(mockHooksMantine.mockUseDisclosure).toHaveBeenCalled();
      
      // Verify the create button exists in the empty state
      expect(screen.getByTestId('create-base-btn')).toBeInTheDocument();
      
      // Note: The actual click handler test would require more complex mock setup
      // that connects the TableEmptyStatus onCreateButton prop to the actual handler
    });
  });

  describe('Trash Actions', () => {
    it('should handle restore table base', async () => {
      const mockRestoreTableBase = vi.fn().mockResolvedValue({ isOk: () => true });
      mockHooks.mockUseTableBaseMutation.mockReturnValue({
        createTableBase: vi.fn(),
        restoreTableBase: mockRestoreTableBase,
        permanentlyDeleteTableBase: vi.fn(),
      });

      renderWithRouter(
        <AppContextProvider>
          <TableBaseTrashPage />
        </AppContextProvider>
      );
      
      const restoreButton = screen.getByTestId('restore-action');
      fireEvent.click(restoreButton);
      
      expect(mockRestoreTableBase).toHaveBeenCalledWith('1');
      
      // Wait for async operation to complete and check success notification
      await vi.waitFor(() => {
        expect(mockNotifications.show).toHaveBeenCalledWith({
          message: 'notification.success.restore',
          status: 'success',
        });
      });
    });

    it('should handle permanently delete table base', async () => {
      const mockPermanentlyDeleteTableBase = vi.fn().mockResolvedValue({ isOk: () => true });
      const mockClose = vi.fn();
      
      mockHooks.mockUseTableBaseMutation.mockReturnValue({
        createTableBase: vi.fn(),
        restoreTableBase: vi.fn(),
        permanentlyDeleteTableBase: mockPermanentlyDeleteTableBase,
      });

      renderWithRouter(
        <AppContextProvider>
          <TableBaseTrashPage />
        </AppContextProvider>
      );
      
      const deleteButton = screen.getByTestId('permanently-delete-action');
      fireEvent.click(deleteButton);
      
      // Verify the confirm modal is called
      expect(mockOpenConfirmModal).toHaveBeenCalledWith({
        onConfirm: expect.any(Function),
        confirmButtonType: 'delete',
        cancelText: 'tableBase.noBtn',
        confirmText: 'tableBase.yesBtn',
        title: 'tableBase.deleteConfirmTitle',
        content: 'tableBase.deleteConfirmContent',
      });
      
      // Manually trigger the onConfirm callback to cover lines 104-112
      const onConfirmCallback = mockOpenConfirmModal.mock.calls[0][0].onConfirm;
      await onConfirmCallback({ close: mockClose });
      
      // Verify the delete function was called and success notification shown
      expect(mockPermanentlyDeleteTableBase).toHaveBeenCalledWith('1');
      expect(mockNotifications.show).toHaveBeenCalledWith({
        message: 'notification.success.permanentlyDelete',
        status: 'success',
      });
      expect(mockClose).toHaveBeenCalled();
    });
  });

  describe('Clearance Notification', () => {
    it('should show clearance notification when bases will be deleted', async () => {
      // Mock the utility functions to return true for some bases
      const { isPermanentDeleteToday, isPermanentDeleteNext3Days } = await import('@/utils');
      vi.mocked(isPermanentDeleteToday).mockImplementation((date) => date === 'today');
      vi.mocked(isPermanentDeleteNext3Days).mockImplementation((date) => date === 'next3days');

      renderWithRouter(
        <AppContextProvider>
          <TableBaseTrashPage />
        </AppContextProvider>
      );
      
      expect(screen.getByTestId('clearance-trash-notification')).toBeInTheDocument();
      expect(screen.getByTestId('deleted-today')).toHaveTextContent('1');
      expect(screen.getByTestId('deleted-next-3-days')).toHaveTextContent('1');
      expect(screen.getByTestId('locale')).toHaveTextContent('trash');
    });

    it('should not show clearance notification when no bases will be deleted', async () => {
      // Mock the utility functions to return false for all bases
      const { isPermanentDeleteToday, isPermanentDeleteNext3Days } = await import('@/utils');
      vi.mocked(isPermanentDeleteToday).mockReturnValue(false);
      vi.mocked(isPermanentDeleteNext3Days).mockReturnValue(false);

      renderWithRouter(
        <AppContextProvider>
          <TableBaseTrashPage />
        </AppContextProvider>
      );
      
      expect(screen.queryByTestId('clearance-trash-notification')).not.toBeInTheDocument();
    });
  });

  describe('Empty States', () => {
    it('should show empty state when no bases exist', () => {
      mockHooks.mockUseTableBaseQuery.mockReturnValue({
        isLoading: false,
        tableBases: [],
      });

      renderWithRouter(
        <AppContextProvider>
          <TableBaseTrashPage />
        </AppContextProvider>
      );
      
      expect(screen.getByTestId('table-empty-status')).toBeInTheDocument();
    });

    it('should show search empty state when no results found', () => {
      mockHooks.mockUseTableBaseQuery.mockReturnValue({
        isLoading: false,
        tableBases: [],
      });
      mockHooks.mockUsePaginationSearchParams.mockReturnValue({ page: 1, search: 'notfound' });

      renderWithRouter(
        <AppContextProvider>
          <TableBaseTrashPage />
        </AppContextProvider>
      );
      
      expect(screen.getByTestId('table-empty-status')).toBeInTheDocument();
    });
  });



  describe('Menu Actions', () => {
    it('should set up workspace trash action menu', () => {
      const mockShow = vi.fn();
      mockHooks.mockUseWorkspaceTrashActionMenu.mockReturnValue({
        show: mockShow,
      });

      renderWithRouter(
        <AppContextProvider>
          <TableBaseTrashPage />
        </AppContextProvider>
      );
      
      // Verify the component renders correctly with the layout
      expect(screen.getByTestId('table-trash-layout')).toBeInTheDocument();
      
      // Note: The useWorkspaceTrashActionMenu hook is used inside the component
      // but may not be directly called until a menu action is triggered.
      // This test verifies the component structure is correct.
    });
  });
});