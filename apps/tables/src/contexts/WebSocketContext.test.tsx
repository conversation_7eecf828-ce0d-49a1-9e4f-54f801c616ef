import { AppConfig } from '@/configs';
import { TABLES_WEBSOCKET_EVENTS } from '@/constants/event';
import { CommonAPI } from '@/services/api';
import { IWebsocketResponse } from '@/types';
import { sendCustomEvent } from '@resola-ai/utils';
import { Centrifuge } from 'centrifuge';
import { render, screen, waitFor } from '@testing-library/react';
import { MemoryRouter } from 'react-router-dom';
import { vi, describe, it, expect, beforeEach, afterEach } from 'vitest';
import { WebSocketContextProvider, useWebsocketContext } from './WebSocketContext';

// Mock dependencies
vi.mock('centrifuge', () => {
  const mockCentrifuge = {
    newSubscription: vi.fn(),
    connect: vi.fn(),
    disconnect: vi.fn(),
    on: vi.fn(),
  };
  return {
    Centrifuge: vi.fn(() => mockCentrifuge),
  };
});

vi.mock('@/services/api', () => ({
  CommonAPI: {
    getWsToken: vi.fn(),
  },
}));

vi.mock('@/hooks', () => ({
  useTableFieldsMutation: vi.fn(() => ({
    handleTableFieldUpdateEvent: vi.fn(),
    handleTableFieldCreateEvent: vi.fn(),
    handleTableFieldDeleteEvent: vi.fn(),
  })),
}));

vi.mock('@resola-ai/utils', () => ({
  sendCustomEvent: vi.fn(),
}));

vi.mock('@/configs', () => ({
  AppConfig: {
    WEBSOCKET_URL: 'ws://test-websocket-url',
    IS_PRODUCTION: false,
  },
}));

// Mock react-router-dom
vi.mock('react-router-dom', async () => {
  const actual = await vi.importActual('react-router-dom');
  return {
    ...actual,
    useParams: () => ({ baseId: 'test-base', tableId: 'test-table' }),
    useLocation: () => ({ pathname: '/test-base/test-table' }),
  };
});

// Test component to access context
const TestComponent = () => {
  const context = useWebsocketContext();
  return <div data-testid="context-value">{JSON.stringify(context)}</div>;
};

const renderWithRouter = (ui: React.ReactNode, route = '/test-base/test-table') => {
  return render(
    <MemoryRouter initialEntries={[route]}>
      {ui}
    </MemoryRouter>
  );
};

describe('WebSocketContext', () => {
  let mockCentrifuge: any;
  let mockSubscription: any;
  let mockCommonAPI: any;
  let mockSendCustomEvent: any;

  beforeEach(() => {
    vi.clearAllMocks();
    
    // Setup Centrifuge mock
    mockSubscription = {
      on: vi.fn(),
      subscribe: vi.fn(),
      unsubscribe: vi.fn(),
    };
    
    mockCentrifuge = {
      newSubscription: vi.fn(() => mockSubscription),
      connect: vi.fn(),
      disconnect: vi.fn(),
      on: vi.fn(),
    };
    
    (Centrifuge as any).mockImplementation(() => mockCentrifuge);
    
    // Setup CommonAPI mock
    mockCommonAPI = CommonAPI as any;
    mockCommonAPI.getWsToken.mockResolvedValue({
      isOk: () => true,
      value: {
        wsAccessToken: 'test-ws-token',
        orgId: 'test-org-id',
      },
    });
    
    // Setup useTableFieldsMutation mock is handled in the global mock
    
    // Setup sendCustomEvent mock
    mockSendCustomEvent = sendCustomEvent as any;
  });

  afterEach(() => {
    vi.clearAllMocks();
  });

  describe('WebSocketContextProvider', () => {
    it('should render children and provide context value', () => {
      const { getByText } = renderWithRouter(
        <WebSocketContextProvider>
          <TestComponent />
          <div>Test Child</div>
        </WebSocketContextProvider>
      );
      
      expect(getByText('Test Child')).toBeTruthy();
      expect(screen.getByTestId('context-value')).toBeInTheDocument();
    });

    it('should initialize WebSocket connection correctly', async () => {
      renderWithRouter(
        <WebSocketContextProvider>
          <div>Test</div>
        </WebSocketContextProvider>
      );

      await waitFor(() => {
        // Verify token retrieval
        expect(mockCommonAPI.getWsToken).toHaveBeenCalled();
        
        // Verify Centrifuge configuration
        expect(Centrifuge).toHaveBeenCalledWith(AppConfig.WEBSOCKET_URL, {
          debug: !AppConfig.IS_PRODUCTION,
          token: 'test-ws-token',
          getToken: expect.any(Function),
        });
        
        // Verify subscription setup
        expect(mockCentrifuge.newSubscription).toHaveBeenCalledWith('tables:ORG_test-org-id|test-base-test-table');
        
        // Verify connection and subscription
        expect(mockCentrifuge.connect).toHaveBeenCalled();
        expect(mockSubscription.subscribe).toHaveBeenCalled();
        
        // Verify event handlers
        expect(mockCentrifuge.on).toHaveBeenCalledWith('connected', expect.any(Function));
        expect(mockCentrifuge.on).toHaveBeenCalledWith('error', expect.any(Function));
      });
    });

    it('should cleanup on unmount', async () => {
      const { unmount } = renderWithRouter(
        <WebSocketContextProvider>
          <div>Test</div>
        </WebSocketContextProvider>
      );

      await waitFor(() => {
        expect(mockCentrifuge.connect).toHaveBeenCalled();
      });

      unmount();

      expect(mockSubscription.unsubscribe).toHaveBeenCalled();
      expect(mockCentrifuge.disconnect).toHaveBeenCalled();
    });
  });

  describe('useWebsocketContext', () => {
    it('should throw error when used outside provider', () => {
      // Suppress console.error for this test
      const consoleSpy = vi.spyOn(console, 'error').mockImplementation(() => {});
      
      expect(() => {
        render(<TestComponent />);
      }).toThrow('useWebsocketContext must be used inside WebSocketContextProvider');
      
      consoleSpy.mockRestore();
    });
  });

  describe('WebSocket event handling', () => {
    let publicationHandler: any;
    let mockHandleTableFieldCreateEvent: any;
    let mockHandleTableFieldUpdateEvent: any;
    let mockHandleTableFieldDeleteEvent: any;

    beforeEach(async () => {
      // Setup consolidated mocks for all field events
      mockHandleTableFieldCreateEvent = vi.fn();
      mockHandleTableFieldUpdateEvent = vi.fn();
      mockHandleTableFieldDeleteEvent = vi.fn();
      
      const { useTableFieldsMutation } = await import('@/hooks');
      vi.mocked(useTableFieldsMutation).mockReturnValue({
        createField: vi.fn(),
        updateField: vi.fn(),
        removeField: vi.fn(),
        setPrimaryField: vi.fn(),
        handleTableFieldUpdateEvent: mockHandleTableFieldUpdateEvent,
        handleTableFieldCreateEvent: mockHandleTableFieldCreateEvent,
        handleTableFieldDeleteEvent: mockHandleTableFieldDeleteEvent,
      });

      renderWithRouter(
        <WebSocketContextProvider>
          <div>Test</div>
        </WebSocketContextProvider>
      );

      await waitFor(() => {
        expect(mockSubscription.on).toHaveBeenCalledWith('publication', expect.any(Function));
      });

      publicationHandler = mockSubscription.on.mock.calls.find(
        call => call[0] === 'publication'
      )[1];
    });

    it('should handle FIELDS events', async () => {
      // Test FIELDS_CREATE
      await publicationHandler({ 
        data: {
          type: TABLES_WEBSOCKET_EVENTS.FIELDS_CREATE,
          data: { props: { id: 'field-1', name: 'Test Field' } }
        }
      });
      expect(mockHandleTableFieldCreateEvent).toHaveBeenCalledWith('test-base', 'test-table', { id: 'field-1', name: 'Test Field' });

      // Test FIELDS_UPDATE
      await publicationHandler({ 
        data: {
          type: TABLES_WEBSOCKET_EVENTS.FIELDS_UPDATE,
          data: { props: { id: 'field-1', name: 'Updated Field' } }
        }
      });
      expect(mockHandleTableFieldUpdateEvent).toHaveBeenCalledWith('test-base', 'test-table', { id: 'field-1', name: 'Updated Field' });

      // Test FIELDS_DELETE
      await publicationHandler({ 
        data: {
          type: TABLES_WEBSOCKET_EVENTS.FIELDS_DELETE,
          data: { id: 'field-1' }
        }
      });
      expect(mockHandleTableFieldDeleteEvent).toHaveBeenCalledWith('test-base', 'test-table', 'field-1');
    });

    it('should handle RECORDS and DATA events with sendCustomEvent', async () => {
      const events = [
        {
          type: TABLES_WEBSOCKET_EVENTS.RECORDS_INSERT,
          data: { record: { id: 'record-1' } }
        },
        {
          type: TABLES_WEBSOCKET_EVENTS.RECORDS_UPDATE,
          data: { record: { id: 'record-1' } }
        },
        {
          type: TABLES_WEBSOCKET_EVENTS.RECORDS_DELETE,
          data: { record: { id: 'record-1' } }
        },
        {
          type: TABLES_WEBSOCKET_EVENTS.DATA_IMPORT,
          data: { import: { status: 'completed' } }
        },
        {
          type: TABLES_WEBSOCKET_EVENTS.DATA_CLEAR,
          data: { clear: { status: 'completed' } }
        }
      ];

      for (const eventData of events) {
        await publicationHandler({ data: eventData });
        expect(mockSendCustomEvent).toHaveBeenCalledWith(eventData.type, eventData);
      }
    });

    it('should handle unknown event types gracefully', async () => {
      const eventData: IWebsocketResponse<any> = {
        type: 'UNKNOWN_EVENT' as any,
        data: { some: 'data' }
      };

      // Should not throw error
      expect(() => publicationHandler({ data: eventData })).not.toThrow();
    });
  });

  describe('Error handling and token renewal', () => {
    it('should handle WebSocket token retrieval failure', async () => {
      mockCommonAPI.getWsToken.mockResolvedValue({
        isOk: () => false,
        value: null,
      });

      renderWithRouter(
        <WebSocketContextProvider>
          <div>Test</div>
        </WebSocketContextProvider>
      );

      await waitFor(() => {
        expect(mockCommonAPI.getWsToken).toHaveBeenCalled();
      });

      // Should not create Centrifuge instance if token retrieval fails
      expect(Centrifuge).not.toHaveBeenCalled();
    });

    it('should handle WebSocket errors and renew token for specific error codes', async () => {
      renderWithRouter(
        <WebSocketContextProvider>
          <div>Test</div>
        </WebSocketContextProvider>
      );

      await waitFor(() => {
        expect(mockCentrifuge.on).toHaveBeenCalledWith('error', expect.any(Function));
      });

      const errorHandler = mockCentrifuge.on.mock.calls.find(
        call => call[0] === 'error'
      )[1];

      // Test error code 101 (token expired)
      errorHandler({ error: { code: 101 } });
      expect(mockCommonAPI.getWsToken).toHaveBeenCalledTimes(2); // Initial call + renewal

      // Test error code 109 (token invalid)
      errorHandler({ error: { code: 109 } });
      expect(mockCommonAPI.getWsToken).toHaveBeenCalledTimes(3);

      // Test error code 110 (token not found)
      errorHandler({ error: { code: 110 } });
      expect(mockCommonAPI.getWsToken).toHaveBeenCalledTimes(4);

      // Test other error codes should not trigger renewal
      errorHandler({ error: { code: 999 } });
      expect(mockCommonAPI.getWsToken).toHaveBeenCalledTimes(4);
    });

    it('should handle token renewal failure gracefully', async () => {
      mockCommonAPI.getWsToken
        .mockResolvedValueOnce({
          isOk: () => true,
          value: { wsAccessToken: 'initial-token', orgId: 'test-org-id' },
        })
        .mockResolvedValueOnce({
          isOk: () => false,
          value: null,
        });

      renderWithRouter(
        <WebSocketContextProvider>
          <div>Test</div>
        </WebSocketContextProvider>
      );

      await waitFor(() => {
        expect(mockCentrifuge.on).toHaveBeenCalledWith('error', expect.any(Function));
      });

      const errorHandler = mockCentrifuge.on.mock.calls.find(
        call => call[0] === 'error'
      )[1];

      // Should not throw error when token renewal fails
      expect(() => errorHandler({ error: { code: 101 } })).not.toThrow();
    });

    it('should handle getToken callback failure when renewing token', async () => {
      // First call succeeds to establish connection
      mockCommonAPI.getWsToken
        .mockResolvedValueOnce({
          isOk: () => true,
          value: { wsAccessToken: 'initial-token', orgId: 'test-org-id' },
        })
        // Second call fails (this will test the return '' path)
        .mockResolvedValueOnce({
          isOk: () => false,
          value: null,
        });

      renderWithRouter(
        <WebSocketContextProvider>
          <div>Test</div>
        </WebSocketContextProvider>
      );

      await waitFor(() => {
        expect(Centrifuge).toHaveBeenCalledWith(AppConfig.WEBSOCKET_URL, {
          debug: !AppConfig.IS_PRODUCTION,
          token: 'initial-token',
          getToken: expect.any(Function),
        });
      });

      // Get the getToken callback that was passed to Centrifuge
      const centrifugeConfig = (Centrifuge as any).mock.calls[0][1];
      const getTokenCallback = centrifugeConfig.getToken;

      // Call the getToken callback which should trigger renewWsToken
      const result = await getTokenCallback();

      // When renewWsToken fails (isOk() returns false), it should return empty string
      expect(result).toBe('');
      expect(mockCommonAPI.getWsToken).toHaveBeenCalledTimes(2);
    });

    it('should handle getToken callback success when renewing token', async () => {
      // First call succeeds to establish connection
      mockCommonAPI.getWsToken
        .mockResolvedValueOnce({
          isOk: () => true,
          value: { wsAccessToken: 'initial-token', orgId: 'test-org-id' },
        })
        // Second call succeeds (this will test lines 38-40)
        .mockResolvedValueOnce({
          isOk: () => true,
          value: { wsAccessToken: 'renewed-token', orgId: 'test-org-id' },
        });

      renderWithRouter(
        <WebSocketContextProvider>
          <div>Test</div>
        </WebSocketContextProvider>
      );

      await waitFor(() => {
        expect(Centrifuge).toHaveBeenCalledWith(AppConfig.WEBSOCKET_URL, {
          debug: !AppConfig.IS_PRODUCTION,
          token: 'initial-token',
          getToken: expect.any(Function),
        });
      });

      // Get the getToken callback that was passed to Centrifuge
      const centrifugeConfig = (Centrifuge as any).mock.calls[0][1];
      const getTokenCallback = centrifugeConfig.getToken;

      // Call the getToken callback which should trigger renewWsToken success path
      const result = await getTokenCallback();

      // When renewWsToken succeeds, it should return the new token
      expect(result).toBe('renewed-token');
      expect(mockCommonAPI.getWsToken).toHaveBeenCalledTimes(2);
    });
  });

  describe('Connection events', () => {
    it('should handle connected event gracefully', async () => {
      renderWithRouter(
        <WebSocketContextProvider>
          <div>Test</div>
        </WebSocketContextProvider>
      );

      await waitFor(() => {
        expect(mockCentrifuge.on).toHaveBeenCalledWith('connected', expect.any(Function));
      });

      const connectedHandler = mockCentrifuge.on.mock.calls.find(
        call => call[0] === 'connected'
      )[1];

      // Should not throw error when connected event is triggered
      expect(() => connectedHandler({})).not.toThrow();
    });
  });
});
