import { useAppContext } from '@/contexts';
import {
  useTableQuery,
  useTableRecordsStream,
  useTableViewsQuery,
  useTablesQuery
} from '@/hooks';
import { ViewType, ViewSortOrder } from '@/types';
import { renderWithRouter } from '@/utils/test';
import { fireEvent, screen, waitFor } from '@testing-library/react';
import React from 'react';
import { afterEach, beforeEach, describe, expect, it, vi } from 'vitest';
import TableView from './index';

// Mock dependencies
vi.mock('@tolgee/react', () => ({
  useTranslate: () => ({
    t: (key: string) => key,
  }),
  Tolgee: vi.fn(() => ({
    use: vi.fn().mockReturnThis(),
    init: vi.fn().mockResolvedValue(undefined),
  })),
  FormatSimple: vi.fn(() => ({})),
}));

vi.mock('@tolgee/web/tools', () => ({
  InContextTools: vi.fn(() => ({})),
}));

vi.mock('@/tolgee', () => ({
  tolgee: {
    t: vi.fn((key: string) => key),
  },
}));

vi.mock('react-router-dom', () => ({
  useParams: () => ({ baseId: 'base-1', tableId: 'table-1' }),
  BrowserRouter: ({ children }: { children: React.ReactNode }) => (
    <div data-testid='browser-router'>{children}</div>
  ),
  MemoryRouter: ({ children }: { children: React.ReactNode }) => (
    <div data-testid='memory-router'>{children}</div>
  ),
}));

vi.mock('@/contexts', () => ({
  useAppContext: vi.fn(() => ({
    currentWorkspace: { id: 'workspace-1', name: 'Test Workspace' },
    tableTrashViewModalOpened: false,
    openTableTrashViewModal: vi.fn(),
    closeTableTrashViewModal: vi.fn(),
    pendingClearTableIds: [],
    removePendingClearTableId: vi.fn(),
    addPendingClearTableId: vi.fn(),
  })),
}));

// Mock functions for testing
const mockCreateRecords = vi.fn();
const mockUpdateRecord = vi.fn();
const mockRemoveRecord = vi.fn();
const mockRemoveRecords = vi.fn();
const mockDuplicateRecord = vi.fn();
const mockClearRecords = vi.fn();
const mockCreateView = vi.fn();
const mockUpdateView = vi.fn();
const mockRemoveView = vi.fn();
const mockCreateField = vi.fn();
const mockUpdateField = vi.fn();
const mockRemoveField = vi.fn();
const mockSetPrimaryField = vi.fn();
const mockUpsert = vi.fn();
const mockUpdateOne = vi.fn();
const mockRemoveOne = vi.fn();
const mockRemoveMany = vi.fn();
const mockFindByField = vi.fn();
const mockFindOne = vi.fn();
const mockFindAll = vi.fn();
const mockSearchData = vi.fn();
const mockSubscribe = vi.fn();
const mockFind = vi.fn();
const mockOrderByIds = vi.fn();
const mockUpdateFilter = vi.fn();
const mockUpdatePageIndex = vi.fn();
const mockInitCollection = vi.fn();
const mockAbort = vi.fn();

vi.mock('@/hooks', () => ({
  useTableQuery: vi.fn(() => ({
    isLoading: false,
    key: 'table-key',
  })),
  useTableRecordsStream: vi.fn(() => ({
    abort: mockAbort,
  })),
  useTableRecordsMutation: vi.fn(() => ({
    createRecords: mockCreateRecords,
    removeRecord: mockRemoveRecord,
    removeRecords: mockRemoveRecords,
    updateRecord: mockUpdateRecord,
    duplicateRecord: mockDuplicateRecord,
    isMutating: false,
    clearRecords: mockClearRecords,
  })),
  useTableViewsMutation: vi.fn(() => ({
    createView: mockCreateView,
    removeView: mockRemoveView,
    updateView: mockUpdateView,
  })),
  useTableFieldsMutation: vi.fn(() => ({
    createField: mockCreateField,
    updateField: mockUpdateField,
    removeField: mockRemoveField,
    setPrimaryField: mockSetPrimaryField,
  })),
  useTableViewsQuery: vi.fn(() => ({
    views: [
      { 
        id: 'view-1', 
        name: 'Default View', 
        filter: {},
        sort: [],
        options: { rowHeight: 40 },
      },
    ],
    isInitialLoading: false,
    isLoading: false,
    error: null,
  })),
  useTablesQuery: vi.fn(() => ({
    tables: [
      { 
        id: 'table-1', 
        name: 'Test Table',
        fields: [
          { id: 'field-1', name: 'Name', type: 'text', isPrimary: true, options: {} },
          { id: 'field-2', name: 'Email', type: 'email', options: {} },
        ],
      },
    ],
    isLoading: false,
    error: null,
  })),
}));

vi.mock('@/components/TableView/MiniMongo', () => ({
  useMinimongo: vi.fn(() => ({
    isLoading: false,
    data: [
      { id: 'record-1', 'field-1': 'John Doe', 'field-2': '<EMAIL>', sortId: '1' },
      { id: 'record-2', 'field-1': 'Jane Smith', 'field-2': '<EMAIL>', sortId: '2' },
    ],
    totalDataCount: 2,
    filter: { selectors: {}, sort: [] },
    updateFilter: mockUpdateFilter,
    updatePageIndex: mockUpdatePageIndex,
    upsert: mockUpsert,
    updateOne: mockUpdateOne,
    removeOne: mockRemoveOne,
    initCollection: mockInitCollection,
    removeMany: mockRemoveMany,
    findByField: mockFindByField,
    findOne: mockFindOne,
    findAll: mockFindAll,
    isFetching: false,
    searchData: mockSearchData,
    subscribe: mockSubscribe,
    find: mockFind,
    orderByIds: mockOrderByIds,
  })),
}));

vi.mock('@resola-ai/ui/components', () => ({
  DecaTable: ({ 
    dataCount, 
    selectedView, 
    primaryFieldId, 
    searchMatchedFields, 
    state,
    onCellUpdate,
    onViewChange,
    onCopy,
    onRowSelectionChange,
    onColumnOrderChange,
    onColumnSizingChange,
    onAddNewRow,
    onSearch,
    leftToolbarComponents,
  }: any) => {
    // Trigger callbacks for testing
    React.useEffect(() => {
      if (onCellUpdate) {
        onCellUpdate({ columnId: 'field-1', id: 'record-1', value: 'Updated Value' });
      }
      if (onViewChange) {
        onViewChange('view-1', { id: 'view-1', name: 'Test View' }, 'UPDATE_VIEW');
      }
      if (onCopy) {
        onCopy('test-data', true);
      }
      if (onRowSelectionChange) {
        onRowSelectionChange({ 'record-1': true });
      }
      if (onColumnOrderChange) {
        onColumnOrderChange(['field-1', 'field-2']);
      }
      if (onColumnSizingChange) {
        onColumnSizingChange(() => ({ 'field-1': 200, 'field-2': 150 }));
      }
      if (onAddNewRow) {
        // Test add new row callback
        onAddNewRow();
      }
      if (onSearch) {
        // Test search callback
        onSearch('test search');
      }
    }, []);

    return (
      <div data-testid='deca-table'>
        <div data-testid='data-count'>{dataCount}</div>
        <div data-testid='selected-view'>{selectedView?.name}</div>
        <div data-testid='primary-field'>{primaryFieldId}</div>
        <div data-testid='search-matched'>{Object.keys(searchMatchedFields).length}</div>
        <div data-testid='row-selection'>{Object.keys(state?.rowSelection || {}).length}</div>
        <div data-testid='column-order'>{state?.columnOrder?.join(',')}</div>
        <div data-testid='column-sizing'>{Object.keys(state?.columnSizing || {}).length}</div>
        <div data-testid='column-pinning'>{state?.columnPinning?.left?.join(',')}</div>
        <button 
          data-testid='trigger-callbacks'
          onClick={() => {
            onCellUpdate?.({ columnId: 'field-1', id: 'record-1', value: 'Manual Update' });
            onViewChange?.('view-1', { id: 'view-1', name: 'Manual View' }, 'CREATE_VIEW');
            onCopy?.('manual-copy', true);
            onRowSelectionChange?.({ 'record-2': true });
            onColumnOrderChange?.(['field-2', 'field-1']);
            onColumnSizingChange?.(() => ({ 'field-1': 300, 'field-2': 250 }));
            onAddNewRow?.();
            onSearch?.('manual search');
          }}
        >
          Trigger Callbacks
        </button>
        <button 
          data-testid='trigger-toolbar-callbacks'
          onClick={() => {
            // Extract the onOpenChange functions from leftToolbarComponents
            // Test toolbar callbacks
            if (leftToolbarComponents && Array.isArray(leftToolbarComponents)) {
              leftToolbarComponents.forEach((component) => {
                if (component?.props?.onOpenChange) {
                  // Trigger open then close to cover both branches
                  component.props.onOpenChange(true);
                  component.props.onOpenChange(false);
                }
              });
            }
          }}
        >
          Trigger Toolbar Callbacks
        </button>
      </div>
    );
  },
  AddNewColumnButton: () => <div data-testid='add-column-button'>Add Column Button</div>,
  ColumnForm: () => <div data-testid='column-form'>Column Form</div>,
  // Field types used in constants/table.ts
  TableTextField: () => <div data-testid='table-text-field'>Text Field</div>,
  TableLongTextField: () => <div data-testid='table-long-text-field'>Long Text Field</div>,
  TableEmailField: () => <div data-testid='table-email-field'>Email Field</div>,
  TablePhoneField: () => <div data-testid='table-phone-field'>Phone Field</div>,
  TableURLField: () => <div data-testid='table-url-field'>URL Field</div>,
  TableSingleSelectField: () => (
    <div data-testid='table-single-select-field'>Single Select Field</div>
  ),
  TableMultipleSelectField: () => (
    <div data-testid='table-multiple-select-field'>Multiple Select Field</div>
  ),
  TableDateTimeField: () => <div data-testid='table-datetime-field'>DateTime Field</div>,
  TableCreatedTimeField: () => <div data-testid='table-created-time-field'>Created Time Field</div>,
  TableModifiedTimeField: () => (
    <div data-testid='table-modified-time-field'>Modified Time Field</div>
  ),
  TableImageField: () => <div data-testid='table-image-field'>Image Field</div>,
  TableNumberField: () => <div data-testid='table-number-field'>Number Field</div>,
  TableCheckboxField: () => <div data-testid='table-checkbox-field'>Checkbox Field</div>,
  TableCurrencyField: () => <div data-testid='table-currency-field'>Currency Field</div>,
  TableAutonumberField: () => <div data-testid='table-autonumber-field'>Autonumber Field</div>,
  TablePercentField: () => <div data-testid='table-percent-field'>Percent Field</div>,
  TableCreatedByField: () => <div data-testid='table-created-by-field'>Created By Field</div>,
  TableModifiedByField: () => <div data-testid='table-modified-by-field'>Modified By Field</div>,
}));

vi.mock('@resola-ai/ui/components/DecaTable/constants', () => ({
  DECA_TABLE_CLASSES: {
    DISABLE_ROW: 'disable-row',
    TABLE_CONTAINER: 'table-container',
    TABLE_PAPER: 'table-paper',
    HEADER_EDIT_MENU: 'header-edit-menu',
  },
  TableMenuViewChangeTypes: {
    CREATE_VIEW: 'CREATE_VIEW',
    SWITCH_VIEW: 'SWITCH_VIEW',
  },
  CUSTOM_SELECT_COL_ID: 'custom-select-col-id',
  NEWCOL_ID: 'newcol-id',
}));

vi.mock('@resola-ai/ui/components/DecaTable/components/Toolbar', () => ({
  TableToolbar: () => <div data-testid='table-toolbar'>Table Toolbar</div>,
  FilterToolbarItem: () => <div data-testid='filter-toolbar-item'>Filter Toolbar Item</div>,
  TableFilterToolbarItem: () => (
    <div data-testid='table-filter-toolbar-item'>Table Filter Toolbar Item</div>
  ),
  SortToolbarItem: () => <div data-testid='sort-toolbar-item'>Sort Toolbar Item</div>,
  TableSortToolbarItem: () => (
    <div data-testid='table-sort-toolbar-item'>Table Sort Toolbar Item</div>
  ),
  HeightToolbarItem: () => <div data-testid='height-toolbar-item'>Height Toolbar Item</div>,
  TableHeightToolbarItem: () => (
    <div data-testid='table-height-toolbar-item'>Table Height Toolbar Item</div>
  ),
  AddRowToolbarItem: () => <div data-testid='add-row-toolbar-item'>Add Row Toolbar Item</div>,
  SearchToolbarItem: () => <div data-testid='search-toolbar-item'>Search Toolbar Item</div>,
  ActionMenu: () => <div data-testid='action-menu'>Action Menu</div>,
  TableMenuViewToolbarItem: () => (
    <div data-testid='table-menu-view-toolbar'>Table Menu View Toolbar</div>
  ),
  SelectViewToolbarItem: () => <div data-testid='select-view-toolbar'>Select View Toolbar</div>,
  CustomFieldsToolbarItem: () => (
    <div data-testid='custom-fields-toolbar'>Custom Fields Toolbar</div>
  ),
  TableAddRow: () => <div data-testid='table-add-row'>Table Add Row</div>,
  TableSearch: () => <div data-testid='table-search'>Table Search</div>,
  TableActionMenu: () => <div data-testid='table-action-menu'>Table Action Menu</div>,
  DefaultTableToolbarChangeTypes: {},
  TableActionMenuChangeTypes: {},
  TableCustomFieldsChangeTypes: {},
  TableFilterChangeTypes: {},
  TableSelectViewChangeTypes: {},
}));

vi.mock('@resola-ai/ui/components/DecaTable/components/ContextMenu', () => ({
  HeaderContextMenuItem: () => (
    <div data-testid='header-context-menu-item'>Header Context Menu Item</div>
  ),
  HeaderContextMenuItemSeparator: () => (
    <div data-testid='header-context-menu-separator'>Header Context Menu Separator</div>
  ),
  RowContextMenuItem: () => <div data-testid='row-context-menu-item'>Row Context Menu Item</div>,
  RowContextMenuItemSeparator: () => (
    <div data-testid='row-context-menu-separator'>Row Context Menu Separator</div>
  ),
  NumberInputRowContextMenuItem: () => (
    <div data-testid='number-input-row-context-menu-item'>Number Input Row Context Menu Item</div>
  ),
}));

vi.mock(
  '@resola-ai/ui/components/DecaTable/components/ContextMenu/ConfirmHeaderContextMenuItem',
  () => ({
    ConfirmHeaderContextMenuItem: () => (
      <div data-testid='confirm-header-context-menu'>Confirm Header Context Menu</div>
    ),
  })
);

vi.mock('./EmptyState', () => ({
  EmptyState: vi.fn(({ onAddColumn }) => (
    <div data-testid='empty-state'>
      <span>table.empty</span>
      <button data-testid='add-column-btn' onClick={onAddColumn}>
        table.addColumn
      </button>
    </div>
  )),
}));

describe('TableView', () => {
  beforeEach(() => {
    vi.clearAllMocks();
    // Reset mock implementations
    mockCreateRecords.mockResolvedValue({ isOk: () => true, value: { data: [] } });
    mockUpdateRecord.mockResolvedValue({ isOk: () => true, value: { data: {} } });
    mockRemoveRecord.mockResolvedValue({ isOk: () => true });
    mockRemoveRecords.mockResolvedValue({ isOk: () => true });
    mockDuplicateRecord.mockResolvedValue({ isOk: () => true, value: { data: [] } });
    mockClearRecords.mockResolvedValue({ isOk: () => true });
    mockCreateView.mockResolvedValue({ isOk: () => true, value: { data: { id: 'new-view' } } });
    mockUpdateView.mockResolvedValue({ isOk: () => true });
    mockRemoveView.mockResolvedValue({ isOk: () => true });
    mockCreateField.mockResolvedValue({ isOk: () => true, value: { data: {} } });
    mockUpdateField.mockResolvedValue({ isOk: () => true });
    mockRemoveField.mockResolvedValue({ isOk: () => true });
    mockSetPrimaryField.mockResolvedValue({ isOk: () => true });
    mockUpsert.mockResolvedValue([]);
    mockUpdateOne.mockResolvedValue({});
    mockRemoveOne.mockResolvedValue({});
    mockRemoveMany.mockResolvedValue({});
    mockFindByField.mockResolvedValue([]);
    mockFindOne.mockResolvedValue({});
    mockFindAll.mockResolvedValue([]);
    mockSearchData.mockResolvedValue({});
    mockSubscribe.mockReturnValue(vi.fn());
    mockFind.mockResolvedValue([]);
    mockUpdateFilter.mockImplementation(() => {});
    mockUpdatePageIndex.mockImplementation(() => {});
    mockInitCollection.mockResolvedValue({});
    mockAbort.mockImplementation(() => {});
  });

  afterEach(() => {
    vi.clearAllMocks();
  });

  describe('Basic Rendering', () => {
    it('renders table with correct props and data', () => {
      renderWithRouter(<TableView />);
      
      expect(screen.getByTestId('deca-table')).toBeInTheDocument();
      expect(screen.getByTestId('data-count')).toHaveTextContent('2');
      expect(screen.getByTestId('selected-view')).toHaveTextContent('Default View');
      expect(screen.getByTestId('primary-field')).toHaveTextContent('field-1');
    });
  });

  describe('Loading States', () => {
    it('renders table during view loading state', () => {
      vi.mocked(useTableViewsQuery).mockReturnValue({
        views: [],
        isInitialLoading: true,
        isLoading: true,
        isValidating: false,
        error: null,
      });

      renderWithRouter(<TableView />);
      expect(screen.queryByTestId('deca-table')).toBeInTheDocument();
    });
    });

  describe('Empty State', () => {


    it('handles empty state add column callback', () => {
      // Mock empty table
      vi.mocked(useTablesQuery).mockReturnValue({
        tables: [
          {
            id: 'table-1',
            name: 'Empty Table',
            baseId: 'base-1',
            description: 'Empty table description',
            meta: {},
            fields: [], // No fields to trigger isEmpty condition
            data: {},
            createdAt: '2023-01-01T00:00:00Z',
            updatedAt: '2023-01-01T00:00:00Z',
            deletedAt: '',
          },
        ],
        pagination: null,
        isLoading: false,
        isValidating: false,
        isInitialLoading: false,
        error: null,
      });

      renderWithRouter(<TableView />);

      // First verify EmptyState is shown
      expect(screen.getByTestId('empty-state')).toBeInTheDocument();

      // Click the add column button to trigger the onAddColumn callback
      // Should hide empty state and open column form
      const addColumnButton = screen.getByTestId('add-column-btn');
      fireEvent.click(addColumnButton);

      // Test add column callback
      // Note: The callback execution is what we're testing, not the UI change afterward
    });
  });


  describe('WebSocket Events', () => {
    it('handles websocket events for record operations', async () => {
      renderWithRouter(<TableView />);
      
      await waitFor(() => {
      expect(screen.getByTestId('deca-table')).toBeInTheDocument();
    });

      // Simulate websocket record delete event
      const deleteEvent = new CustomEvent('records.delete', {
        detail: {
          data: {
            recordIds: ['record-1', 'record-2'],
          },
        },
      });
      window.dispatchEvent(deleteEvent);

      // Simulate websocket record insert event
      const insertEvent = new CustomEvent('records.insert', {
        detail: {
          data: [
            { _id: 'new-record-1', name: 'New Record 1' },
            { _id: 'new-record-2', name: 'New Record 2' },
          ],
        },
      });
      window.dispatchEvent(insertEvent);

      // Simulate websocket record update event
      const updateEvent = new CustomEvent('records.update', {
        detail: {
          data: {
            _id: 'record-1',
            name: 'Updated Record',
          },
        },
      });
      window.dispatchEvent(updateEvent);

      // Wait for the event handlers to process
      await waitFor(() => {
        // Verify that the websocket handlers called the appropriate minimongo functions
        expect(mockRemoveMany).toHaveBeenCalledWith(['record-1', 'record-2']);
        expect(mockUpsert).toHaveBeenCalledWith([
          { id: 'new-record-1', _id: 'new-record-1', name: 'New Record 1' },
          { id: 'new-record-2', _id: 'new-record-2', name: 'New Record 2' },
        ]);
        expect(mockUpdateOne).toHaveBeenCalledWith('record-1', {
          _id: 'record-1',
          name: 'Updated Record',
    });
    });
  });



    it('returns null when table should not be shown', () => {
      // Mock empty tables array
      vi.mocked(useTablesQuery).mockReturnValueOnce({
        tables: [],
        pagination: null,
        isLoading: false,
        isValidating: false,
        isInitialLoading: false,
        error: null,
      });

      const { container } = renderWithRouter(<TableView />);

      // Should return null when table shouldn't be shown
      // The container should be empty or only contain minimal content
      expect(container.firstChild).toBeTruthy(); // Mantine styles might still render
    });

  });

  describe('Component Lifecycle', () => {

    it('cleans up properly when component unmounts', () => {
      const { unmount } = renderWithRouter(<TableView />);
      
      const decaTable = screen.getByTestId('deca-table');
      expect(decaTable).toBeInTheDocument();
      
      // Should cleanup on unmount
      unmount();
      expect(screen.queryByTestId('deca-table')).not.toBeInTheDocument();

      // Verify abort was called for cleanup
      expect(mockAbort).toHaveBeenCalled();
    });

    it('responds to view ID changes', () => {
      // Mock different view being selected
      vi.mocked(useTableViewsQuery).mockReturnValue({
        views: [
          {
            id: 'view-1',
            name: 'View 1',
            description: 'Test view 1',
            baseId: 'base-1',
            tableId: 'table-1',
            type: ViewType.GRID,
            filter: {},
            sort: {},
            project: {},
            group: {},
            sortHeader: [],
            options: { rowHeight: 40 },
          },
          {
            id: 'view-2',
            name: 'View 2',
            description: 'Test view 2',
            baseId: 'base-1',
            tableId: 'table-1',
            type: ViewType.GRID,
            filter: { field1: 'test' },
            sort: {},
            project: {},
            group: {},
            sortHeader: [],
            options: { rowHeight: 34 },
          },
        ],
        isInitialLoading: false,
        isLoading: false,
        isValidating: false,
        error: null,
      });

      renderWithRouter(<TableView />);
      
      const decaTable = screen.getByTestId('deca-table');
      expect(decaTable).toBeInTheDocument();
      expect(screen.getByTestId('selected-view')).toHaveTextContent('View 1');
    });
  });


  describe('Edge Cases', () => {

    it('processes view display logic correctly', () => {
      // Mock table views with specific configurations to trigger displayViews useMemo
      vi.mocked(useTableViewsQuery).mockReturnValueOnce({
        views: [
          {
            id: 'view-1',
            name: 'Custom View',
            type: ViewType.GRID,
            description: 'A custom view',
            baseId: 'base-1',
            tableId: 'table-1',
            filter: { field1: 'value1' },
            sort: { 'field-1': ViewSortOrder.ASC },
            project: { 'field-1': 1, 'field-2': 2 },
            group: {},
            sortHeader: ['field-2', 'field-1'],
            options: {
              rowHeight: 34,
              automaticSorting: true,
            },
          },
        ],
        isInitialLoading: false,
        isLoading: false,
        isValidating: false,
        error: null,
      });

      renderWithRouter(<TableView />);
      
      const decaTable = screen.getByTestId('deca-table');
      expect(decaTable).toBeInTheDocument();
      
      // The displayViews useMemo should process the view configuration
      // and generate display fields, column order, filters, sort, etc.
      });
    });








  describe('Utility Functions Coverage', () => {
    it('covers useTableQuery onSuccess callback with isCleaningData', () => {
      // Mock addPendingClearTableId and removePendingClearTableId
      const mockAddPendingClearTableId = vi.fn();
      const mockRemovePendingClearTableId = vi.fn();
      
      vi.mocked(useAppContext).mockReturnValue({
        tableTrashViewModalOpened: false,
        openTableTrashViewModal: vi.fn(),
        closeTableTrashViewModal: vi.fn(),
        pendingClearTableIds: [],
        addPendingClearTableId: mockAddPendingClearTableId,
        removePendingClearTableId: mockRemovePendingClearTableId,
      } as any);

      // Create a mock that captures the onSuccess callback and calls it
      let capturedOnSuccess: any = null;
      vi.mocked(useTableQuery).mockImplementation((_baseId, _tableId, options) => {
        capturedOnSuccess = options?.onSuccess;
        return {
          table: null,
          isLoading: false,
          isValidating: false,
          mutate: vi.fn(),
          error: null,
          key: ['test-table-key'],
        };
      });

      renderWithRouter(<TableView />);
      
      // Now simulate calling the onSuccess callback with isCleaningData = true
      if (capturedOnSuccess) {
        capturedOnSuccess({
          data: {
            system: {
              isCleaningData: true  // Triggers cleanup
            }
          }
        });
      }

      // Should add table to pending clear list
      expect(mockAddPendingClearTableId).toHaveBeenCalledWith('table-1');

      // Test when table is not cleaning
      if (capturedOnSuccess) {
        capturedOnSuccess({
          data: {
            system: {
              isCleaningData: false
            }
          }
        });
      }

      // Should remove table from pending clear list
      expect(mockRemovePendingClearTableId).toHaveBeenCalledWith('table-1');
    });

    it('covers pinned record subscription handlers', () => {
      // Mock the subscribe function to capture the event handler and trigger it
      let capturedEventHandler: any = null;
      mockSubscribe.mockImplementation((handler) => {
        capturedEventHandler = handler;
        return vi.fn(); // unsubscribe function
      });

      // Mock find and orderByIds functions for pinned records
      const mockRecords = [
        { id: 'pinned-1', name: 'Record 1' },
        { id: 'pinned-2', name: 'Record 2' }
      ];
      mockFind.mockResolvedValue(mockRecords);
      mockOrderByIds.mockReturnValue(mockRecords);

      renderWithRouter(<TableView />);
      
      // Verify the component rendered and subscription was set up
      expect(capturedEventHandler).toBeDefined();

      // Test remove pinned record handler
      if (capturedEventHandler) {
        capturedEventHandler({
          type: 'remove',
          docs: [{ id: 'pinned-1' }] // Remove pinned record
        });
      }

      // Test update pinned record handler  
      if (capturedEventHandler) {
        capturedEventHandler({
          type: 'update',
          docs: [{ id: 'pinned-1' }] // Update pinned record
        });
      }

      expect(screen.getByTestId('deca-table')).toBeInTheDocument();
    });

    it('covers auto-ID processing in useTableRecordsStream', () => {
      // Mock useTableRecordsStream to capture and call onSuccess with auto-ID data
      let capturedOnSuccess: any = null;
      vi.mocked(useTableRecordsStream).mockImplementation((_baseId, _tableId, options) => {
        capturedOnSuccess = options?.onSuccess;
        return { 
          data: undefined,
          error: null,
          isLoading: false,
          isValidating: false,
          mutate: vi.fn(),
          abort: vi.fn() 
        };
      });

      renderWithRouter(<TableView />);

      // Simulate onSuccess callback with data needing auto-ID processing
      if (capturedOnSuccess) {
        capturedOnSuccess({
          data: [
            { id: 'rec1', field1: 'value1' }, // Will get autoId processing
            { id: 'rec2', field2: 'value2', autoId: 42 }
          ]
        });
      }

      expect(screen.getByTestId('deca-table')).toBeInTheDocument();
    });
  });

  describe('Callback Functions', () => {
    it('handles cell updates and calls record API', async () => {
      renderWithRouter(<TableView />);
      
      // Wait for the useEffect to trigger the callback
      await waitFor(() => {
        expect(mockUpdateOne).toHaveBeenCalledWith('record-1', { 'field-1': 'Updated Value' });
        expect(mockUpdateRecord).toHaveBeenCalledWith('base-1', 'table-1', 'record-1', {
          'field-1': 'Updated Value',
          sortId: undefined,
        });
      });
    });

    it('handles view changes and calls view API', async () => {
      renderWithRouter(<TableView />);
      
      // Wait for the useEffect to trigger the callback
      await waitFor(() => {
        expect(mockUpdateView).toHaveBeenCalled();
      });
    });
  });
});
