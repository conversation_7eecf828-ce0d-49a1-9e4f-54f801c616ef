import { describe, it, expect } from 'vitest';
import { z } from 'zod';
import { primaryFieldFormSchema } from './schema';

describe('PrimaryField Schema', () => {
  describe('primaryFieldFormSchema', () => {
    it('should validate a valid fieldId', () => {
      const validData = {
        fieldId: 'field-123',
      };

      const result = primaryFieldFormSchema.safeParse(validData);
      expect(result.success).toBe(true);
      if (result.success) {
        expect(result.data.fieldId).toBe('field-123');
      }
    });

    it('should validate with a long fieldId', () => {
      const validData = {
        fieldId: 'very-long-field-id-with-many-characters',
      };

      const result = primaryFieldFormSchema.safeParse(validData);
      expect(result.success).toBe(true);
      if (result.success) {
        expect(result.data.fieldId).toBe('very-long-field-id-with-many-characters');
      }
    });

    it('should validate with a single character fieldId', () => {
      const validData = {
        fieldId: 'a',
      };

      const result = primaryFieldFormSchema.safeParse(validData);
      expect(result.success).toBe(true);
      if (result.success) {
        expect(result.data.fieldId).toBe('a');
      }
    });

    it('should fail when fieldId is missing', () => {
      const invalidData = {};

      const result = primaryFieldFormSchema.safeParse(invalidData);
      expect(result.success).toBe(false);
      if (!result.success) {
        expect(result.error.issues[0].path).toEqual(['fieldId']);
        expect(result.error.issues[0].code).toBe(z.ZodIssueCode.invalid_type);
      }
    });

    it('should fail when fieldId is not a string', () => {
      const invalidData = {
        fieldId: 123,
      };

      const result = primaryFieldFormSchema.safeParse(invalidData);
      expect(result.success).toBe(false);
      if (!result.success) {
        expect(result.error.issues[0].path).toEqual(['fieldId']);
        expect(result.error.issues[0].code).toBe(z.ZodIssueCode.invalid_type);
      }
    });

    it('should fail when fieldId is an empty string', () => {
      const invalidData = {
        fieldId: '',
      };

      const result = primaryFieldFormSchema.safeParse(invalidData);
      expect(result.success).toBe(false);
      if (!result.success) {
        expect(result.error.issues[0].path).toEqual(['fieldId']);
        expect(result.error.issues[0].code).toBe(z.ZodIssueCode.too_small);
      }
    });

    it('should fail when fieldId is null', () => {
      const invalidData = {
        fieldId: null,
      };

      const result = primaryFieldFormSchema.safeParse(invalidData);
      expect(result.success).toBe(false);
      if (!result.success) {
        expect(result.error.issues[0].path).toEqual(['fieldId']);
        expect(result.error.issues[0].code).toBe(z.ZodIssueCode.invalid_type);
      }
    });

    it('should fail when fieldId is undefined', () => {
      const invalidData = {
        fieldId: undefined,
      };

      const result = primaryFieldFormSchema.safeParse(invalidData);
      expect(result.success).toBe(false);
      if (!result.success) {
        expect(result.error.issues[0].path).toEqual(['fieldId']);
        expect(result.error.issues[0].code).toBe(z.ZodIssueCode.invalid_type);
      }
    });

    it('should fail when fieldId is a boolean', () => {
      const invalidData = {
        fieldId: true,
      };

      const result = primaryFieldFormSchema.safeParse(invalidData);
      expect(result.success).toBe(false);
      if (!result.success) {
        expect(result.error.issues[0].path).toEqual(['fieldId']);
        expect(result.error.issues[0].code).toBe(z.ZodIssueCode.invalid_type);
      }
    });

    it('should fail when fieldId is an object', () => {
      const invalidData = {
        fieldId: { id: 'test' },
      };

      const result = primaryFieldFormSchema.safeParse(invalidData);
      expect(result.success).toBe(false);
      if (!result.success) {
        expect(result.error.issues[0].path).toEqual(['fieldId']);
        expect(result.error.issues[0].code).toBe(z.ZodIssueCode.invalid_type);
      }
    });

    it('should fail when fieldId is an array', () => {
      const invalidData = {
        fieldId: ['test'],
      };

      const result = primaryFieldFormSchema.safeParse(invalidData);
      expect(result.success).toBe(false);
      if (!result.success) {
        expect(result.error.issues[0].path).toEqual(['fieldId']);
        expect(result.error.issues[0].code).toBe(z.ZodIssueCode.invalid_type);
      }
    });

    it('should handle extra properties by ignoring them', () => {
      const validData = {
        fieldId: 'field-123',
        extraProperty: 'should-be-ignored',
        anotherProperty: 123,
      };

      const result = primaryFieldFormSchema.safeParse(validData);
      expect(result.success).toBe(true);
      if (result.success) {
        expect(result.data.fieldId).toBe('field-123');
        expect((result.data as any).extraProperty).toBeUndefined();
        expect((result.data as any).anotherProperty).toBeUndefined();
      }
    });
  });

  describe('Type inference', () => {
    it('should correctly infer the type', () => {
      // This test ensures the type is properly inferred
      const data: z.infer<typeof primaryFieldFormSchema> = {
        fieldId: 'test-field-id',
      };

      expect(data.fieldId).toBe('test-field-id');
      expect(typeof data.fieldId).toBe('string');
    });
  });
});