import { render, screen, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { MantineProvider } from '@mantine/core';
import { emotionTransform, MantineEmotionProvider } from '@mantine/emotion';
import PrimaryFieldForm from './PrimaryFieldForm';
import type { PrimaryFieldFormSubmitCallback } from './PrimaryFieldForm';

// Mock @tolgee/react
const mockT = vi.fn((key: string) => key);
vi.mock('@tolgee/react', () => ({
  useTranslate: () => ({ t: mockT }),
}));

// Mock @/hooks
const mockTables = [
  {
    id: 'table1',
    name: 'Test Table',
    fields: [
      {
        id: 'field1',
        name: 'Field 1',
        type: 'TEXT',
        isPrimary: false,
      },
      {
        id: 'field2',
        name: 'Field 2',
        type: 'NUMBER',
        isPrimary: true,
      },
      {
        id: 'field3',
        name: 'Field 3',
        type: 'TEXT',
        isPrimary: false,
      },
    ],
  },
];

vi.mock('@/hooks', () => ({
  useTablesQuery: vi.fn(() => ({
    tables: mockTables,
  })),
}));

// Mock @/constants/table
vi.mock('@/constants/table', () => ({
  SUPPORTED_PRIMARY_FIELD_TYPES: ['TEXT', 'NUMBER', 'EMAIL'],
}));

// Mock @mantine/core
vi.mock('@mantine/core', async (importOriginal) => {
  const actual = await importOriginal();
  return {
    ...(actual as any),
    Box: ({ children, className, ...props }: any) => (
      <div className={className} data-testid="box" {...props}>
        {children}
      </div>
    ),
    Group: ({ children, className, ...props }: any) => (
      <div className={className} data-testid="group" {...props}>
        {children}
      </div>
    ),
    rem: (value: number) => `${value}px`,
  };
});

// Mock @mantine/emotion to force execution of all style code
vi.mock('@mantine/emotion', async (importOriginal) => {
  const actual = await importOriginal();
  return {
    ...(actual as any),
    createStyles: (fn: any) => {
      // Execute the style function to force coverage of all style definitions
      const mockTheme = {
        colors: {
          decaLight: [null, null, '#f0f0f0'],
          decaGrey: [null, null, null, null, null, null, null, null, null, '#666666'],
        },
        spacing: { xs: 10, sm: 12, md: 16, lg: 20, xl: 24 },
        radius: { xl: 16 },
        fontSizes: { xs: 12, sm: 14, md: 16, lg: 18, xl: 20 },
        lineHeights: { xs: 1.2, sm: 1.4, md: 1.6, lg: 1.8, xl: 2 },
        fontWeights: { normal: 400, medium: 500, semibold: 600, bold: 700 },
        shadows: { xs: '0 1px 3px rgba(0, 0, 0, 0.12)', sm: '0 1px 3px rgba(0, 0, 0, 0.12)' },
        breakpoints: { xs: '576px', sm: '768px', md: '992px', lg: '1200px', xl: '1400px' },
        other: {},
        fn: {
          smallerThan: (breakpoint: string) => `@media (max-width: ${breakpoint})`,
          largerThan: (breakpoint: string) => `@media (min-width: ${breakpoint})`,
          lighten: (color: string, _alpha: number) => color,
          darken: (color: string, _alpha: number) => color,
          rgba: (color: string, _alpha: number) => color,
        },
      };
      
      try {
        fn(mockTheme);
      } catch (error) {
        // Ignore errors, we just want to execute the function
      }
      
      return () => ({
        classes: {
          bodyContent: 'body-content-class',
          bodyFooter: 'body-footer-class',
          select: 'select-class',
        },
      });
    },
  };
});

// Mock @resola-ai/ui
vi.mock('@resola-ai/ui', () => ({
  DecaButton: ({ children, onClick, disabled, loading, type, variant, size, ...props }: any) => (
    <button
      onClick={onClick}
      disabled={disabled || loading}
      type={type}
      data-testid="deca-button"
      data-variant={variant}
      data-size={size}
      data-loading={loading}
      {...props}
    >
      {children}
    </button>
  ),
}));

// Mock react-hook-form-mantine
vi.mock('react-hook-form-mantine', () => ({
  Select: ({ name, control, label, placeholder, data, size, className, withCheckIcon, allowDeselect, ...props }: any) => (
    <div className={className} data-testid="select">
      <label>{label}</label>
      <select
        name={name}
        data-testid={`select-${name}`}
        data-placeholder={placeholder}
        data-size={size}
        data-with-check-icon={withCheckIcon}
        data-allow-deselect={allowDeselect}
        {...props}
      >
        {data?.map((option: any) => (
          <option key={option.value} value={option.value}>
            {option.label}
          </option>
        ))}
      </select>
    </div>
  ),
}));

// Mock react-hook-form
const mockHandleSubmit = vi.fn();
const mockControl = {};
const mockWatch = vi.fn();
const mockFormState = { isSubmitting: false, isValid: true };

vi.mock('react-hook-form', () => ({
  useForm: () => ({
    handleSubmit: mockHandleSubmit,
    control: mockControl,
    formState: mockFormState,
    watch: mockWatch,
  }),
}));

// Mock @hookform/resolvers/zod
vi.mock('@hookform/resolvers/zod', () => ({
  zodResolver: () => ({}),
}));

// Create a Mantine wrapper for proper theme support
const MantineWrapper = ({ children }: { children: React.ReactNode }) => (
  <MantineProvider stylesTransform={emotionTransform}>
    <MantineEmotionProvider>{children}</MantineEmotionProvider>
  </MantineProvider>
);

const renderWithMantine = (ui: React.ReactNode) => {
  return render(<MantineWrapper>{ui}</MantineWrapper>);
};

describe('PrimaryField/PrimaryFieldForm', () => {
  const mockOnSubmit: PrimaryFieldFormSubmitCallback = vi.fn();
  const mockOnCancel = vi.fn();

  const defaultProps = {
    baseId: 'base1',
    tableId: 'table1',
    onSubmit: mockOnSubmit,
    onCancel: mockOnCancel,
  };

  beforeEach(() => {
    vi.clearAllMocks();
    mockT.mockImplementation((key: string) => key);
    mockWatch.mockReturnValue('');
    mockFormState.isSubmitting = false;
    mockFormState.isValid = true;
  });

  describe('Rendering', () => {
  it('should render without crashing', () => {
      renderWithMantine(<PrimaryFieldForm {...defaultProps} />);
      
      expect(screen.getByTestId('box')).toBeInTheDocument();
      expect(screen.getByTestId('group')).toBeInTheDocument();
    });

    it('should render form with correct structure', () => {
      renderWithMantine(<PrimaryFieldForm {...defaultProps} />);
      
      expect(screen.getByTestId('box')).toBeInTheDocument();
      expect(screen.getByTestId('select')).toBeInTheDocument();
      expect(screen.getAllByTestId('deca-button')).toHaveLength(2);
    });

    it('should render with default values', () => {
      const defaultValues = { fieldId: 'field1' };
      renderWithMantine(<PrimaryFieldForm {...defaultProps} defaultValues={defaultValues} />);
      
      expect(screen.getByTestId('select-fieldId')).toBeInTheDocument();
    });

    it('should render without baseId and tableId', () => {
      renderWithMantine(<PrimaryFieldForm onSubmit={mockOnSubmit} onCancel={mockOnCancel} />);
      
      expect(screen.getByTestId('box')).toBeInTheDocument();
    });
  });

  describe('Field Options', () => {
    it('should filter fields by supported types', () => {
      renderWithMantine(<PrimaryFieldForm {...defaultProps} />);
      
      const select = screen.getByTestId('select-fieldId');
      const options = select.querySelectorAll('option');
      
      // Should only include TEXT and NUMBER fields (EMAIL is supported but not in mock data)
      expect(options).toHaveLength(3); // 2 TEXT fields + 1 NUMBER field
    });
  });

  describe('Form Validation', () => {
    it('should disable submit button when form is invalid', () => {
      mockFormState.isValid = false;
      
      renderWithMantine(<PrimaryFieldForm {...defaultProps} />);
      
      const submitButton = screen.getAllByTestId('deca-button')[1];
      expect(submitButton).toBeDisabled();
    });

    it('should disable submit button when fieldId is same as default', () => {
      const defaultValues = { fieldId: 'field1' };
      mockWatch.mockReturnValue('field1');
      
      renderWithMantine(<PrimaryFieldForm {...defaultProps} defaultValues={defaultValues} />);
      
      const submitButton = screen.getAllByTestId('deca-button')[1];
      expect(submitButton).toBeDisabled();
    });

    it('should enable submit button when form is valid and fieldId is different', () => {
      mockFormState.isValid = true;
      mockWatch.mockReturnValue('field3');
      
      renderWithMantine(<PrimaryFieldForm {...defaultProps} />);
      
      const submitButton = screen.getAllByTestId('deca-button')[1];
      expect(submitButton).not.toBeDisabled();
    });
  });

  describe('Form Submission', () => {
    it('should call handleSubmit with correct parameters', () => {
      renderWithMantine(<PrimaryFieldForm {...defaultProps} />);
      
      expect(mockHandleSubmit).toHaveBeenCalledWith(expect.any(Function));
    });

    it('should handle form submission with valid data', async () => {
      const mockOnSubmitFn = vi.fn().mockResolvedValue(undefined);
      mockHandleSubmit.mockImplementation((callback) => {
        // Return a function that will call the callback with valid data
        return () => callback({ fieldId: 'field1' });
      });
      
      renderWithMantine(<PrimaryFieldForm {...defaultProps} onSubmit={mockOnSubmitFn} />);
      
      const submitButton = screen.getAllByTestId('deca-button')[1];
      await userEvent.click(submitButton);
      
      expect(mockOnSubmitFn).toHaveBeenCalled();
    });

    it('should handle submission when field is already primary', async () => {
      const mockOnSubmitFn = vi.fn();
      mockHandleSubmit.mockImplementation((callback) => {
        // Return a function that will call the callback with primary field
        return () => callback({ fieldId: 'field2' });
      });
      
      renderWithMantine(<PrimaryFieldForm {...defaultProps} onSubmit={mockOnSubmitFn} />);
      
      const submitButton = screen.getAllByTestId('deca-button')[1];
      await userEvent.click(submitButton);
      
      expect(mockOnSubmitFn).not.toHaveBeenCalled();
    });

    it('should handle submission when field is not found', async () => {
      const mockOnSubmitFn = vi.fn();
      mockHandleSubmit.mockImplementation((callback) => () => callback({ fieldId: 'nonexistent' }));
      
      renderWithMantine(<PrimaryFieldForm {...defaultProps} onSubmit={mockOnSubmitFn} />);
      
      const submitButton = screen.getAllByTestId('deca-button')[1];
      await userEvent.click(submitButton);
      
      expect(mockOnSubmitFn).not.toHaveBeenCalled();
    });

    it('should handle submission error', async () => {
      const consoleSpy = vi.spyOn(console, 'log').mockImplementation(() => {});
      const mockOnSubmitFn = vi.fn().mockRejectedValue(new Error('Test error'));
      mockHandleSubmit.mockImplementation((callback) => {
        // Return a function that will call the callback and trigger error
        return () => callback({ fieldId: 'field1' });
      });
      
      renderWithMantine(<PrimaryFieldForm {...defaultProps} onSubmit={mockOnSubmitFn} />);
      
      const submitButton = screen.getAllByTestId('deca-button')[1];
      await userEvent.click(submitButton);
      
      await waitFor(() => {
        expect(consoleSpy).toHaveBeenCalledWith(expect.any(Error));
      });
      
      consoleSpy.mockRestore();
    });

    it('should handle missing onSubmit prop', async () => {
      mockHandleSubmit.mockImplementation((callback) => callback);
      
      renderWithMantine(<PrimaryFieldForm onCancel={mockOnCancel} onSubmit={vi.fn()} />);
      
      const submitButton = screen.getAllByTestId('deca-button')[1];
      await userEvent.click(submitButton);
      
      // Should not crash when onSubmit is undefined
      expect(submitButton).toBeInTheDocument();
    });

    it('should handle successful form submission with field data', async () => {
      const mockOnSubmitFn = vi.fn().mockResolvedValue(undefined);
      mockHandleSubmit.mockImplementation((callback) => {
        // Return a function that will call the callback with non-primary field
        return () => callback({ fieldId: 'field1' });
      });
      
      renderWithMantine(<PrimaryFieldForm {...defaultProps} onSubmit={mockOnSubmitFn} />);
      
      const submitButton = screen.getAllByTestId('deca-button')[1];
      await userEvent.click(submitButton);
      
      expect(mockOnSubmitFn).toHaveBeenCalled();
    });
  });

  describe('Button Interactions', () => {
    it('should call onCancel when cancel button is clicked', async () => {
      renderWithMantine(<PrimaryFieldForm {...defaultProps} />);
      
      const cancelButton = screen.getAllByTestId('deca-button')[0];
      await userEvent.click(cancelButton);
      
      expect(mockOnCancel).toHaveBeenCalled();
    });

    it('should show loading state on submit button', () => {
      mockFormState.isSubmitting = true;
      
      renderWithMantine(<PrimaryFieldForm {...defaultProps} />);
      
      const submitButton = screen.getAllByTestId('deca-button')[1];
      expect(submitButton).toHaveAttribute('data-loading', 'true');
    });

    it('should disable submit button when loading', () => {
      mockFormState.isSubmitting = true;
      
      renderWithMantine(<PrimaryFieldForm {...defaultProps} />);
      
      const submitButton = screen.getAllByTestId('deca-button')[1];
      expect(submitButton).toBeDisabled();
    });
  });

  describe('Translation', () => {
    it('should call translation function with correct keys', () => {
      renderWithMantine(<PrimaryFieldForm {...defaultProps} />);
      
      expect(mockT).toHaveBeenCalledWith('primaryFieldModal.selectLabel');
      expect(mockT).toHaveBeenCalledWith('primaryFieldModal.selectPlaceholder');
      expect(mockT).toHaveBeenCalledWith('primaryFieldModal.cancelBtn');
      expect(mockT).toHaveBeenCalledWith('primaryFieldModal.submitBtn');
    });

    it('should display translated text', () => {
      mockT.mockReturnValue('Translated Text');
      
      renderWithMantine(<PrimaryFieldForm {...defaultProps} />);
      
      expect(screen.getAllByText('Translated Text')).toHaveLength(3); // label, cancel, submit
    });
  });

  describe('Component Props', () => {
    it('should handle all required props', () => {
      const props = {
        baseId: 'base1',
        tableId: 'table1',
        onSubmit: mockOnSubmit,
        onCancel: mockOnCancel,
      };
      
      renderWithMantine(<PrimaryFieldForm {...props} />);
      
      expect(screen.getByTestId('box')).toBeInTheDocument();
    });

    it('should handle optional props', () => {
      const props = {
        onSubmit: mockOnSubmit,
        onCancel: mockOnCancel,
        defaultValues: { fieldId: 'field1' },
      };
      
      renderWithMantine(<PrimaryFieldForm {...props} />);
      
      expect(screen.getByTestId('box')).toBeInTheDocument();
    });
  });

  describe('Edge Cases', () => {
    it('should handle missing onSubmit prop', () => {
      renderWithMantine(<PrimaryFieldForm onCancel={mockOnCancel} onSubmit={vi.fn()} />);
      
      expect(screen.getByTestId('box')).toBeInTheDocument();
    });

    it('should handle missing onCancel prop', () => {
      renderWithMantine(<PrimaryFieldForm onSubmit={mockOnSubmit} onCancel={vi.fn()} />);
      
      expect(screen.getByTestId('box')).toBeInTheDocument();
    });

    it('should handle undefined defaultValues', () => {
      renderWithMantine(<PrimaryFieldForm {...defaultProps} defaultValues={undefined} />);
      
      expect(screen.getByTestId('box')).toBeInTheDocument();
    });

    it('should handle empty defaultValues', () => {
      renderWithMantine(<PrimaryFieldForm {...defaultProps} defaultValues={{}} />);
      
      expect(screen.getByTestId('box')).toBeInTheDocument();
    });
  });

  describe('Accessibility', () => {
    it('should have proper form structure', () => {
      renderWithMantine(<PrimaryFieldForm {...defaultProps} />);
      
      expect(screen.getByTestId('box')).toBeInTheDocument();
      expect(screen.getByTestId('select-fieldId')).toBeInTheDocument();
    });

    it('should have proper button labels', () => {
      renderWithMantine(<PrimaryFieldForm {...defaultProps} />);
      
      const buttons = screen.getAllByTestId('deca-button');
      expect(buttons[0]).toHaveAttribute('aria-label', 'primaryFieldModal.cancelBtn');
      expect(buttons[1]).toHaveAttribute('aria-label', 'primaryFieldModal.submitBtn');
    });
  });
});
