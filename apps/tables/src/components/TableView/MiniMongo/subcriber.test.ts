import { MemoryDb } from 'minimongo';
import { getSubscribers, notifySubscribers, subscribers, type ChangeEvent, type SubscriberCallback } from './subcriber';

// Mock minimongo
vi.mock('minimongo', () => ({
  MemoryDb: vi.fn().mockImplementation(() => ({})),
}));

describe('subcriber', () => {
  let mockDb: MemoryDb;

  beforeEach(() => {
    vi.clearAllMocks();
    mockDb = new MemoryDb();
  });

  afterEach(() => {
    vi.restoreAllMocks();
  });

  describe('getSubscribers', () => {
    it('should create new subscriber set for new database', () => {
      const result = getSubscribers(mockDb, 'test-collection');
      
      expect(result).toBeInstanceOf(Set);
      expect(subscribers.has(mockDb)).toBe(true);
      expect(subscribers.get(mockDb)?.has('test-collection')).toBe(true);
    });

    it('should create new subscriber set for new collection on existing database', () => {
      // First call creates the database entry
      getSubscribers(mockDb, 'collection1');
      
      // Second call creates new collection entry
      const result = getSubscribers(mockDb, 'collection2');
      
      expect(result).toBeInstanceOf(Set);
      expect(subscribers.get(mockDb)?.has('collection1')).toBe(true);
      expect(subscribers.get(mockDb)?.has('collection2')).toBe(true);
    });

    it('should return existing subscriber set for existing collection', () => {
      const firstCall = getSubscribers(mockDb, 'test-collection');
      const secondCall = getSubscribers(mockDb, 'test-collection');
      
      expect(firstCall).toBe(secondCall);
    });

    it('should handle multiple databases independently', () => {
      const db1 = new MemoryDb();
      const db2 = new MemoryDb();
      
      const subscribers1 = getSubscribers(db1, 'collection');
      const subscribers2 = getSubscribers(db2, 'collection');
      
      expect(subscribers1).not.toBe(subscribers2);
      expect(subscribers.has(db1)).toBe(true);
      expect(subscribers.has(db2)).toBe(true);
    });
  });

  describe('notifySubscribers', () => {
    it('should notify all subscribers for a collection', () => {
      const callback1 = vi.fn();
      const callback2 = vi.fn();
      
      getSubscribers(mockDb, 'test-collection').add(callback1);
      getSubscribers(mockDb, 'test-collection').add(callback2);
      
      const testDocs = [{ id: '1', name: 'Test' }];
      
      notifySubscribers(mockDb, 'test-collection', 'upsert', testDocs);
      
      expect(callback1).toHaveBeenCalledWith({
        type: 'upsert',
        docs: testDocs,
        timestamp: expect.any(Number),
        collectionName: 'test-collection',
      });
      expect(callback2).toHaveBeenCalledWith({
        type: 'upsert',
        docs: testDocs,
        timestamp: expect.any(Number),
        collectionName: 'test-collection',
      });
    });

    it('should handle different event types', () => {
      const callback = vi.fn();
      getSubscribers(mockDb, 'test-collection').add(callback);
      
      const testDocs = [{ id: '1', name: 'Test' }];
      
      // Test upsert event
      notifySubscribers(mockDb, 'test-collection', 'upsert', testDocs);
      expect(callback).toHaveBeenCalledWith(expect.objectContaining({ type: 'upsert' }));
      
      // Test update event
      notifySubscribers(mockDb, 'test-collection', 'update', testDocs);
      expect(callback).toHaveBeenCalledWith(expect.objectContaining({ type: 'update' }));
      
      // Test remove event
      notifySubscribers(mockDb, 'test-collection', 'remove', testDocs);
      expect(callback).toHaveBeenCalledWith(expect.objectContaining({ type: 'remove' }));
    });

    it('should create immutable event objects', () => {
      const callback = vi.fn();
      getSubscribers(mockDb, 'test-collection').add(callback);
      
      const testDocs = [{ id: '1', name: 'Test' }];
      
      notifySubscribers(mockDb, 'test-collection', 'upsert', testDocs);
      
      const event = callback.mock.calls[0][0];
      expect(Object.isFrozen(event)).toBe(true);
      expect(Object.isFrozen(event.docs)).toBe(true);
    });

    it('should handle empty subscriber set gracefully', () => {
      // Don't add any subscribers
      const testDocs = [{ id: '1', name: 'Test' }];
      
      expect(() => {
        notifySubscribers(mockDb, 'nonexistent-collection', 'upsert', testDocs);
      }).not.toThrow();
    });

    it('should handle missing database gracefully', () => {
      const testDocs = [{ id: '1', name: 'Test' }];
      
      expect(() => {
        notifySubscribers(mockDb, 'test-collection', 'upsert', testDocs);
      }).not.toThrow();
    });

    it('should handle subscriber errors gracefully', () => {
      const errorCallback = vi.fn().mockImplementation(() => {
        throw new Error('Subscriber error');
      });
      const normalCallback = vi.fn();
      
      getSubscribers(mockDb, 'test-collection').add(errorCallback);
      getSubscribers(mockDb, 'test-collection').add(normalCallback);
      
      const testDocs = [{ id: '1', name: 'Test' }];
      
      // Should not throw and should call both callbacks
      expect(() => {
        notifySubscribers(mockDb, 'test-collection', 'upsert', testDocs);
      }).not.toThrow();
      
      expect(errorCallback).toHaveBeenCalled();
      expect(normalCallback).toHaveBeenCalled();
    });

    it('should preserve original docs array', () => {
      const callback = vi.fn();
      getSubscribers(mockDb, 'test-collection').add(callback);
      
      const originalDocs = [{ id: '1', name: 'Test' }];
      const docsCopy = [...originalDocs];
      
      notifySubscribers(mockDb, 'test-collection', 'upsert', originalDocs);
      
      // Original array should be unchanged
      expect(originalDocs).toEqual(docsCopy);
      
      // Event should have a frozen copy
      const event = callback.mock.calls[0][0];
      expect(event.docs).not.toBe(originalDocs);
      expect(event.docs).toEqual(originalDocs);
    });

    it('should include correct timestamp', () => {
      const callback = vi.fn();
      getSubscribers(mockDb, 'test-collection').add(callback);
      
      const beforeTime = Date.now();
      notifySubscribers(mockDb, 'test-collection', 'upsert', []);
      const afterTime = Date.now();
      
      const event = callback.mock.calls[0][0];
      expect(event.timestamp).toBeGreaterThanOrEqual(beforeTime);
      expect(event.timestamp).toBeLessThanOrEqual(afterTime);
    });

    it('should include correct collection name', () => {
      const callback = vi.fn();
      getSubscribers(mockDb, 'my-collection').add(callback);
      
      notifySubscribers(mockDb, 'my-collection', 'upsert', []);
      
      const event = callback.mock.calls[0][0];
      expect(event.collectionName).toBe('my-collection');
    });
  });

  describe('subscribers WeakMap', () => {
    it('should allow garbage collection of unused databases', () => {
      const db1 = new MemoryDb();
      const db2 = new MemoryDb();
      
      getSubscribers(db1, 'collection');
      getSubscribers(db2, 'collection');
      
      expect(subscribers.has(db1)).toBe(true);
      expect(subscribers.has(db2)).toBe(true);
      
      // Clear references to allow garbage collection
      // In a real scenario, this would happen when the database objects go out of scope
      // For testing, we can verify the WeakMap behavior
      expect(subscribers.get(db1)).toBeDefined();
      expect(subscribers.get(db2)).toBeDefined();
    });

    it('should maintain separate maps for different databases', () => {
      const db1 = new MemoryDb();
      const db2 = new MemoryDb();
      
      const subscribers1 = getSubscribers(db1, 'collection');
      const subscribers2 = getSubscribers(db2, 'collection');
      
      expect(subscribers1).not.toBe(subscribers2);
      expect(subscribers.get(db1)).not.toBe(subscribers.get(db2));
    });
  });

  describe('type definitions', () => {
    it('should have correct ChangeEvent type structure', () => {
      const event: ChangeEvent<{ id: string; name: string }> = {
        type: 'upsert',
        docs: [{ id: '1', name: 'Test' }],
        timestamp: Date.now(),
        collectionName: 'test-collection',
      };
      
      expect(event.type).toBe('upsert');
      expect(event.docs).toEqual([{ id: '1', name: 'Test' }]);
      expect(typeof event.timestamp).toBe('number');
      expect(event.collectionName).toBe('test-collection');
    });

    it('should have correct SubscriberCallback type', () => {
      const callback: SubscriberCallback<{ id: string; name: string }> = (event) => {
        expect(event.type).toMatch(/^(upsert|update|remove)$/);
        expect(Array.isArray(event.docs)).toBe(true);
        expect(typeof event.timestamp).toBe('number');
        expect(typeof event.collectionName).toBe('string');
      };
      
      const event: ChangeEvent<{ id: string; name: string }> = {
        type: 'upsert',
        docs: [{ id: '1', name: 'Test' }],
        timestamp: Date.now(),
        collectionName: 'test-collection',
      };
      
      callback(event);
    });
  });
});
