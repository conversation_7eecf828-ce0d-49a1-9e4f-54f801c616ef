import { renderHook, act } from '@testing-library/react';
import { useMinimongo } from './useMinimongo';
import { vi } from 'vitest';

// Mock minimongo
const mockMemoryDb = vi.hoisted(() => ({
  collections: {},
  addCollection: vi.fn((_name, callback) => {
    setTimeout(() => callback(), 0);
  }),
  removeCollection: vi.fn((_name, success, _error) => {
    setTimeout(() => success(), 0);
  }),
}));

vi.mock('minimongo', () => ({
  MemoryDb: vi.fn().mockImplementation(() => mockMemoryDb),
}));

// Mock useEntities hook
const mockUseEntities = vi.hoisted(() => vi.fn());
vi.mock('@/hooks', () => ({
  useEntities: mockUseEntities,
}));

// Mock lodash
vi.mock('lodash/isEmpty', () => ({
  default: vi.fn((obj) => Object.keys(obj).length === 0),
}));

describe('useMinimongo', () => {
  let mockCollection: any;

  beforeEach(() => {
    vi.clearAllMocks();
    
    // Create mock collection
    mockCollection = {
      find: vi.fn().mockReturnValue({
        fetch: vi.fn((success, _error) => {
          setTimeout(() => success([]), 0);
        }),
      }),
      findOne: vi.fn().mockResolvedValue(null),
      upsert: vi.fn().mockResolvedValue(undefined),
      remove: vi.fn().mockResolvedValue(undefined),
    };

    // Setup mock database instance
    mockMemoryDb.collections = { tables: mockCollection };
    
    // Setup default useEntities mock
    mockUseEntities.mockReturnValue({
      ids: [],
      entities: {},
      updateEntity: vi.fn(),
      setAll: vi.fn(),
    });
  });

  afterEach(() => {
    vi.restoreAllMocks();
  });

  describe('initialization', () => {
    it('should initialize with default values', () => {
      const { result } = renderHook(() => useMinimongo({}));

      expect(result.current.data).toEqual([]);
      expect(result.current.totalDataCount).toBe(0);
      expect(result.current.isLoading).toBe(true);
      expect(result.current.isFetching).toBe(false);
      expect(result.current.filter).toEqual({
        selectors: {},
        sort: { sortId: -1 },
      });
      expect(result.current.page).toBe(1);
    });

    it('should initialize with custom collection name', () => {
      const { result } = renderHook(() => useMinimongo({ collectionName: 'custom' }));

      expect(result.current.data).toEqual([]);
    });
  });

  describe('fetchData function', () => {
    it('should fetch data successfully', async () => {
      const mockData = [{ id: '1', name: 'Test' }];
      const mockSetAll = vi.fn();
      
      // Mock the useEntities hook
      mockUseEntities.mockReturnValue({
        ids: [],
        entities: {},
        updateEntity: vi.fn(),
        setAll: mockSetAll,
      });
      
      mockCollection.find().fetch.mockImplementation((success) => {
        setTimeout(() => success(mockData), 0);
      });

      const { result } = renderHook(() => useMinimongo<{ id: string; name: string }>({}));

      await act(async () => {
        await result.current.fetchData();
      });

      expect(mockSetAll).toHaveBeenCalledWith(mockData);
    });

    it('should handle fetch errors gracefully', async () => {
      // Test error handling by making the collection undefined
      mockMemoryDb.collections = {};
      
      const { result } = renderHook(() => useMinimongo<{ id: string; name: string }>({}));

      await act(async () => {
        await result.current.fetchData();
      });

      // Should handle gracefully without throwing
      expect(result.current.data).toEqual([]);
    });

    it('should return empty data when collection does not exist', async () => {
      mockMemoryDb.collections = {};
      
      const { result } = renderHook(() => useMinimongo<{ id: string; name: string }>({ collectionName: 'nonexistent' }));

      await act(async () => {
        await result.current.fetchData();
      });

      expect(result.current.data).toEqual([]);
    });
  });

  describe('initCollection', () => {
    it('should add collection when it does not exist', async () => {
      mockMemoryDb.collections = {};
      
      const { result } = renderHook(() => useMinimongo({}));

      await act(async () => {
        await result.current.initCollection();
      });

      expect(mockMemoryDb.addCollection).toHaveBeenCalledWith('tables', expect.any(Function));
    });

    it('should remove existing collection before adding new one', async () => {
      mockMemoryDb.collections = { tables: mockCollection };
      
      const { result } = renderHook(() => useMinimongo({}));

      await act(async () => {
        await result.current.initCollection();
      });

      expect(mockMemoryDb.removeCollection).toHaveBeenCalledWith('tables', expect.any(Function), expect.any(Function));
      expect(mockMemoryDb.addCollection).toHaveBeenCalledWith('tables', expect.any(Function));
    });

    it('should handle remove collection error', async () => {
      mockMemoryDb.collections = { tables: mockCollection };
      const mockRemoveCollection = vi.fn((_name, _success, error) => {
        setTimeout(() => error(), 0);
      });
      mockMemoryDb.removeCollection = mockRemoveCollection;

      const { result } = renderHook(() => useMinimongo({}));

      await act(async () => {
        await expect(result.current.initCollection()).rejects.toThrow();
      });
    });
  });

  describe('state updates', () => {
    it('should update page and filter state correctly', () => {
      const { result } = renderHook(() => useMinimongo({}));

      // Test page update with value and function
      act(() => {
        result.current.updatePageIndex(2);
      });
      expect(result.current.page).toBe(2);
      expect(result.current.isFetching).toBe(true);

      // Test page update with function
      act(() => {
        result.current.updatePageIndex(prev => prev + 1);
      });
      expect(result.current.page).toBe(3);

      // Test filter update with value and function
      act(() => {
        result.current.updateFilter({ selectors: { name: 'test' }, sort: { name: 1 } });
      });
      expect(result.current.filter).toEqual({ selectors: { name: 'test' }, sort: { name: 1 } });
      expect(result.current.isFetching).toBe(true);

      // Test filter update with function
      act(() => {
        result.current.updateFilter(prev => ({ ...prev, selectors: { name: 'updated' } }));
      });
      expect(result.current.filter.selectors).toEqual({ name: 'updated' });
    });
  });

  describe('findOne', () => {
    it('should find one document by id', async () => {
      const mockDoc = { id: '1', name: 'Test' };
      mockCollection.findOne.mockResolvedValue(mockDoc);

      const { result } = renderHook(() => useMinimongo<{ id: string; name: string }>({}));

      await act(async () => {
        const doc = await result.current.findOne('1');
        expect(doc).toEqual(mockDoc);
      });
    });

  });

  describe('find', () => {
    it('should find multiple documents by ids', async () => {
      const mockDocs = [{ id: '1', name: 'Test1' }, { id: '2', name: 'Test2' }];
      mockCollection.find().fetch.mockImplementation((success) => {
        setTimeout(() => success(mockDocs), 0);
      });

      const { result } = renderHook(() => useMinimongo<{ id: string; name: string }>({}));

      await act(async () => {
        const docs = await result.current.find(['1', '2']);
        expect(docs).toEqual(mockDocs);
      });
    });

    it('should return empty array when no ids provided', async () => {
      const { result } = renderHook(() => useMinimongo<{ id: string; name: string }>({}));

      await act(async () => {
        const docs = await result.current.find([]);
        expect(docs).toEqual([]);
      });
    });

  });

  describe('subscribe', () => {
    it('should subscribe to collection changes', () => {
      const { result } = renderHook(() => useMinimongo({}));
      const callback = vi.fn();

      act(() => {
        const unsubscribe = result.current.subscribe(callback);
        expect(typeof unsubscribe).toBe('function');
        unsubscribe();
      });
    });

    it('should return empty function when no collection name', () => {
      const { result } = renderHook(() => useMinimongo({ collectionName: '' }));
      const callback = vi.fn();

      act(() => {
        const unsubscribe = result.current.subscribe(callback);
        expect(typeof unsubscribe).toBe('function');
      });
    });
  });

  describe('upsert', () => {
    it('should upsert documents and notify subscribers', async () => {
      const mockData = [{ id: '1', name: 'Test' }];
      const { result } = renderHook(() => useMinimongo<{ id: string; name: string }>({}));

      await act(async () => {
        await result.current.upsert(mockData);
      });

      expect(mockCollection.upsert).toHaveBeenCalledWith({ id: '1', name: 'Test', _id: '1' });
    });

    it('should handle upsert when collection does not exist', async () => {
      mockMemoryDb.collections = {};
      
      const { result } = renderHook(() => useMinimongo<{ id: string; name: string }>({ collectionName: 'nonexistent' }));

      await act(async () => {
        await result.current.upsert([{ id: '1', name: 'Test' }]);
      });

      expect(mockCollection.upsert).not.toHaveBeenCalled();
    });
  });

  describe('updateOne', () => {
    it('should update one document', async () => {
      const mockDoc = { id: '1', name: 'Updated' };
      mockCollection.findOne.mockResolvedValue(mockDoc);
      mockCollection.upsert.mockResolvedValue(undefined);

      const { result } = renderHook(() => useMinimongo<{ id: string; name: string }>({}));

      await act(async () => {
        const doc = await result.current.updateOne('1', { name: 'Updated' });
        expect(doc).toEqual(mockDoc);
      });
    });

    it('should handle update when collection does not exist', async () => {
      mockMemoryDb.collections = {};
      
      const { result } = renderHook(() => useMinimongo<{ id: string; name: string }>({ collectionName: 'nonexistent' }));

      await act(async () => {
        const doc = await result.current.updateOne('1', { name: 'Updated' });
        expect(doc).toBeUndefined();
      });
    });
  });

  describe('removeOne', () => {
    it('should remove one document', async () => {
      const mockDoc = { id: '1', name: 'Test' };
      mockCollection.findOne.mockResolvedValue(mockDoc);
      mockCollection.remove.mockResolvedValue(undefined);

      const { result } = renderHook(() => useMinimongo<{ id: string; name: string }>({}));

      await act(async () => {
        await result.current.removeOne('1');
      });

      expect(mockCollection.remove).toHaveBeenCalledWith({ _id: '1' });
    });

    it('should handle remove when collection does not exist', async () => {
      mockMemoryDb.collections = {};
      
      const { result } = renderHook(() => useMinimongo<{ id: string; name: string }>({ collectionName: 'nonexistent' }));

      await act(async () => {
        await result.current.removeOne('1');
      });

      expect(mockCollection.remove).not.toHaveBeenCalled();
    });

    it('should handle remove when document does not exist', async () => {
      mockCollection.findOne.mockResolvedValue(null);

      const { result } = renderHook(() => useMinimongo<{ id: string; name: string }>({}));

      await act(async () => {
        await result.current.removeOne('1');
      });

      expect(mockCollection.remove).not.toHaveBeenCalled();
    });
  });

  describe('removeMany', () => {
    it('should remove multiple documents', async () => {
      const mockDocs = [{ id: '1', name: 'Test1' }, { id: '2', name: 'Test2' }];
      mockCollection.find().fetch.mockImplementation((success) => {
        setTimeout(() => success(mockDocs), 0);
      });

      const { result } = renderHook(() => useMinimongo<{ id: string; name: string }>({}));

      await act(async () => {
        await result.current.removeMany(['1', '2']);
      });

      expect(mockCollection.remove).toHaveBeenCalledTimes(2);
    });

    it('should handle removeMany when collection does not exist', async () => {
      mockMemoryDb.collections = {};
      
      const { result } = renderHook(() => useMinimongo<{ id: string; name: string }>({ collectionName: 'nonexistent' }));

      await act(async () => {
        await result.current.removeMany(['1', '2']);
      });

      expect(mockCollection.remove).not.toHaveBeenCalled();
    });
  });

  describe('findByField', () => {
    it('should find documents by field value', async () => {
      const mockDocs = [{ id: '1', name: 'Test' }];
      mockCollection.find().fetch.mockImplementation((success) => {
        setTimeout(() => success(mockDocs), 0);
      });

      const { result } = renderHook(() => useMinimongo<{ id: string; name: string }>({}));

      await act(async () => {
        const docs = await result.current.findByField('name' as keyof { id: string; name: string }, 'Test');
        expect(docs).toEqual(mockDocs);
      });
    });

    it('should handle array values with $in operator', async () => {
      const mockDocs = [{ id: '1', name: 'Test' }];
      mockCollection.find().fetch.mockImplementation((success) => {
        setTimeout(() => success(mockDocs), 0);
      });

      const { result } = renderHook(() => useMinimongo<{ id: string; name: string }>({}));

      await act(async () => {
        const docs = await result.current.findByField('name' as keyof { id: string; name: string }, ['Test1', 'Test2']);
        expect(docs).toEqual(mockDocs);
      });
    });

    it('should handle idPattern with regex', async () => {
      const mockDocs = [{ id: 'test-1', name: 'Test' }];
      mockCollection.find().fetch.mockImplementation((success) => {
        setTimeout(() => success(mockDocs), 0);
      });

      const { result } = renderHook(() => useMinimongo<{ id: string; name: string }>({}));

      await act(async () => {
        const docs = await result.current.findByField('name' as keyof { id: string; name: string }, 'Test', 'test-');
        expect(docs).toEqual(mockDocs);
      });
    });

    it('should return empty array when collection does not exist', async () => {
      mockMemoryDb.collections = {};
      
      const { result } = renderHook(() => useMinimongo<{ id: string; name: string }>({ collectionName: 'nonexistent' }));

      await act(async () => {
        const docs = await result.current.findByField('name' as keyof { id: string; name: string }, 'Test');
        expect(docs).toEqual([]);
      });
    });

    it('should return empty array when field or value is missing', async () => {
      const { result } = renderHook(() => useMinimongo<{ id: string; name: string }>({}));

      await act(async () => {
        const docs = await result.current.findByField('' as keyof { id: string; name: string }, 'Test');
        expect(docs).toEqual([]);
      });

      await act(async () => {
        const docs = await result.current.findByField('name' as keyof { id: string; name: string }, '');
        expect(docs).toEqual([]);
      });
    });
  });

  describe('findAll', () => {
    it('should find all documents with current filter', async () => {
      const mockDocs = [{ id: '1', name: 'Test' }];
      mockCollection.find().fetch.mockImplementation((success) => {
        setTimeout(() => success(mockDocs), 0);
      });

      const { result } = renderHook(() => useMinimongo<{ id: string; name: string }>({}));

      await act(async () => {
        const docs = await result.current.findAll();
        expect(docs).toEqual(mockDocs);
      });
    });

    it('should find all documents ignoring filter when option is set', async () => {
      const mockDocs = [{ id: '1', name: 'Test' }];
      mockCollection.find().fetch.mockImplementation((success) => {
        setTimeout(() => success(mockDocs), 0);
      });

      const { result } = renderHook(() => useMinimongo<{ id: string; name: string }>({}));

      await act(async () => {
        const docs = await result.current.findAll({ ignoreFilter: true });
        expect(docs).toEqual(mockDocs);
      });
    });

    it('should handle findAll errors gracefully', async () => {
      // Test error handling by making the collection undefined
      mockMemoryDb.collections = {};
      
      const { result } = renderHook(() => useMinimongo<{ id: string; name: string }>({}));

      await act(async () => {
        const docs = await result.current.findAll();
        expect(docs).toEqual([]);
      });
    });

    it('should return empty array when collection does not exist', async () => {
      mockMemoryDb.collections = {};
      
      const { result } = renderHook(() => useMinimongo<{ id: string; name: string }>({ collectionName: 'nonexistent' }));

      await act(async () => {
        const docs = await result.current.findAll();
        expect(docs).toEqual([]);
      });
    });
  });

  describe('searchData', () => {
    it('should search data with text and update data by default', async () => {
      const mockData = [{ id: '1', name: 'Test' }];
      mockCollection.find().fetch.mockImplementation((success) => {
        setTimeout(() => success(mockData), 0);
      });

      const { result } = renderHook(() => useMinimongo<{ id: string; name: string }>({}));

      await act(async () => {
        const matchedFields = await result.current.searchData('test', ['name']);
        expect(matchedFields).toEqual({ '1': { name: true } });
      });
    });

    it('should search data without updating when updateData is false', async () => {
      const mockData = [{ id: '1', name: 'Test' }];
      mockCollection.find().fetch.mockImplementation((success) => {
        setTimeout(() => success(mockData), 0);
      });

      const { result } = renderHook(() => useMinimongo<{ id: string; name: string }>({}));

      await act(async () => {
        const matchedFields = await result.current.searchData('test', ['name'], { updateData: false });
        expect(matchedFields).toEqual({ '1': { name: true } });
      });
    });

    it('should return empty object when collection does not exist', async () => {
      mockMemoryDb.collections = {};
      
      const { result } = renderHook(() => useMinimongo<{ id: string; name: string }>({ collectionName: 'nonexistent' }));

      await act(async () => {
        const matchedFields = await result.current.searchData('test', ['name']);
        expect(matchedFields).toEqual({});
      });
    });

    it('should return empty object when no searchable fields', async () => {
      const { result } = renderHook(() => useMinimongo<{ id: string; name: string }>({}));

      await act(async () => {
        const matchedFields = await result.current.searchData('test', []);
        expect(matchedFields).toEqual({});
      });
    });
  });

  describe('edge cases and error scenarios', () => {
    it('should handle various edge cases gracefully', async () => {
      const { result } = renderHook(() => useMinimongo<{ id: string; name: string }>({}));

      // Test empty search text scenarios
      await act(async () => {
        const result1 = await result.current.searchData('', ['name']);
        expect(result1).toEqual({});
        
        const result2 = await result.current.searchData(null as any, ['name']);
        expect(result2).toEqual({});
      });

      // Test missing collections for various operations
      mockMemoryDb.collections = {};
      
      await act(async () => {
        // All these should handle missing collections gracefully
        expect(await result.current.findOne('1')).toBeNull();
        expect(await result.current.find(['1', '2'])).toEqual([]);
        expect(await result.current.findAll()).toEqual([]);
        expect(await result.current.findByField('name', 'test')).toEqual([]);
        expect(await result.current.searchData('test', ['name'])).toEqual({});
        
        // Operations that should not throw
        await result.current.upsert([{ id: '1', name: 'Test' }]);
        await result.current.updateOne('1', { name: 'Updated' });
        await result.current.removeOne('1');
        await result.current.removeMany(['1', '2']);
      });
    });
  });

  describe('cleanup', () => {
    it('should cleanup subscribers on unmount', () => {
      const { result, unmount } = renderHook(() => useMinimongo<{ id: string; name: string }>({}));

      act(() => {
        result.current.subscribe(vi.fn());
      });

      unmount();
      // Cleanup is tested indirectly through the useEffect cleanup
    });
  });

  describe('error handling in database operations', () => {
    it('should handle find operation errors', async () => {
      const { result } = renderHook(() => useMinimongo<{ id: string; name: string }>({}));

      // Wait for initial setup to complete
      await act(async () => {
        await new Promise(resolve => setTimeout(resolve, 0));
      });

      // Now mock collection.find to simulate an error specifically for find operation
      mockCollection.find.mockReturnValue({
        fetch: vi.fn((_success, error) => {
          error(new Error('Find operation failed'));
        }),
      });

      await act(async () => {
        await expect(result.current.find(['1', '2'])).rejects.toThrow('Find operation failed');
      });
    });

    it('should handle findAll operation errors', async () => {
      const { result } = renderHook(() => useMinimongo<{ id: string; name: string }>({}));

      // Wait for initial setup to complete
      await act(async () => {
        await new Promise(resolve => setTimeout(resolve, 0));
      });

      // Now mock collection.find to simulate an error specifically for findAll operation
      mockCollection.find.mockReturnValue({
        fetch: vi.fn((_success, error) => {
          error(new Error('FindAll operation failed'));
        }),
      });

      await act(async () => {
        await expect(result.current.findAll()).rejects.toThrow('FindAll operation failed');
      });
    });

    it('should handle fetch operation errors gracefully', async () => {
      const { result } = renderHook(() => useMinimongo<{ id: string; name: string }>({}));

      // Wait for initial setup to complete
      await act(async () => {
        await new Promise(resolve => setTimeout(resolve, 0));
      });

      // Mock collection.find to simulate an error for direct fetchData call
      mockCollection.find.mockReturnValue({
        fetch: vi.fn((_success, error) => {
          error(new Error('Database fetch error'));
        }),
      });

      // Test the internal fetch function that uses Promise.all directly
      await act(async () => {
        await expect(result.current.fetchData()).rejects.toThrow('Database fetch error');
      });
    });
  });
});