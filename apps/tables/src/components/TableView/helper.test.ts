import { Field, FieldType, ViewRowHeight, ViewSortOrder } from '@/types';
import {
  convertViewSortOrderToSort,
  convertSortToViewSortOrder,
  removeUnsortableFields,
  removeUnfilterableFields,
  getOrderedColumns,
  getVisibleColumns,
  getViewPayload,
  getFieldTypeFromMRTHeader,
  getSortTranslationKey,
  getFieldFromViewColumnField,
  normalizeOptionValues,
} from './helper';
import { Sort as DecaTableSort, SortOrder as DecaTableSortOrder, View as DecaTableView, ViewColumnFields as DecaTableViewColumnFields } from '@resola-ai/ui/components';
import { describe, it, expect, vi } from 'vitest';

// Mock the utility functions
vi.mock('@/utils', () => ({
  calculateColumnVisibility: vi.fn((tableFieldIds, visibleFieldIds) => {
    const visibility: Record<string, number> = {};
    tableFieldIds.forEach((id: string) => {
      visibility[id] = visibleFieldIds.includes(id) ? 1 : 0;
    });
    return visibility;
  }),
  formatMongoDBQuery: vi.fn((query) => query),
  parseMongoDBQuery: vi.fn((query) => query),
  sanitizeRuleGroup: vi.fn((query) => query),
}));

describe('TableView helper functions', () => {
  describe('convertViewSortOrderToSort', () => {
    it('should convert ViewSortOrder to DecaTableSort correctly', () => {
      const viewSortOrder = {
        'field1': ViewSortOrder.ASC,
        'field2': ViewSortOrder.DESC,
      };

      const result = convertViewSortOrderToSort(viewSortOrder);

      expect(result).toEqual([
        { fieldId: 'field1', order: DecaTableSortOrder.Ascending },
        { fieldId: 'field2', order: DecaTableSortOrder.Descending },
      ]);
    });

    it('should handle empty object', () => {
      const result = convertViewSortOrderToSort({});
      expect(result).toEqual([]);
    });
  });

  describe('convertSortToViewSortOrder', () => {
    it('should convert DecaTableSort to ViewSortOrder correctly', () => {
      const sortArray: DecaTableSort[] = [
        { fieldId: 'field1', order: DecaTableSortOrder.Ascending },
        { fieldId: 'field2', order: DecaTableSortOrder.Descending },
      ];

      const result = convertSortToViewSortOrder(sortArray);

      expect(result).toEqual({
        field1: ViewSortOrder.ASC,
        field2: ViewSortOrder.DESC,
      });
    });

    it('should handle empty array', () => {
      const result = convertSortToViewSortOrder([]);
      expect(result).toEqual({});
    });
  });

  describe('removeUnsortableFields', () => {
    const fields: Field[] = [
      { id: 'field1', name: 'Text Field', type: FieldType.TEXT, description: '', system: false, required: false, isPrimary: false },
      { id: 'field2', name: 'Number Field', type: FieldType.NUMBER, description: '', system: false, required: false, isPrimary: false, options: { numberFormat: 'decimal' as any, decimalPlaces: 2 } },
      { id: 'field3', name: 'Image Field', type: FieldType.IMAGE as any, description: '', system: false, required: false, isPrimary: false },
    ];
    const unsortableFieldTypes = [FieldType.IMAGE] as any;

    it('should remove unsortable fields from sort', () => {
      const sort = {
        'field1': ViewSortOrder.ASC,
        'field2': ViewSortOrder.DESC,
        'field3': ViewSortOrder.ASC,
      };

      const result = removeUnsortableFields(sort, fields, unsortableFieldTypes);

      expect(result).toEqual({
        'field1': ViewSortOrder.ASC,
        'field2': ViewSortOrder.DESC,
      });
    });

    it('should return empty object when sort is null', () => {
      const result = removeUnsortableFields(null as any, fields, unsortableFieldTypes);
      expect(result).toEqual({});
    });

    it('should return empty object when sort is empty', () => {
      const result = removeUnsortableFields({}, fields, unsortableFieldTypes);
      expect(result).toEqual({});
    });

    it('should handle field not found', () => {
      const sort = {
        'nonexistent': ViewSortOrder.ASC,
        'field1': ViewSortOrder.ASC,
      };

      const result = removeUnsortableFields(sort, fields, unsortableFieldTypes);

      expect(result).toEqual({
        'field1': ViewSortOrder.ASC,
      });
    });
  });

  describe('removeUnfilterableFields', () => {
    const fields: Field[] = [
      { id: 'field1', name: 'Text Field', type: FieldType.TEXT, description: '', system: false, required: false, isPrimary: false },
      { id: 'field2', name: 'Number Field', type: FieldType.NUMBER, description: '', system: false, required: false, isPrimary: false, options: { numberFormat: 'decimal' as any, decimalPlaces: 2 } },
    ];
    const unfilterableFieldTypes = [FieldType.IMAGE];

    it('should remove unfilterable fields from filters', () => {
      const filters = { field1: 'test', field2: 'image' };

      const result = removeUnfilterableFields(filters, fields, unfilterableFieldTypes);

      // The mocked sanitizeRuleGroup returns the original query, so we expect the original filters
      expect(result).toEqual({ field1: 'test', field2: 'image' });
    });

    it('should return empty object when filters is null', () => {
      const result = removeUnfilterableFields(null as any, fields, unfilterableFieldTypes);
      expect(result).toEqual({});
    });

    it('should return empty object when filters is empty', () => {
      const result = removeUnfilterableFields({}, fields, unfilterableFieldTypes);
      expect(result).toEqual({});
    });
  });

  describe('getOrderedColumns', () => {
    const columns: DecaTableViewColumnFields[] = [
      { id: 'col1', header: 'Column 1', fieldMetaId: 'field1', isVisible: true },
      { id: 'col2', header: 'Column 2', fieldMetaId: 'field2', isVisible: true },
      { id: 'col3', header: 'Column 3', fieldMetaId: 'field3', isVisible: true },
    ];

    it('should order columns according to sortColumnIds', () => {
      const sortColumnIds = ['col3', 'col1', 'col2'];

      const result = getOrderedColumns(columns, sortColumnIds);

      expect(result).toEqual([
        { id: 'col3', header: 'Column 3', fieldMetaId: 'field3', isVisible: true },
        { id: 'col1', header: 'Column 1', fieldMetaId: 'field1', isVisible: true },
        { id: 'col2', header: 'Column 2', fieldMetaId: 'field2', isVisible: true },
      ]);
    });

    it('should return original columns when sortColumnIds is null', () => {
      const result = getOrderedColumns(columns, null as any);
      expect(result).toEqual(columns);
    });

    it('should handle columns not in sortColumnIds', () => {
      const sortColumnIds = ['col2'];
      const result = getOrderedColumns(columns, sortColumnIds);

      expect(result[0]).toEqual({ id: 'col2', header: 'Column 2', fieldMetaId: 'field2', isVisible: true });
      expect(result).toHaveLength(3);
    });
  });

  describe('getVisibleColumns', () => {
    const columns: DecaTableViewColumnFields[] = [
      { id: 'col1', header: 'Column 1', fieldMetaId: 'field1', isVisible: true },
      { id: 'col2', header: 'Column 2', fieldMetaId: 'field2', isVisible: true },
      { id: 'col3', header: 'Column 3', fieldMetaId: 'field3', isVisible: true },
    ];

    it('should set isVisible based on project values', () => {
      const project = { col1: 1, col2: 0, col3: 1 };

      const result = getVisibleColumns(columns, project);

      expect(result).toEqual([
        { id: 'col1', header: 'Column 1', fieldMetaId: 'field1', isVisible: true },
        { id: 'col2', header: 'Column 2', fieldMetaId: 'field2', isVisible: false },
        { id: 'col3', header: 'Column 3', fieldMetaId: 'field3', isVisible: true },
      ]);
    });

    it('should return original columns when project is null', () => {
      const result = getVisibleColumns(columns, null as any);
      expect(result).toEqual(columns);
    });
  });

  describe('getViewPayload', () => {
    const currentTableView: DecaTableView = {
      id: 'view1',
      name: 'Test View',
      description: 'Test Description',
      type: 'grid' as any,
      icon: 'table',
      createdBy: new Date(),
      permission: 'read' as any,
      fields: [
        { id: 'field1', header: 'Field 1', fieldMetaId: 'field1', isVisible: true },
        { id: 'field2', header: 'Field 2', fieldMetaId: 'field2', isVisible: false },
      ],
      filters: { field1: 'test' },
      sort: [{ fieldId: 'field1', order: DecaTableSortOrder.Ascending }],
      rowHeight: 'sm',
      autoSort: true,
    };
    const tableFieldIds = ['field1', 'field2', 'field3'];
    const columnSizing = { field1: 200, field2: 150 };

    it('should create complete payload with all properties', () => {
      const result = getViewPayload(currentTableView, tableFieldIds, columnSizing);

      expect(result.name).toBe('Test View');
      expect(result.description).toBe('Test Description');
      expect(result.project).toEqual({ field1: 1, field2: 0, field3: 0 });
      expect(result.filter).toEqual({ field1: 'test' });
      expect(result.sort).toEqual({ field1: ViewSortOrder.ASC });
      expect(result.options?.rowHeight).toBe(ViewRowHeight.SHORT);
      expect(result.options?.columnSizes).toEqual(columnSizing);
      expect(result.options?.automaticSorting).toBe(true);
    });

    it('should handle minimal payload', () => {
      const minimalView: DecaTableView = {
        id: 'view1',
        name: 'Minimal View',
        type: 'grid' as any,
        icon: 'table',
        createdBy: new Date(),
        fields: [],
        autoSort: false,
        rowHeight: 'sm',
        permission: 'read' as any,
      };

      const result = getViewPayload(minimalView, [], {});

      expect(result.name).toBe('Minimal View');
      expect(result.options?.automaticSorting).toBe(false);
    });

    it('should handle medium row height', () => {
      const viewWithMediumHeight = { ...currentTableView, rowHeight: 'md' as any };
      const result = getViewPayload(viewWithMediumHeight, tableFieldIds, columnSizing);

      expect(result.options?.rowHeight).toBe(ViewRowHeight.MEDIUM);
    });

    it('should handle empty columnSizing', () => {
      const result = getViewPayload(currentTableView, tableFieldIds, {});
      expect(result.options?.columnSizes).toBeUndefined();
    });
  });

  describe('getFieldTypeFromMRTHeader', () => {
    it('should return field type from header', () => {
      const header = {
        column: {
          columnDef: {
            type: FieldType.EMAIL,
          },
        },
      } as any;

      const result = getFieldTypeFromMRTHeader(header);

      expect(result).toBe(FieldType.EMAIL);
    });

    it('should return default TEXT type when header is undefined', () => {
      const result = getFieldTypeFromMRTHeader(undefined);
      expect(result).toBe(FieldType.TEXT);
    });

    it('should return default TEXT type when type is not found', () => {
      const header = {
        column: {
          columnDef: {},
        },
      } as any;

      const result = getFieldTypeFromMRTHeader(header);

      expect(result).toBe(FieldType.TEXT);
    });
  });

  describe('getSortTranslationKey', () => {
    it('should return correct keys for CHECKBOX type', () => {
      const result = getSortTranslationKey(FieldType.CHECKBOX);
      expect(result).toEqual({
        asc: 'sort.sortAscCheckbox',
        desc: 'sort.sortDescCheckbox',
      });
    });

    it('should return correct keys for DATETIME type', () => {
      const result = getSortTranslationKey(FieldType.DATETIME);
      expect(result).toEqual({
        asc: 'sort.sortAscDate',
        desc: 'sort.sortDescDate',
      });
    });

    it('should return correct keys for CREATEDAT type', () => {
      const result = getSortTranslationKey(FieldType.CREATEDAT);
      expect(result).toEqual({
        asc: 'sort.sortAscDate',
        desc: 'sort.sortDescDate',
      });
    });

    it('should return correct keys for UPDATEDAT type', () => {
      const result = getSortTranslationKey(FieldType.UPDATEDAT);
      expect(result).toEqual({
        asc: 'sort.sortAscDate',
        desc: 'sort.sortDescDate',
      });
    });

    it('should return correct keys for SELECT type', () => {
      const result = getSortTranslationKey(FieldType.SELECT);
      expect(result).toEqual({
        asc: 'sort.sortAscSelect',
        desc: 'sort.sortDescSelect',
      });
    });

    it('should return correct keys for MULTISELECT type', () => {
      const result = getSortTranslationKey(FieldType.MULTISELECT);
      expect(result).toEqual({
        asc: 'sort.sortAscSelect',
        desc: 'sort.sortDescSelect',
      });
    });

    it('should return correct keys for NUMBER type', () => {
      const result = getSortTranslationKey(FieldType.NUMBER);
      expect(result).toEqual({
        asc: 'sort.sortAscNumber',
        desc: 'sort.sortDescNumber',
      });
    });

    it('should return correct keys for PHONE type', () => {
      const result = getSortTranslationKey(FieldType.PHONE);
      expect(result).toEqual({
        asc: 'sort.sortAscNumber',
        desc: 'sort.sortDescNumber',
      });
    });

    it('should return correct keys for AUTOID type', () => {
      const result = getSortTranslationKey(FieldType.AUTOID);
      expect(result).toEqual({
        asc: 'sort.sortAscNumber',
        desc: 'sort.sortDescNumber',
      });
    });

    it('should return correct keys for CURRENCY type', () => {
      const result = getSortTranslationKey(FieldType.CURRENCY);
      expect(result).toEqual({
        asc: 'sort.sortAscNumber',
        desc: 'sort.sortDescNumber',
      });
    });

    it('should return correct keys for PERCENTAGE type', () => {
      const result = getSortTranslationKey(FieldType.PERCENTAGE);
      expect(result).toEqual({
        asc: 'sort.sortAscNumber',
        desc: 'sort.sortDescNumber',
      });
    });

    it('should return correct keys for TEXT type', () => {
      const result = getSortTranslationKey(FieldType.TEXT);
      expect(result).toEqual({
        asc: 'sort.sortAscText',
        desc: 'sort.sortDescText',
      });
    });

    it('should return correct keys for LONGTEXT type', () => {
      const result = getSortTranslationKey(FieldType.LONGTEXT);
      expect(result).toEqual({
        asc: 'sort.sortAscText',
        desc: 'sort.sortDescText',
      });
    });

    it('should return correct keys for EMAIL type', () => {
      const result = getSortTranslationKey(FieldType.EMAIL);
      expect(result).toEqual({
        asc: 'sort.sortAscText',
        desc: 'sort.sortDescText',
      });
    });

    it('should return correct keys for URL type', () => {
      const result = getSortTranslationKey(FieldType.URL);
      expect(result).toEqual({
        asc: 'sort.sortAscText',
        desc: 'sort.sortDescText',
      });
    });

    it('should return correct keys for CREATEDBY type', () => {
      const result = getSortTranslationKey(FieldType.CREATEDBY);
      expect(result).toEqual({
        asc: 'sort.sortAscText',
        desc: 'sort.sortDescText',
      });
    });

    it('should return correct keys for UPDATEDBY type', () => {
      const result = getSortTranslationKey(FieldType.UPDATEDBY);
      expect(result).toEqual({
        asc: 'sort.sortAscText',
        desc: 'sort.sortDescText',
      });
    });

    it('should return default keys for unknown type', () => {
      const result = getSortTranslationKey('UNKNOWN_TYPE' as FieldType);
      expect(result).toEqual({
        asc: 'sort.sortAsc',
        desc: 'sort.sortDesc',
      });
    });
  });

  describe('getFieldFromViewColumnField', () => {
    it('should create field payload with all properties', () => {
      const viewColumnField: DecaTableViewColumnFields = {
        id: 'field1',
        header: 'Field Name',
        fieldMetaId: 'field1',
        isVisible: true,
        type: FieldType.TEXT,
        options: { choices: [], default: '', isPrimary: false },
      };

      const result = getFieldFromViewColumnField(viewColumnField);

      expect(result).toEqual({
        type: FieldType.TEXT,
        name: 'Field Name',
        options: { choices: [], default: '', isPrimary: false },
      });
    });

    it('should create field payload with minimal properties', () => {
      const viewColumnField: DecaTableViewColumnFields = {
        id: 'field1',
        header: 'Field Name',
        fieldMetaId: 'field1',
        isVisible: true,
      };

      const result = getFieldFromViewColumnField(viewColumnField);

      expect(result).toEqual({
        name: 'Field Name',
        options: {},
      });
    });

    it('should handle undefined options', () => {
      const viewColumnField: DecaTableViewColumnFields = {
        id: 'field1',
        header: 'Field Name',
        fieldMetaId: 'field1',
        isVisible: true,
        type: FieldType.TEXT,
        options: undefined,
      };

      const result = getFieldFromViewColumnField(viewColumnField);

      expect(result).toEqual({
        type: FieldType.TEXT,
        name: 'Field Name',
        options: {},
      });
    });
  });

  describe('normalizeOptionValues', () => {
    it('should filter out empty strings from array', () => {
      const result = normalizeOptionValues(['option1', '', 'option2', '']);
      expect(result).toEqual(['option1', 'option2']);
    });

    it('should return empty array for array with only empty strings', () => {
      const result = normalizeOptionValues(['', '', '']);
      expect(result).toEqual([]);
    });

    it('should return array with all non-empty values', () => {
      const result = normalizeOptionValues(['a', 'b', 'c']);
      expect(result).toEqual(['a', 'b', 'c']);
    });

    it('should split comma-separated string and filter empty values', () => {
      const result = normalizeOptionValues('option1, option2, , option3');
      expect(result).toEqual(['option1', 'option2', 'option3']);
    });

    it('should trim whitespace from comma-separated values', () => {
      const result = normalizeOptionValues('  option1  ,  option2  ,  option3  ');
      expect(result).toEqual(['option1', 'option2', 'option3']);
    });

    it('should return single-item array for string without comma', () => {
      const result = normalizeOptionValues('single-option');
      expect(result).toEqual(['single-option']);
    });

    it('should trim single string value', () => {
      const result = normalizeOptionValues('  single-option  ');
      expect(result).toEqual(['single-option']);
    });

    it('should return empty array for empty string', () => {
      const result = normalizeOptionValues('');
      expect(result).toEqual([]);
    });

    it('should return empty array for string with only commas and spaces', () => {
      const result = normalizeOptionValues(' , , , ');
      expect(result).toEqual([]);
    });

    it('should return empty array for non-string, non-array input', () => {
      const result = normalizeOptionValues(null as any);
      expect(result).toEqual([]);
    });

    it('should return empty array for undefined input', () => {
      const result = normalizeOptionValues(undefined as any);
      expect(result).toEqual([]);
    });

    it('should return empty array for number input', () => {
      const result = normalizeOptionValues(123 as any);
      expect(result).toEqual([]);
    });
  });
});