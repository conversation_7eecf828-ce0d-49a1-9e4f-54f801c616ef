import { mockLibraries, renderWithRouter } from '@/utils/test';
import { screen } from '@testing-library/react';
import { vi, describe, it, expect, beforeEach, afterEach } from 'vitest';
import TableDataClearOverlay from './LoadingOverlay';
import { IWebsocketResponse } from '@/types';

// Mock dependencies using vi.hoisted like ai-studio
const mocks = vi.hoisted(() => {
  const mockCloseOverlay = vi.fn();
  const mockCreateCustomEventListener = vi.fn();
  const mockUnregister = vi.fn();
  const mockUseWindowEvent = vi.fn();
  let dataClearHandler: Function | undefined;
  let keydownHandler: Function | undefined;

  return {
    mockCloseOverlay,
    mockCreateCustomEventListener,
    mockUnregister,
    mockUseWindowEvent,
    dataClearHandler,
    keydownHandler,
  };
});

vi.mock('@resola-ai/utils', () => ({
  createCustomEventListener: mocks.mockCreateCustomEventListener,
}));

vi.mock('@mantine/hooks', () => ({
  useWindowEvent: mocks.mockUseWindowEvent,
}));

vi.mock('@tolgee/react', () => ({
  useTranslate: () => ({ t: (key: string) => key }),
}));

vi.mock('../Common', () => ({
  LoaderSpinner: () => <div data-testid="loader-spinner">Loading...</div>,
}));

mockLibraries();

describe('TableDataClearOverlay', () => {
  const defaultProps = {
    isShow: true,
    closeOverlay: mocks.mockCloseOverlay,
    className: 'test-class',
    parentId: 'test-parent',
  };

  beforeEach(() => {
    vi.clearAllMocks();
    
    // Reset document.activeElement
    Object.defineProperty(document, 'activeElement', {
      value: null,
      writable: true
    });
    
    // Mock event listeners directly like ai-studio does
    window.addEventListener = vi.fn();
    window.removeEventListener = vi.fn();
    document.addEventListener = vi.fn();
    document.removeEventListener = vi.fn();
    
    // Setup mocks to capture handlers and return proper values
    mocks.mockCreateCustomEventListener.mockImplementation((event, handler) => {
      // Store the handler for later testing
      if (event === 'data.clear') {
        mocks.dataClearHandler = handler;
      }
      return mocks.mockUnregister;
    });
    
    mocks.mockUseWindowEvent.mockImplementation((event, handler) => {
      // Store the handler for later testing
      if (event === 'keydown') {
        mocks.keydownHandler = handler;
      }
    });
  });

  afterEach(() => {
    vi.clearAllMocks();
  });

  describe('Rendering', () => {
    it('should render when isShow is true', () => {
      renderWithRouter(<TableDataClearOverlay {...defaultProps} />);

      expect(screen.getByTestId('loader-spinner')).toBeInTheDocument();
      expect(screen.getByText('dataClearing')).toBeInTheDocument();
    });

    it('should not render when isShow is false', () => {
      renderWithRouter(<TableDataClearOverlay {...defaultProps} isShow={false} />);

      expect(screen.queryByTestId('loader-spinner')).not.toBeInTheDocument();
      expect(screen.queryByText('dataClearing')).not.toBeInTheDocument();
    });

    it('should render with custom className', () => {
      const { container } = renderWithRouter(
        <TableDataClearOverlay {...defaultProps} className="custom-class" />
      );

      const overlay = container.querySelector('.custom-class');
      expect(overlay).toBeInTheDocument();
    });

    it('should render without className', () => {
      renderWithRouter(
        <TableDataClearOverlay isShow={true} closeOverlay={mocks.mockCloseOverlay} />
      );

      expect(screen.getByTestId('loader-spinner')).toBeInTheDocument();
      expect(screen.getByText('dataClearing')).toBeInTheDocument();
    });
  });

  describe('Component Structure', () => {
    it('should render with correct structure and styling', () => {
      const { container } = renderWithRouter(<TableDataClearOverlay {...defaultProps} />);

      const centerElement = container.querySelector('.mantine-Center-root');
      expect(centerElement).toBeInTheDocument();
      
      expect(screen.getByTestId('loader-spinner')).toBeInTheDocument();
      expect(screen.getByText('dataClearing')).toBeInTheDocument();
    });

    it('should apply custom className correctly', () => {
      const { container } = renderWithRouter(
        <TableDataClearOverlay {...defaultProps} className="custom-overlay" />
      );

      const overlay = container.querySelector('.custom-overlay');
      expect(overlay).toBeInTheDocument();
    });
  });

  describe('Props Handling', () => {
    it('should handle all required props', () => {
      renderWithRouter(
        <TableDataClearOverlay 
          isShow={true} 
          closeOverlay={mocks.mockCloseOverlay}
          className="test-class"
          parentId="test-parent"
        />
      );

      expect(screen.getByTestId('loader-spinner')).toBeInTheDocument();
      expect(screen.getByText('dataClearing')).toBeInTheDocument();
    });

    it('should handle minimal props', () => {
      renderWithRouter(
        <TableDataClearOverlay 
          isShow={true} 
          closeOverlay={mocks.mockCloseOverlay}
        />
      );

      expect(screen.getByTestId('loader-spinner')).toBeInTheDocument();
      expect(screen.getByText('dataClearing')).toBeInTheDocument();
    });
  });

  describe('Conditional Rendering', () => {
    it('should not render content when isShow is false', () => {
      renderWithRouter(
        <TableDataClearOverlay {...defaultProps} isShow={false} />
      );

      expect(screen.queryByTestId('loader-spinner')).not.toBeInTheDocument();
      expect(screen.queryByText('dataClearing')).not.toBeInTheDocument();
    });

    it('should render when isShow is true', () => {
      renderWithRouter(
        <TableDataClearOverlay {...defaultProps} isShow={true} />
      );

      expect(screen.getByTestId('loader-spinner')).toBeInTheDocument();
      expect(screen.getByText('dataClearing')).toBeInTheDocument();
    });
  });

  describe('Event Handlers', () => {

    it('should handle dataClearHandler when isCleaned is true (lines 38-43)', () => {
      renderWithRouter(<TableDataClearOverlay {...defaultProps} />);

      // Simulate the event with isCleaned: true - this should execute lines 38-43
      const mockEvent = {
        detail: {
          data: {
            isCleaned: true
          }
        }
      } as unknown as CustomEvent<IWebsocketResponse<{ isCleaned?: boolean }>>;

      // Call the event handler directly to execute the component's internal logic
      if (mocks.dataClearHandler) {
        mocks.dataClearHandler(mockEvent);
        expect(mocks.mockCloseOverlay).toHaveBeenCalled();
      }
    });

    it('should not call closeOverlay when isCleaned is false', () => {
      renderWithRouter(<TableDataClearOverlay {...defaultProps} />);

      // Simulate the event with isCleaned: false
      const mockEvent = {
        detail: {
          data: {
            isCleaned: false
          }
        }
      };

      // Call the event handler
      if (mocks.dataClearHandler) {
        mocks.dataClearHandler(mockEvent);
        expect(mocks.mockCloseOverlay).not.toHaveBeenCalled();
      }
    });

    it('should not call closeOverlay when data is undefined', () => {
      renderWithRouter(<TableDataClearOverlay {...defaultProps} />);

      // Simulate the event with undefined data
      const mockEvent = {
        detail: {
          data: undefined
        }
      };

      // Call the event handler
      if (mocks.dataClearHandler) {
        mocks.dataClearHandler(mockEvent);
        expect(mocks.mockCloseOverlay).not.toHaveBeenCalled();
      }
    });

    it('should prevent keyboard events when overlay is visible and focus is in parent', () => {
      // Mock document.activeElement and closest method to return valid parent element
      const mockActiveElement = {
        closest: vi.fn().mockReturnValue(document.createElement('div'))
      };
      Object.defineProperty(document, 'activeElement', {
        value: mockActiveElement,
        writable: true
      });

      renderWithRouter(<TableDataClearOverlay {...defaultProps} isShow={true} />);

      // Create a mock keyboard event with proper preventDefault/stopPropagation
      const mockEvent = {
        preventDefault: vi.fn(),
        stopPropagation: vi.fn()
      } as unknown as KeyboardEvent;

      // Call the keydown handler - this should execute lines 49-56
      if (mocks.keydownHandler) {
        mocks.keydownHandler(mockEvent);
        expect(mockEvent.preventDefault).toHaveBeenCalled();
        expect(mockEvent.stopPropagation).toHaveBeenCalled();
        expect(mockActiveElement.closest).toHaveBeenCalledWith('#test-parent');
      }
    });

    it('should not prevent keyboard events when overlay is hidden', () => {
      renderWithRouter(<TableDataClearOverlay {...defaultProps} isShow={false} />);

      // Create a mock keyboard event
      const mockEvent = {
        preventDefault: vi.fn(),
        stopPropagation: vi.fn()
      } as unknown as KeyboardEvent;

      // Call the keydown handler
      if (mocks.keydownHandler) {
        mocks.keydownHandler(mockEvent);
        expect(mockEvent.preventDefault).not.toHaveBeenCalled();
        expect(mockEvent.stopPropagation).not.toHaveBeenCalled();
      }
    });

    it('should not prevent keyboard events when no parent container is specified', () => {
      // Mock document.activeElement and closest method
      const mockActiveElement = {
        closest: vi.fn().mockReturnValue(true)
      };
      Object.defineProperty(document, 'activeElement', {
        value: mockActiveElement,
        writable: true
      });

      renderWithRouter(
        <TableDataClearOverlay 
          {...defaultProps} 
          isShow={true} 
          parentId={undefined} 
        />
      );

      // Create a mock keyboard event
      const mockEvent = {
        preventDefault: vi.fn(),
        stopPropagation: vi.fn()
      } as unknown as KeyboardEvent;

      // Call the keydown handler
      if (mocks.keydownHandler) {
        mocks.keydownHandler(mockEvent);
        expect(mockEvent.preventDefault).not.toHaveBeenCalled();
        expect(mockEvent.stopPropagation).not.toHaveBeenCalled();
      }
    });

    it('should not prevent keyboard events when no element has focus', () => {
      // Mock document.activeElement as null
      Object.defineProperty(document, 'activeElement', {
        value: null,
        writable: true
      });

      renderWithRouter(<TableDataClearOverlay {...defaultProps} isShow={true} />);

      // Create a mock keyboard event
      const mockEvent = {
        preventDefault: vi.fn(),
        stopPropagation: vi.fn()
      } as unknown as KeyboardEvent;

      // Call the keydown handler
      if (mocks.keydownHandler) {
        mocks.keydownHandler(mockEvent);
        expect(mockEvent.preventDefault).not.toHaveBeenCalled();
        expect(mockEvent.stopPropagation).not.toHaveBeenCalled();
      }
    });

    it('should not prevent keyboard events when focus is outside parent container', () => {
      // Mock document.activeElement and closest method to return null
      const mockActiveElement = {
        closest: vi.fn().mockReturnValue(null)
      };
      Object.defineProperty(document, 'activeElement', {
        value: mockActiveElement,
        writable: true
      });

      renderWithRouter(<TableDataClearOverlay {...defaultProps} isShow={true} />);

      // Create a mock keyboard event
      const mockEvent = {
        preventDefault: vi.fn(),
        stopPropagation: vi.fn()
      } as unknown as KeyboardEvent;

      // Call the keydown handler
      if (mocks.keydownHandler) {
        mocks.keydownHandler(mockEvent);
        expect(mockEvent.preventDefault).not.toHaveBeenCalled();
        expect(mockEvent.stopPropagation).not.toHaveBeenCalled();
      }
    });

    it('should close overlay when data clear event has isCleaned true', () => {
      const mockCloseOverlay = vi.fn();
      
      // Capture the real data clear handler when component registers it
      const originalCreateCustomEventListener = mocks.mockCreateCustomEventListener;
      let realDataClearHandler: Function | null = null;
      
      mocks.mockCreateCustomEventListener.mockImplementation((event, handler) => {
        if (event === 'data.clear') {
          realDataClearHandler = handler;
        }
        return mocks.mockUnregister;
      });
      
      renderWithRouter(
        <TableDataClearOverlay 
          isShow={true} 
          closeOverlay={mockCloseOverlay}
          parentId="test-parent"
        />
      );

      // Execute the real data clear handler with isCleaned: true
      if (realDataClearHandler) {
        const cleanedEvent = {
          detail: {
            data: {
              isCleaned: true
            }
          }
        } as any;

        (realDataClearHandler as any)(cleanedEvent);
        expect(mockCloseOverlay).toHaveBeenCalled();
      }
      
      mocks.mockCreateCustomEventListener.mockImplementation(originalCreateCustomEventListener);
    });

    it('should prevent keyboard events when overlay is shown and focus is inside parent', () => {
      // Capture the real keyboard handler when component registers it
      const originalUseWindowEvent = mocks.mockUseWindowEvent;
      let realKeydownHandler: Function | null = null;
      
      mocks.mockUseWindowEvent.mockImplementation((event, handler) => {
        if (event === 'keydown') {
          realKeydownHandler = handler;
        }
      });
      
      // Set up DOM with parent and child element
      const parentElement = document.createElement('div');
      parentElement.id = 'test-parent';
      document.body.appendChild(parentElement);
      
      const targetElement = document.createElement('input');
      parentElement.appendChild(targetElement);
      
      Object.defineProperty(document, 'activeElement', {
        value: targetElement,
        writable: true
      });

      renderWithRouter(
        <TableDataClearOverlay 
          isShow={true} 
          closeOverlay={vi.fn()}
          parentId="test-parent"
        />
      );

      // Execute the real keyboard handler
      if (realKeydownHandler) {
        const keyEvent = {
          preventDefault: vi.fn(),
          stopPropagation: vi.fn()
        } as unknown as KeyboardEvent;

        (realKeydownHandler as any)(keyEvent);
        
        expect(keyEvent.preventDefault).toHaveBeenCalled();
        expect(keyEvent.stopPropagation).toHaveBeenCalled();
      }
      
      document.body.removeChild(parentElement);
      mocks.mockUseWindowEvent.mockImplementation(originalUseWindowEvent);
    });

    it('should capture and execute real keyboard handler to prevent events', () => {
      // Advanced approach: Intercept addEventListener to capture the real handler
      const originalAddEventListener = global.addEventListener;
      let realKeydownHandler: Function | null = null;
      
      global.addEventListener = vi.fn((event: string, handler: any, options?: any) => {
        if (event === 'keydown') {
          realKeydownHandler = handler;
        }
        return originalAddEventListener?.call(global, event, handler, options);
      }) as any;
      
      // Set up DOM environment
      const parentElement = document.createElement('div');
      parentElement.id = 'test-parent';
      document.body.appendChild(parentElement);
      
      const targetElement = document.createElement('input');
      parentElement.appendChild(targetElement);
      
      Object.defineProperty(document, 'activeElement', {
        value: targetElement,
        writable: true
      });

      renderWithRouter(
        <TableDataClearOverlay 
          isShow={true} 
          closeOverlay={vi.fn()}
          parentId="test-parent"
        />
      );

      // Execute the real component handler
      if (realKeydownHandler) {
        const keyEvent = new KeyboardEvent('keydown', {
          key: 'Enter',
          bubbles: true,
          cancelable: true
        });
        
        const preventDefaultSpy = vi.spyOn(keyEvent, 'preventDefault');
        const stopPropagationSpy = vi.spyOn(keyEvent, 'stopPropagation');

        (realKeydownHandler as any)(keyEvent);
        
        expect(preventDefaultSpy).toHaveBeenCalled();
        expect(stopPropagationSpy).toHaveBeenCalled();
      }
      
      document.body.removeChild(parentElement);
      
      if (originalAddEventListener) {
        global.addEventListener = originalAddEventListener;
      }
    });

  });
});