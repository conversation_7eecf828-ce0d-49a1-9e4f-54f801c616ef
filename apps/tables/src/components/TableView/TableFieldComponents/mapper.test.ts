import { 
  DateFormatOption, 
  FieldType, 
  TimeFormatOption, 
  CurrencyFormatOption, 
  NumberFormatOption, 
  PercentageFormatOption, 
  SelectOrderOption 
} from '@/types';
import * as mapper from './mapper';

describe('TableFieldComponents/mapper', () => {
  describe('convertToTableFieldOptionsPayload', () => {
    it('should handle DATETIME', () => {
      const result = mapper.convertToTableFieldOptionsPayload(
        {
          date: { format: 'ja' },
          time: { enabled: true, format: '12h' },
          timezone: { enabled: true, format: 'Asia/Tokyo' },
          displayTimezone: true,
          useCurrentDate: true,
        },
        FieldType.DATETIME
      );
      expect(result).toHaveProperty('date');
      expect(result).toHaveProperty('time');
      expect(result).toHaveProperty('timezone');
    });
    it('should handle CREATEDAT/UPDATEDAT', () => {
      const result = mapper.convertToTableFieldOptionsPayload(
        {
          date: { format: 'en' },
          time: { enabled: false, format: '24h' },
          timezone: { enabled: false, format: 'UTC' },
          displayTimezone: false,
        },
        FieldType.CREATEDAT
      );
      expect(result).toHaveProperty('date');
      expect(result).toHaveProperty('time');
      expect(result).toHaveProperty('timezone');
    });
    it('should handle PHONE', () => {
      const result = mapper.convertToTableFieldOptionsPayload(
        {
          format: 'US',
        },
        FieldType.PHONE
      );
      expect(result).toHaveProperty('phoneFormat');
    });
    it('should handle LONGTEXT', () => {
      const result = mapper.convertToTableFieldOptionsPayload(
        {
          // No required options for LONGTEXT
        } as any,
        FieldType.LONGTEXT
      );
      expect(result).toHaveProperty('richtext');
    });
    it('should handle SELECT', () => {
      const result = mapper.convertToTableFieldOptionsPayload(
        {
          choices: [{ label: 'A', color: 'red', id: '1' }],
          order: 'asc',
          enableColor: true,
          defaultValue: '1',
        } as any,
        FieldType.SELECT
      );
      expect(result).toHaveProperty('choices');
      expect(result).toHaveProperty('order');
    });
    it('should handle MULTISELECT', () => {
      const result = mapper.convertToTableFieldOptionsPayload(
        {
          choices: [{ label: 'A', color: 'red', id: '1' }],
          order: 'desc',
          enableColor: false,
          defaultValue: ['1', '2'],
        } as any,
        FieldType.MULTISELECT
      );
      expect(result).toHaveProperty('choices');
      expect(result).toHaveProperty('order');
    });
    it('should handle CURRENCY', () => {
      const result = mapper.convertToTableFieldOptionsPayload(
        {
          currency: 'USD',
          customSymbol: '$',
          decimalPlaces: 2,
          separator: { enabled: true, format: ',' },
        } as any,
        FieldType.CURRENCY
      );
      expect(result).toHaveProperty('format');
      expect(result).toHaveProperty('customSymbol');
    });
    it('should handle NUMBER', () => {
      const result = mapper.convertToTableFieldOptionsPayload(
        {
          numberFormat: 'integer',
          decimalPlaces: 0,
        } as any,
        FieldType.NUMBER
      );
      expect(result).toHaveProperty('numberFormat');
    });
    it('should handle PERCENTAGE', () => {
      const result = mapper.convertToTableFieldOptionsPayload(
        {
          decimalPlaces: 1,
          presentation: { enabled: true, type: 'bar' },
          separator: { enabled: false },
        } as any,
        FieldType.PERCENTAGE
      );
      expect(result).toHaveProperty('decimalPlaces');
    });
    it('should return null for unknown type', () => {
      const result = mapper.convertToTableFieldOptionsPayload({} as any, 'unknown' as any);
      expect(result).toBeNull();
    });
    it('should handle missing options gracefully', () => {
      expect(() =>
        mapper.convertToTableFieldOptionsPayload(undefined, FieldType.DATETIME)
      ).not.toThrow();
    });

    // Additional comprehensive tests for better coverage
    it('should handle DATETIME with all date formats', () => {
      const formats = ['ja', 'en', 'long', 'eu'];
      formats.forEach(format => {
        const result = mapper.convertToTableFieldOptionsPayload(
          { date: { format } },
          FieldType.DATETIME
        );
        expect(result).toHaveProperty('date');
        // The format might be undefined if the mapping doesn't exist, which is expected
        expect((result as any)?.date).toBeDefined();
      });
    });

    it('should handle DATETIME with all time formats', () => {
      const formats = ['12h', '24h'];
      formats.forEach(format => {
        const result = mapper.convertToTableFieldOptionsPayload(
          { date: { format: 'YYYY-MM-DD' }, time: { enabled: true, format } },
          FieldType.DATETIME
        );
        expect(result).toHaveProperty('time');
        // The format might be undefined if the mapping doesn't exist, which is expected
        expect((result as any)?.time).toBeDefined();
      });
    });

    it('should handle CREATEDAT with all date formats', () => {
      const formats = ['ja', 'en', 'long', 'eu'];
      formats.forEach(format => {
        const result = mapper.convertToTableFieldOptionsPayload(
          { date: { format } },
          FieldType.CREATEDAT
        );
        expect(result).toHaveProperty('date');
        // The format might be undefined if the mapping doesn't exist, which is expected
        expect((result as any)?.date).toBeDefined();
      });
    });

    it('should handle UPDATEDAT with all date formats', () => {
      const formats = ['ja', 'en', 'long', 'eu'];
      formats.forEach(format => {
        const result = mapper.convertToTableFieldOptionsPayload(
          { date: { format } },
          FieldType.UPDATEDAT
        );
        expect(result).toHaveProperty('date');
        // The format might be undefined if the mapping doesn't exist, which is expected
        expect((result as any)?.date).toBeDefined();
      });
    });

    it('should handle SELECT with all order options', () => {
      const orders = ['asc' as const, 'desc' as const, 'manual' as const];
      orders.forEach(order => {
        const result = mapper.convertToTableFieldOptionsPayload(
          { choices: [], order },
          FieldType.SELECT
        );
        expect(result).toHaveProperty('order');
        expect((result as any)?.order).toBeDefined();
      });
    });

    it('should handle MULTISELECT with all order options', () => {
      const orders = ['asc' as const, 'desc' as const, 'manual' as const];
      orders.forEach(order => {
        const result = mapper.convertToTableFieldOptionsPayload(
          { choices: [], order },
          FieldType.MULTISELECT
        );
        expect(result).toHaveProperty('order');
        expect((result as any)?.order).toBeDefined();
      });
    });

    it('should handle MULTISELECT with array defaultValue', () => {
      const result = mapper.convertToTableFieldOptionsPayload(
        { choices: [], defaultValue: ['1', '2', '3'] },
        FieldType.MULTISELECT
      );
      expect(result).toHaveProperty('defaultValue');
      expect((result as any)?.defaultValue).toBe('1,2,3');
    });

    it('should handle MULTISELECT with empty array defaultValue', () => {
      const result = mapper.convertToTableFieldOptionsPayload(
        { choices: [], defaultValue: [] },
        FieldType.MULTISELECT
      );
      expect(result).toHaveProperty('defaultValue');
      expect((result as any)?.defaultValue).toBe('');
    });

    it('should handle MULTISELECT with invalid defaultValue', () => {
      const result = mapper.convertToTableFieldOptionsPayload(
        { choices: [], defaultValue: undefined },
        FieldType.MULTISELECT
      );
      expect(result).toHaveProperty('defaultValue');
      expect((result as any)?.defaultValue).toBe('');
    });

    it('should handle CURRENCY with all currency formats', () => {
      const currencies = ['USD', 'JPY'];
      currencies.forEach(currency => {
        const result = mapper.convertToTableFieldOptionsPayload(
          { currency, decimalPlaces: 2 },
          FieldType.CURRENCY
        );
        expect(result).toHaveProperty('format');
        expect((result as any)?.format).toBeDefined();
      });
    });

    it('should handle CURRENCY with separator disabled', () => {
      const result = mapper.convertToTableFieldOptionsPayload(
        { currency: 'USD', decimalPlaces: 2, separator: { enabled: false } },
        FieldType.CURRENCY
      );
      expect(result).toHaveProperty('showThousandsSeparator');
      expect((result as any)?.showThousandsSeparator).toBe(false);
      expect((result as any)?.thousandAndDecimalSeparator).toBeUndefined();
    });

    it('should handle CURRENCY with separator enabled', () => {
      const result = mapper.convertToTableFieldOptionsPayload(
        { currency: 'USD', decimalPlaces: 2, separator: { enabled: true, format: ',' } },
        FieldType.CURRENCY
      );
      expect(result).toHaveProperty('showThousandsSeparator');
      expect((result as any)?.showThousandsSeparator).toBe(true);
      expect((result as any)?.thousandAndDecimalSeparator).toBe(',');
    });

    it('should handle NUMBER with all number formats', () => {
      const formats = ['integer' as const, 'decimal' as const];
      formats.forEach(format => {
        const result = mapper.convertToTableFieldOptionsPayload(
          { numberFormat: format } as any,
          FieldType.NUMBER
        );
        expect(result).toHaveProperty('numberFormat');
        expect((result as any)?.numberFormat).toBeDefined();
      });
    });

    it('should handle PERCENTAGE with all presentation types', () => {
      const presentations = ['bar' as const, 'ring' as const];
      presentations.forEach(type => {
        const result = mapper.convertToTableFieldOptionsPayload(
          { presentation: { enabled: true, type } } as any,
          FieldType.PERCENTAGE
        );
        expect(result).toHaveProperty('presentation');
        expect((result as any)?.presentation).toBeDefined();
      });
    });

    it('should handle PERCENTAGE with presentation disabled', () => {
      const result = mapper.convertToTableFieldOptionsPayload(
        { separator: { enabled: false }, presentation: { enabled: false } },
        FieldType.PERCENTAGE
      );
      expect(result).toHaveProperty('presentation');
      expect((result as any)?.presentation).toBeUndefined();
    });

    it('should handle SELECT with empty choices array', () => {
      const result = mapper.convertToTableFieldOptionsPayload(
        { choices: [] },
        FieldType.SELECT
      );
      expect(result).toHaveProperty('choices');
      expect((result as any)?.choices).toEqual([]);
    });

    it('should handle MULTISELECT with empty choices array', () => {
      const result = mapper.convertToTableFieldOptionsPayload(
        { choices: [] },
        FieldType.MULTISELECT
      );
      expect(result).toHaveProperty('choices');
      expect((result as any)?.choices).toEqual([]);
    });

    it('should handle SELECT with choices containing null values', () => {
      const result = mapper.convertToTableFieldOptionsPayload(
        { choices: [{ label: '', color: '', id: '' }] },
        FieldType.SELECT
      );
      expect(result).toHaveProperty('choices');
      expect((result as any)?.choices).toEqual([{ label: '', color: '', id: '' }]);
    });

    it('should handle MULTISELECT with choices containing null values', () => {
      const result = mapper.convertToTableFieldOptionsPayload(
        { choices: [{ label: '', color: '', id: '' }] },
        FieldType.MULTISELECT
      );
      expect(result).toHaveProperty('choices');
      expect((result as any)?.choices).toEqual([{ label: '', color: '', id: '' }]);
    });
  });

  describe('convertFromTableFieldOptionsPayload', () => {
    it('should handle DATETIME', () => {
      const result = mapper.convertFromTableFieldOptionsPayload(
        {
          date: { format: DateFormatOption.JAPAN },
          time: { enabled: true, format: TimeFormatOption['12h'] },
          timezone: { enabled: true, format: 'Asia/Tokyo' },
        } as any,
        FieldType.DATETIME
      );
      expect(result).toHaveProperty('date');
      expect(result).toHaveProperty('time');
      expect(result).toHaveProperty('timezone');
    });

    it('should handle CREATEDAT', () => {
      const result = mapper.convertFromTableFieldOptionsPayload(
        {
          date: { format: DateFormatOption.US },
          time: { enabled: false, format: TimeFormatOption['24h'] },
          timezone: { enabled: false, format: 'UTC' },
        } as any,
        FieldType.CREATEDAT
      );
      expect(result).toHaveProperty('date');
      expect(result).toHaveProperty('time');
      expect(result).toHaveProperty('timezone');
    });

    it('should handle UPDATEDAT', () => {
      const result = mapper.convertFromTableFieldOptionsPayload(
        {
          date: { format: DateFormatOption.EUROPEAN },
          time: { enabled: true, format: TimeFormatOption['12h'] },
          timezone: { enabled: true, format: 'Europe/London' },
        } as any,
        FieldType.UPDATEDAT
      );
      expect(result).toHaveProperty('date');
      expect(result).toHaveProperty('time');
      expect(result).toHaveProperty('timezone');
    });

    it('should handle PHONE', () => {
      const result = mapper.convertFromTableFieldOptionsPayload(
        {
          phoneFormat: { format: 'US' },
        } as any,
        FieldType.PHONE
      );
      expect(result).toHaveProperty('format');
      expect((result as any)?.format).toBe('US');
    });

    it('should handle LONGTEXT', () => {
      const result = mapper.convertFromTableFieldOptionsPayload(
        {} as any,
        FieldType.LONGTEXT
      );
      expect(result).toBeNull();
    });

    it('should handle SELECT', () => {
      const result = mapper.convertFromTableFieldOptionsPayload(
        {
          choices: [{ label: 'A', color: 'red', id: '1' }],
          order: SelectOrderOption.ASC,
          toggleColorMode: true,
          defaultValue: '1',
        } as any,
        FieldType.SELECT
      );
      expect(result).toHaveProperty('choices');
      expect(result).toHaveProperty('order');
      expect(result).toHaveProperty('enableColor');
      expect(result).toHaveProperty('defaultValue');
    });

    it('should handle MULTISELECT', () => {
      const result = mapper.convertFromTableFieldOptionsPayload(
        {
          choices: [{ label: 'A', color: 'red', id: '1' }],
          order: SelectOrderOption.DESC,
          toggleColorMode: false,
          defaultValue: '1,2,3',
        } as any,
        FieldType.MULTISELECT
      );
      expect(result).toHaveProperty('choices');
      expect(result).toHaveProperty('order');
      expect(result).toHaveProperty('enableColor');
      expect(result).toHaveProperty('defaultValue');
      expect((result as any)?.defaultValue).toEqual(['1', '2', '3']);
    });

    it('should handle MULTISELECT with empty defaultValue', () => {
      const result = mapper.convertFromTableFieldOptionsPayload(
        {
          choices: [],
          order: SelectOrderOption.MANUAL,
          toggleColorMode: false,
          defaultValue: '',
        } as any,
        FieldType.MULTISELECT
      );
      expect(result).toHaveProperty('defaultValue');
      expect((result as any)?.defaultValue).toEqual(['']);
    });

    it('should handle MULTISELECT with invalid defaultValue', () => {
      const result = mapper.convertFromTableFieldOptionsPayload(
        {
          choices: [],
          order: SelectOrderOption.MANUAL,
          toggleColorMode: false,
          defaultValue: null,
        } as any,
        FieldType.MULTISELECT
      );
      expect(result).toHaveProperty('defaultValue');
      expect((result as any)?.defaultValue).toEqual([]);
    });

    it('should handle CURRENCY', () => {
      const result = mapper.convertFromTableFieldOptionsPayload(
        {
          format: CurrencyFormatOption.US,
          customSymbol: '$',
          decimalPlaces: 2,
          showThousandsSeparator: true,
          thousandAndDecimalSeparator: ',',
        } as any,
        FieldType.CURRENCY
      );
      expect(result).toHaveProperty('currency');
      expect(result).toHaveProperty('decimalPlaces');
      expect(result).toHaveProperty('separator');
      expect((result as any)?.currency).toBe('USD');
    });

    it('should handle CURRENCY with separator disabled', () => {
      const result = mapper.convertFromTableFieldOptionsPayload(
        {
          format: CurrencyFormatOption.JP,
          customSymbol: '¥',
          decimalPlaces: 0,
          showThousandsSeparator: false,
        } as any,
        FieldType.CURRENCY
      );
      expect(result).toHaveProperty('separator');
      expect((result as any)?.separator?.enabled).toBe(false);
    });

    it('should handle NUMBER', () => {
      const result = mapper.convertFromTableFieldOptionsPayload(
        {
          numberFormat: NumberFormatOption.INTEGER,
          decimalPlaces: 0,
        } as any,
        FieldType.NUMBER
      );
      expect(result).toHaveProperty('numberFormat');
      expect(result).toHaveProperty('decimalPlaces');
      expect((result as any)?.numberFormat).toBe('integer');
    });

    it('should handle NUMBER with decimal format', () => {
      const result = mapper.convertFromTableFieldOptionsPayload(
        {
          numberFormat: NumberFormatOption.DECIMAL,
          decimalPlaces: 2,
        } as any,
        FieldType.NUMBER
      );
      expect(result).toHaveProperty('numberFormat');
      expect((result as any)?.numberFormat).toBe('decimal');
    });

    it('should handle PERCENTAGE', () => {
      const result = mapper.convertFromTableFieldOptionsPayload(
        {
          decimalPlaces: 1,
          presentation: PercentageFormatOption.BAR,
        } as any,
        FieldType.PERCENTAGE
      );
      expect(result).toHaveProperty('decimalPlaces');
      expect(result).toHaveProperty('presentation');
      expect((result as any)?.presentation?.enabled).toBe(true);
      expect((result as any)?.presentation?.type).toBe('bar');
    });

    it('should handle PERCENTAGE with pie presentation', () => {
      const result = mapper.convertFromTableFieldOptionsPayload(
        {
          decimalPlaces: 2,
          presentation: PercentageFormatOption.PIE,
        } as any,
        FieldType.PERCENTAGE
      );
      expect(result).toHaveProperty('presentation');
      expect((result as any)?.presentation?.enabled).toBe(true);
      expect((result as any)?.presentation?.type).toBe('ring');
    });

    it('should handle PERCENTAGE with no presentation', () => {
      const result = mapper.convertFromTableFieldOptionsPayload(
        {
          decimalPlaces: 0,
          presentation: null,
        } as any,
        FieldType.PERCENTAGE
      );
      expect(result).toHaveProperty('presentation');
      expect((result as any)?.presentation?.enabled).toBe(false);
    });

    it('should handle PERCENTAGE with empty presentation', () => {
      const result = mapper.convertFromTableFieldOptionsPayload(
        {
          decimalPlaces: 0,
          presentation: '',
        } as any,
        FieldType.PERCENTAGE
      );
      expect(result).toHaveProperty('presentation');
      expect((result as any)?.presentation?.enabled).toBe(false);
    });

    it('should handle DATETIME with all date formats', () => {
      const formats = [DateFormatOption.JAPAN, DateFormatOption.US, DateFormatOption.LONGDATE, DateFormatOption.EUROPEAN];
      formats.forEach(format => {
        const result = mapper.convertFromTableFieldOptionsPayload(
          { date: { format } } as any,
          FieldType.DATETIME
        );
        expect(result).toHaveProperty('date');
        expect((result as any)?.date?.format).toBeDefined();
      });
    });

    it('should handle DATETIME with all time formats', () => {
      const formats = [TimeFormatOption['12h'], TimeFormatOption['24h']];
      formats.forEach(format => {
        const result = mapper.convertFromTableFieldOptionsPayload(
          { time: { enabled: true, format } } as any,
          FieldType.DATETIME
        );
        expect(result).toHaveProperty('time');
        expect((result as any)?.time?.format).toBeDefined();
      });
    });

    it('should handle CREATEDAT with all date formats', () => {
      const formats = [DateFormatOption.JAPAN, DateFormatOption.US, DateFormatOption.LONGDATE, DateFormatOption.EUROPEAN];
      formats.forEach(format => {
        const result = mapper.convertFromTableFieldOptionsPayload(
          { date: { format } } as any,
          FieldType.CREATEDAT
        );
        expect(result).toHaveProperty('date');
        expect((result as any)?.date?.format).toBeDefined();
      });
    });

    it('should handle UPDATEDAT with all date formats', () => {
      const formats = [DateFormatOption.JAPAN, DateFormatOption.US, DateFormatOption.LONGDATE, DateFormatOption.EUROPEAN];
      formats.forEach(format => {
        const result = mapper.convertFromTableFieldOptionsPayload(
          { date: { format } } as any,
          FieldType.UPDATEDAT
        );
        expect(result).toHaveProperty('date');
        expect((result as any)?.date?.format).toBeDefined();
      });
    });

    it('should handle CURRENCY with all currency formats', () => {
      const formats = [CurrencyFormatOption.US, CurrencyFormatOption.JP];
      formats.forEach(format => {
        const result = mapper.convertFromTableFieldOptionsPayload(
          { format } as any,
          FieldType.CURRENCY
        );
        expect(result).toHaveProperty('currency');
        expect((result as any)?.currency).toBeDefined();
      });
    });

    it('should handle NUMBER with all number formats', () => {
      const formats = [NumberFormatOption.INTEGER, NumberFormatOption.DECIMAL];
      formats.forEach(format => {
        const result = mapper.convertFromTableFieldOptionsPayload(
          { numberFormat: format } as any,
          FieldType.NUMBER
        );
        expect(result).toHaveProperty('numberFormat');
        expect((result as any)?.numberFormat).toBeDefined();
      });
    });

    it('should handle PERCENTAGE with all presentation formats', () => {
      const presentations = [PercentageFormatOption.BAR, PercentageFormatOption.PIE];
      presentations.forEach(presentation => {
        const result = mapper.convertFromTableFieldOptionsPayload(
          { presentation } as any,
          FieldType.PERCENTAGE
        );
        expect(result).toHaveProperty('presentation');
        expect((result as any)?.presentation?.enabled).toBe(true);
      });
    });

    it('should return null for unknown type', () => {
      const result = mapper.convertFromTableFieldOptionsPayload({} as any, 'unknown' as any);
      expect(result).toBeNull();
    });

    it('should handle missing options gracefully', () => {
      expect(() =>
        mapper.convertFromTableFieldOptionsPayload(undefined as any, FieldType.DATETIME)
      ).not.toThrow();
    });

    it('should handle empty options object', () => {
      const result = mapper.convertFromTableFieldOptionsPayload({} as any, FieldType.DATETIME);
      expect(result).toHaveProperty('date');
      expect(result).toHaveProperty('time');
      expect(result).toHaveProperty('timezone');
    });
  });
});
