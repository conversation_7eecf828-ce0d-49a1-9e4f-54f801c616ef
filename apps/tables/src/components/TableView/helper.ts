import { FieldAPIUpdatePayload } from '@/services/api';
import { Field, FieldType, View, ViewRowHeight, ViewSortOrder } from '@/types';
import {
  calculateColumnVisibility,
  formatMongoDBQuery,
  parseMongoDBQuery,
  sanitizeRuleGroup,
} from '@/utils';
import {
  Sort as DecaTableSort,
  SortOrder as DecaTableSortOrder,
  View as DecaTableView,
  ViewColumnFields as DecaTableViewColumnFields,
} from '@resola-ai/ui/components';
import get from 'lodash/get';
import isEmpty from 'lodash/isEmpty';
import set from 'lodash/set';
import { MRT_Header } from 'mantine-react-table';

export const convertViewSortOrderToSort = (
  viewSortOrder: Record<string, ViewSortOrder>
): DecaTableSort[] => {
  return Object.entries(viewSortOrder).map(([fieldId, order]) => ({
    fieldId,
    order:
      order === ViewSortOrder.ASC ? DecaTableSortOrder.Ascending : DecaTableSortOrder.Descending,
  }));
};

export const convertSortToViewSortOrder = (
  sortArray: DecaTableSort[]
): Record<string, ViewSortOrder> => {
  return sortArray.reduce(
    (acc, { fieldId, order }) => {
      acc[fieldId] =
        order === DecaTableSortOrder.Ascending ? ViewSortOrder.ASC : ViewSortOrder.DESC;
      return acc;
    },
    {} as Record<string, ViewSortOrder>
  );
};

export const removeUnsortableFields = (
  sort: Record<string, ViewSortOrder>,
  fields: Field[],
  unsortableFieldTypes: FieldType[]
): Record<string, ViewSortOrder> => {
  if (!sort || isEmpty(sort)) return {};
  return Object.entries(sort).reduce((acc, [fieldId, order]) => {
    const field = fields.find((f) => f.id === fieldId);
    if (field && !unsortableFieldTypes.includes(field.type!)) {
      acc[fieldId] = order;
    }
    return acc;
  }, {});
};

export const removeUnfilterableFields = (
  filters: View['filter'],
  fields: Field[],
  unfilterableFieldTypes: string[]
): Record<string, string> => {
  if (!filters || isEmpty(filters)) return {};
  const parsedQuery = parseMongoDBQuery(filters);
  const sanitizedQuery = sanitizeRuleGroup(parsedQuery, fields, unfilterableFieldTypes);
  return formatMongoDBQuery(sanitizedQuery);
};

export const getOrderedColumns = (
  columns: DecaTableViewColumnFields[],
  sortColumnIds: string[]
): DecaTableViewColumnFields[] => {
  if (!sortColumnIds) return columns;
  return columns.sort((a, b) => {
    const indexA = sortColumnIds.indexOf(a.id);
    const indexB = sortColumnIds.indexOf(b.id);
    if (indexA === -1 && indexB === -1) return 0;
    if (indexA === -1) return 1;
    if (indexB === -1) return -1;
    return indexA - indexB;
  });
};

export const getVisibleColumns = (
  columns: DecaTableViewColumnFields[],
  project: Record<string, number>
): DecaTableViewColumnFields[] => {
  if (!project) return columns;
  return columns.map((column) => ({ ...column, isVisible: project[column.id] !== 0 }));
};

export const getViewPayload = (
  currentTableView: DecaTableView,
  tableFieldIds: string[],
  columnSizing: Record<string, number>
) => {
  const payload: Partial<View> = {
    name: currentTableView.name,
    description: currentTableView.description,
  };

  if (currentTableView.fields?.length > 0) {
    const visibleFieldIds = currentTableView.fields.filter((f) => f.isVisible).map((f) => f.id);
    const columnVisibility = calculateColumnVisibility(tableFieldIds, visibleFieldIds);
    set(payload, 'project', columnVisibility);
  }

  if (currentTableView.filters) {
    set(payload, 'filter', currentTableView.filters);
  }

  if (currentTableView.sort) {
    set(payload, 'sort', convertSortToViewSortOrder(currentTableView.sort));
  }

  if (currentTableView.rowHeight) {
    set(
      payload,
      'options.rowHeight',
      currentTableView.rowHeight === 'sm' ? ViewRowHeight.SHORT : ViewRowHeight.MEDIUM
    );
  }

  if (Object.keys(columnSizing).length > 0) {
    set(payload, 'options.columnSizes', columnSizing);
  }

  set(payload, 'options.automaticSorting', currentTableView.autoSort);

  return payload;
};

export const getFieldTypeFromMRTHeader = (header?: MRT_Header<Record<string, string>>) => {
  const fieldType = get(header, 'column.columnDef.type', FieldType.TEXT);
  return fieldType;
};

export const getSortTranslationKey = (
  fieldType: FieldType | string
): {
  asc: string;
  desc: string;
} => {
  switch (fieldType) {
    case FieldType.CHECKBOX: {
      return {
        asc: 'sort.sortAscCheckbox',
        desc: 'sort.sortDescCheckbox',
      };
    }

    case FieldType.DATETIME:
    case FieldType.CREATEDAT:
    case FieldType.UPDATEDAT:
      return {
        asc: 'sort.sortAscDate',
        desc: 'sort.sortDescDate',
      };

    case FieldType.SELECT:
    case FieldType.MULTISELECT:
      return {
        asc: 'sort.sortAscSelect',
        desc: 'sort.sortDescSelect',
      };

    case FieldType.NUMBER:
    case FieldType.PHONE:
    case FieldType.AUTOID:
    case FieldType.CURRENCY:
    case FieldType.PERCENTAGE:
      return {
        asc: 'sort.sortAscNumber',
        desc: 'sort.sortDescNumber',
      };

    case FieldType.TEXT:
    case FieldType.LONGTEXT:
    case FieldType.EMAIL:
    case FieldType.URL:
    case FieldType.CREATEDBY:
    case FieldType.UPDATEDBY:
      return {
        asc: 'sort.sortAscText',
        desc: 'sort.sortDescText',
      };

    default:
      return {
        asc: 'sort.sortAsc',
        desc: 'sort.sortDesc',
      };
  }
};

export const getFieldFromViewColumnField = (viewColumnField: DecaTableViewColumnFields) => {
  const payload: FieldAPIUpdatePayload = {};
  if (viewColumnField.type) {
    set(payload, 'type', viewColumnField.type);
  }
  if (viewColumnField.header) {
    set(payload, 'name', viewColumnField.header);
  }
  if (viewColumnField['description']) {
    set(payload, 'description', viewColumnField['description']);
  }
  set(payload, 'options', viewColumnField.options || {});
  return payload;
};

export const normalizeOptionValues = (value: string | string[]) => {
  if (Array.isArray(value)) {
    return value.filter((v) => v !== '');
  }
  if (typeof value === 'string') {
    if (value.includes(',')) {
      return value
        .split(',')
        .map((v) => v.trim())
        .filter((v) => v !== '');
    } else {
      const trimmed = value.trim();
      return trimmed === '' ? [] : [trimmed];
    }
  }

  return [];
};
