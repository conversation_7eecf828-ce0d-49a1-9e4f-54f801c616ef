import { DEFAULT_LOGO_URL, JAPANESE_LOGO_URL } from '@/constants';
import { renderWithRouter } from '@/utils/test';
import { screen } from '@testing-library/react';
import { vi, describe, it, expect, beforeEach } from 'vitest';
import BaseLayout from './index';

// Mock dependencies
vi.mock('@/constants', () => ({
  DEFAULT_LOGO_URL: 'default-logo-url',
  JAPANESE_LOGO_URL: 'japanese-logo-url',
  HEADER_Z_INDEX: 1000,
  HEIGHT_OF_HEADER: 60,
}));

vi.mock('@mantine/core', () => ({
  AppShell: ({ children, header }: any) => (
    <div data-testid="app-shell" data-header-height={header?.height}>
      {children}
    </div>
  ),
  Flex: ({ children, className }: any) => (
    <div data-testid="flex" className={className}>
      {children}
    </div>
  ),
}));

vi.mock('@mantine/emotion', () => ({
  createStyles: () => () => ({
    appWrapper: 'app-wrapper-class',
    burgerCustomClass: 'burger-custom-class',
    headerCustomClass: 'header-custom-class',
  }),
}));

vi.mock('@resola-ai/ui/components', () => ({
  HeaderContainer: ({ 
    navigationMobile, 
    logoUrl, 
    jaLogoUrl, 
    burgerCustomClass, 
    className 
  }: any) => (
    <div 
      data-testid="header-container"
      data-navigation-mobile={navigationMobile ? 'present' : 'absent'}
      data-logo-url={logoUrl}
      data-ja-logo-url={jaLogoUrl}
      data-burger-class={burgerCustomClass}
      className={className}
    />
  ),
  LayoutStructure: ({ children }: any) => (
    <div data-testid="layout-structure">{children}</div>
  ),
}));

describe('BaseLayout', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('should render without crashing', () => {
    const { container } = renderWithRouter(
      <BaseLayout>
        <div data-testid="test-child">Test Child</div>
      </BaseLayout>
    );
    
    expect(container).toBeInTheDocument();
  });

  it('should render with children', () => {
    renderWithRouter(
      <BaseLayout>
        <div data-testid="test-child">Test Child</div>
      </BaseLayout>
    );
    
    expect(screen.getByTestId('test-child')).toBeInTheDocument();
    expect(screen.getByTestId('test-child')).toHaveTextContent('Test Child');
  });

  it('should render without navigationMobile prop', () => {
    renderWithRouter(
      <BaseLayout>
        <div data-testid="test-child">Test Child</div>
      </BaseLayout>
    );
    
    expect(screen.getByTestId('header-container')).toHaveAttribute('data-navigation-mobile', 'absent');
  });

  it('should render with navigationMobile prop', () => {
    const navigationMobile = <div data-testid="nav-mobile">Mobile Nav</div>;
    
    renderWithRouter(
      <BaseLayout navigationMobile={navigationMobile}>
        <div data-testid="test-child">Test Child</div>
      </BaseLayout>
    );
    
    expect(screen.getByTestId('header-container')).toHaveAttribute('data-navigation-mobile', 'present');
  });

  it('should render AppShell with correct header height', () => {
    renderWithRouter(
      <BaseLayout>
        <div data-testid="test-child">Test Child</div>
      </BaseLayout>
    );
    
    // Check that AppShell is rendered (it will have mantine-AppShell-root class)
    const appShell = document.querySelector('.mantine-AppShell-root');
    expect(appShell).toBeInTheDocument();
  });

  it('should render LayoutStructure', () => {
    renderWithRouter(
      <BaseLayout>
        <div data-testid="test-child">Test Child</div>
      </BaseLayout>
    );
    
    expect(screen.getByTestId('layout-structure')).toBeInTheDocument();
  });

  it('should render HeaderContainer with correct props', () => {
    renderWithRouter(
      <BaseLayout>
        <div data-testid="test-child">Test Child</div>
      </BaseLayout>
    );
    
    const headerContainer = screen.getByTestId('header-container');
    expect(headerContainer).toHaveAttribute('data-logo-url', DEFAULT_LOGO_URL);
    expect(headerContainer).toHaveAttribute('data-ja-logo-url', JAPANESE_LOGO_URL);
    expect(headerContainer).toHaveAttribute('data-burger-class');
    expect(headerContainer).toHaveClass('css-srb175');
  });

  it('should render Flex with correct className', () => {
    renderWithRouter(
      <BaseLayout>
        <div data-testid="test-child">Test Child</div>
      </BaseLayout>
    );
    
    // Check that Flex is rendered (it will have mantine-Flex-root class)
    const flex = document.querySelector('.mantine-Flex-root');
    expect(flex).toBeInTheDocument();
    expect(flex).toHaveClass('mantine-Flex-root');
  });

  it('should render children inside Flex component', () => {
    renderWithRouter(
      <BaseLayout>
        <div data-testid="test-child">Test Child</div>
      </BaseLayout>
    );
    
    const flex = document.querySelector('.mantine-Flex-root');
    const testChild = screen.getByTestId('test-child');
    
    expect(flex).toContainElement(testChild);
  });

  it('should have correct component hierarchy', () => {
    renderWithRouter(
      <BaseLayout>
        <div data-testid="test-child">Test Child</div>
      </BaseLayout>
    );
    
    // Verify the component hierarchy
    const appShell = document.querySelector('.mantine-AppShell-root');
    const layoutStructure = screen.getByTestId('layout-structure');
    const headerContainer = screen.getByTestId('header-container');
    const flex = document.querySelector('.mantine-Flex-root');
    const testChild = screen.getByTestId('test-child');
    
    expect(appShell).toContainElement(layoutStructure as HTMLElement);
    expect(layoutStructure).toContainElement(headerContainer as HTMLElement);
    expect(layoutStructure).toContainElement(flex as HTMLElement);
    expect(flex).toContainElement(testChild as HTMLElement);
  });

  it('should handle multiple children', () => {
    renderWithRouter(
      <BaseLayout>
        <div data-testid="child-1">Child 1</div>
        <div data-testid="child-2">Child 2</div>
        <div data-testid="child-3">Child 3</div>
      </BaseLayout>
    );
    
    expect(screen.getByTestId('child-1')).toBeInTheDocument();
    expect(screen.getByTestId('child-2')).toBeInTheDocument();
    expect(screen.getByTestId('child-3')).toBeInTheDocument();
  });

  it('should handle empty children', () => {
    renderWithRouter(<BaseLayout><div>Empty content</div></BaseLayout>);
    
    expect(document.querySelector('.mantine-AppShell-root')).toBeInTheDocument();
    expect(screen.getByTestId('layout-structure')).toBeInTheDocument();
    expect(screen.getByTestId('header-container')).toBeInTheDocument();
    expect(document.querySelector('.mantine-Flex-root')).toBeInTheDocument();
  });
});
