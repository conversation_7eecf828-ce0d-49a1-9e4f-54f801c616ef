import { mockLibraries, renderWithRouter } from '@/utils/test';
import { fireEvent, screen, waitFor } from '@testing-library/react';
import { vi, describe, it, expect, beforeEach, afterEach } from 'vitest';
import SearchBox from './index';

// Mock dependencies using vi.hoisted like ai-studio
const mocks = vi.hoisted(() => {
  const mockSetSearchParams = vi.fn();
  const mockSearchParams = new URLSearchParams();
  const mockDebounce = vi.fn((fn: (...args: any[]) => any) => {
    const debouncedFn = vi.fn(fn) as any;
    (debouncedFn as any).cancel = vi.fn();
    return debouncedFn;
  });

  return {
    mockSetSearchParams,
    mockSearchParams,
    mockDebounce,
  };
});

vi.mock('react-router-dom', async () => {
  const actual = await vi.importActual('react-router-dom');
  return {
    ...actual,
    useSearchParams: () => [mocks.mockSearchParams, mocks.mockSetSearchParams],
  };
});

vi.mock('lodash/debounce', () => ({
  default: mocks.mockDebounce,
}));

vi.mock('@tabler/icons-react', () => ({
  IconSearch: () => <div data-testid="search-icon">Search Icon</div>,
}));

mockLibraries();

describe('Common/SearchBox', () => {
  const defaultProps = {
    placeholder: 'Search...',
  };

  beforeEach(() => {
    vi.clearAllMocks();
    // Reset URLSearchParams by creating a new instance
    const newSearchParams = new URLSearchParams();
    Object.assign(mocks.mockSearchParams, newSearchParams);
    // Reset the get method to return null by default
    mocks.mockSearchParams.get = vi.fn().mockReturnValue(null);
  });

  afterEach(() => {
    vi.clearAllMocks();
  });

  describe('Rendering', () => {
    it('should render without crashing', () => {
      renderWithRouter(<SearchBox {...defaultProps} />);
      
      expect(screen.getByPlaceholderText('Search...')).toBeInTheDocument();
      // Check for the actual SVG icon that gets rendered
      const svgIcon = document.querySelector('svg.tabler-icon-search');
      expect(svgIcon).toBeInTheDocument();
    });

    it('should render with custom placeholder', () => {
      renderWithRouter(<SearchBox placeholder="Custom placeholder" />);
      
      expect(screen.getByPlaceholderText('Custom placeholder')).toBeInTheDocument();
    });

    it('should render with custom className', () => {
      const { container } = renderWithRouter(
        <SearchBox {...defaultProps} className="custom-class" />
      );
      
      const input = container.querySelector('.custom-class');
      expect(input).toBeInTheDocument();
    });

    it('should render with default value from search params', () => {
      // Mock the get method to return our test value
      mocks.mockSearchParams.get = vi.fn().mockReturnValue('test search');
      
      renderWithRouter(<SearchBox {...defaultProps} />);
      
      const input = screen.getByDisplayValue('test search');
      expect(input).toBeInTheDocument();
    });

    it('should render with empty value when no search params', () => {
      renderWithRouter(<SearchBox {...defaultProps} />);
      
      const input = screen.getByPlaceholderText('Search...');
      expect(input).toHaveValue('');
    });
  });

  describe('Search Functionality', () => {
    it('should handle input change', () => {
      renderWithRouter(<SearchBox {...defaultProps} />);
      
      const input = screen.getByPlaceholderText('Search...');
      fireEvent.change(input, { target: { value: 'test search' } });
      
      expect(input).toHaveValue('test search');
    });

    it('should handle empty input', () => {
      renderWithRouter(<SearchBox {...defaultProps} />);
      
      const input = screen.getByPlaceholderText('Search...');
      fireEvent.change(input, { target: { value: '' } });
      
      expect(input).toHaveValue('');
    });

    it('should call setSearchParams when input changes', async () => {
      renderWithRouter(<SearchBox {...defaultProps} />);
      
      const input = screen.getByPlaceholderText('Search...');
      fireEvent.change(input, { target: { value: 'new search' } });
      
      // Wait for debounced function to be called
      await waitFor(() => {
        expect(mocks.mockSetSearchParams).toHaveBeenCalled();
      });
    });

    it('should set search param when value is not empty', async () => {
      renderWithRouter(<SearchBox {...defaultProps} />);
      
      const input = screen.getByPlaceholderText('Search...');
      fireEvent.change(input, { target: { value: 'test search' } });
      
      await waitFor(() => {
        expect(mocks.mockSetSearchParams).toHaveBeenCalledWith(expect.any(Function));
      });
    });

    it('should delete search param when value is empty', async () => {
      // Set up initial search param
      mocks.mockSearchParams.get = vi.fn().mockReturnValue('existing search');
      
      renderWithRouter(<SearchBox {...defaultProps} />);
      
      const input = screen.getByPlaceholderText('Search...');
      fireEvent.change(input, { target: { value: '' } });
      
      await waitFor(() => {
        expect(mocks.mockSetSearchParams).toHaveBeenCalledWith(expect.any(Function));
      });
    });

    it('should reset to page 1 when searching', async () => {
      // Set up initial page param
      mocks.mockSearchParams.get = vi.fn().mockImplementation((key) => {
        if (key === 'page') return '5';
        return null;
      });
      
      renderWithRouter(<SearchBox {...defaultProps} />);
      
      const input = screen.getByPlaceholderText('Search...');
      fireEvent.change(input, { target: { value: 'test' } });
      
      await waitFor(() => {
        expect(mocks.mockSetSearchParams).toHaveBeenCalledWith(expect.any(Function));
      });
    });

    it('should not update params when value is the same', () => {
      // Set up initial search param
      mocks.mockSearchParams.get = vi.fn().mockReturnValue('existing search');
      
      renderWithRouter(<SearchBox {...defaultProps} />);
      
      const input = screen.getByPlaceholderText('Search...');
      fireEvent.change(input, { target: { value: 'existing search' } });
      
      // The input should still show the same value
      expect(input).toHaveValue('existing search');
      
      // The debounced function should be called but setSearchParams should not be called
      // because the internal logic prevents it when values are the same
      expect(mocks.mockSetSearchParams).not.toHaveBeenCalled();
    });

    it('should execute setSearchParams callback with correct URLSearchParams manipulation for new search', async () => {
      let capturedCallback: ((params: URLSearchParams) => URLSearchParams) | undefined = undefined;
      
      mocks.mockSetSearchParams.mockImplementation((callback: (params: URLSearchParams) => URLSearchParams) => {
        capturedCallback = callback;
        return new URLSearchParams();
      });
      
      renderWithRouter(<SearchBox {...defaultProps} />);
      
      const input = screen.getByPlaceholderText('Search...');
      fireEvent.change(input, { target: { value: 'new search' } });
      
      await waitFor(() => {
        expect(mocks.mockSetSearchParams).toHaveBeenCalled();
      });
      
      // Test the callback function directly
      if (capturedCallback) {
        const testParams = new URLSearchParams();
        testParams.set('page', '3');
        const result = (capturedCallback as any)(testParams);
        
        expect(result.get('q')).toBe('new search');
        expect(result.get('page')).toBe('1');
      }
    });

    it('should execute setSearchParams callback with correct URLSearchParams manipulation for empty search', async () => {
      let capturedCallback: ((params: URLSearchParams) => URLSearchParams) | undefined = undefined;
      
      mocks.mockSetSearchParams.mockImplementation((callback: (params: URLSearchParams) => URLSearchParams) => {
        capturedCallback = callback;
        return new URLSearchParams();
      });
      
      // Set up initial search param
      mocks.mockSearchParams.get = vi.fn().mockReturnValue('existing search');
      
      renderWithRouter(<SearchBox {...defaultProps} />);
      
      const input = screen.getByPlaceholderText('Search...');
      fireEvent.change(input, { target: { value: '' } });
      
      await waitFor(() => {
        expect(mocks.mockSetSearchParams).toHaveBeenCalled();
      });
      
      // Test the callback function directly
      if (capturedCallback) {
        const testParams = new URLSearchParams();
        testParams.set('q', 'existing search');
        testParams.set('page', '3');
        const result = (capturedCallback as any)(testParams);
        
        expect(result.get('q')).toBeNull();
        expect(result.get('page')).toBe('1');
      }
    });

    it('should not call setSearchParams when value is same as previous', async () => {
      // Set up initial search param
      mocks.mockSearchParams.get = vi.fn().mockReturnValue('existing search');
      
      renderWithRouter(<SearchBox {...defaultProps} />);
      
      const input = screen.getByPlaceholderText('Search...');
      fireEvent.change(input, { target: { value: 'existing search' } });
      
      // Wait a bit to ensure debounced function has time to execute
      await new Promise(resolve => setTimeout(resolve, 100));
      
      // Should not call setSearchParams because value is the same
      expect(mocks.mockSetSearchParams).not.toHaveBeenCalled();
    });
  });

  describe('Props Handling', () => {
    it('should pass through additional props to Input', () => {
      renderWithRouter(
        <SearchBox 
          {...defaultProps} 
          size="lg" 
          disabled={true}
          data-testid="custom-search"
        />
      );
      
      const input = screen.getByTestId('custom-search');
      expect(input).toBeInTheDocument();
      expect(input).toBeDisabled();
    });

    it('should handle all InputProps', () => {
      renderWithRouter(
        <SearchBox 
          {...defaultProps}
          size="sm"
          variant="filled"
          radius="xl"
        />
      );
      
      expect(screen.getByPlaceholderText('Search...')).toBeInTheDocument();
    });
  });

  describe('Component Structure', () => {
    it('should render with correct structure', () => {
      const { container } = renderWithRouter(<SearchBox {...defaultProps} />);
      
      const input = container.querySelector('input');
      expect(input).toBeInTheDocument();
      expect(input).toHaveAttribute('placeholder', 'Search...');
    });

    it('should render search icon', () => {
      renderWithRouter(<SearchBox {...defaultProps} />);
      
      // Check for the actual SVG icon that gets rendered
      const svgIcon = document.querySelector('svg.tabler-icon-search');
      expect(svgIcon).toBeInTheDocument();
    });

    it('should apply correct CSS classes', () => {
      const { container } = renderWithRouter(<SearchBox {...defaultProps} />);
      
      const inputWrapper = container.querySelector('.mantine-Input-wrapper');
      expect(inputWrapper).toBeInTheDocument();
    });
  });

  describe('Debouncing', () => {
    it('should create debounced function on mount', () => {
      renderWithRouter(<SearchBox {...defaultProps} />);
      
      expect(mocks.mockDebounce).toHaveBeenCalledWith(expect.any(Function), 500);
    });

    it('should cancel debounced function on unmount', () => {
      const mockDebouncedFn = vi.fn() as any;
      mockDebouncedFn.cancel = vi.fn();
      mocks.mockDebounce.mockReturnValue(mockDebouncedFn);
      
      const { unmount } = renderWithRouter(<SearchBox {...defaultProps} />);
      
      unmount();
      
      expect(mockDebouncedFn.cancel).toHaveBeenCalled();
    });

    it('should execute debounced function and update search params', () => {
      // Test that the debounced function is created and called
      renderWithRouter(<SearchBox {...defaultProps} />);
      
      const input = screen.getByPlaceholderText('Search...');
      fireEvent.change(input, { target: { value: 'test search' } });
      
      // The input should have the new value
      expect(input).toHaveValue('test search');
      
      // The debounce function should have been called during component mount
      expect(mocks.mockDebounce).toHaveBeenCalledWith(expect.any(Function), 500);
    });
  });

  describe('Edge Cases', () => {
    it('should handle very long search terms', () => {
      const longSearch = 'a'.repeat(1000);
      
      renderWithRouter(<SearchBox {...defaultProps} />);
      
      const input = screen.getByPlaceholderText('Search...');
      fireEvent.change(input, { target: { value: longSearch } });
      
      expect(input).toHaveValue(longSearch);
    });

    it('should handle special characters in search', () => {
      const specialSearch = 'test@#$%^&*()_+{}|:"<>?[]\\;\',./';
      
      renderWithRouter(<SearchBox {...defaultProps} />);
      
      const input = screen.getByPlaceholderText('Search...');
      fireEvent.change(input, { target: { value: specialSearch } });
      
      expect(input).toHaveValue(specialSearch);
    });

    it('should handle rapid input changes', () => {
      renderWithRouter(<SearchBox {...defaultProps} />);
      
      const input = screen.getByPlaceholderText('Search...');
      
      // Simulate rapid typing
      fireEvent.change(input, { target: { value: 'a' } });
      fireEvent.change(input, { target: { value: 'ab' } });
      fireEvent.change(input, { target: { value: 'abc' } });
      fireEvent.change(input, { target: { value: 'abcd' } });
      
      expect(input).toHaveValue('abcd');
    });
  });
});
