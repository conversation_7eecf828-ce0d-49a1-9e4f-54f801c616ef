import { mockLibraries, renderWithMantine } from '@/utils/test';
import { notifications } from '@mantine/notifications';
import { screen } from '@testing-library/react';
import { vi } from 'vitest';
import TableNotifications, { notifications as tableNotifications } from './index';

// Mock the mantine notifications
vi.mock('@mantine/notifications', () => ({
  notifications: {
    show: vi.fn(),
    hide: vi.fn(),
    update: vi.fn(),
    clean: vi.fn(),
    cleanQueue: vi.fn(),
  },
  Notifications: vi.fn(({ children, ...props }) => {
    return <div data-testid="notifications" {...props}>{children}</div>;
  }),
}));

mockLibraries();

describe('Common/TableNotifications', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe('TableNotifications Component', () => {
    it('should render without crashing', () => {
      renderWithMantine(<TableNotifications />);
    });

    it('should render with default withinPortal prop', () => {
      renderWithMantine(<TableNotifications />);
      // The component should render without errors
      expect(document.body).toBeInTheDocument();
    });

    it('should render with custom withinPortal prop', () => {
      renderWithMantine(<TableNotifications withinPortal={false} />);
      expect(document.body).toBeInTheDocument();
    });

    it('should pass through additional props', () => {
      renderWithMantine(<TableNotifications data-testid="custom-notifications" />);
      expect(screen.getByTestId('custom-notifications')).toBeInTheDocument();
    });
  });

  describe('showNotification function', () => {
    it('should show notification with default error status', () => {
      const props = {
        message: 'Test error message',
      };

      tableNotifications.show(props);

      expect(notifications.show).toHaveBeenCalledWith({
        message: 'Test error message',
        icon: expect.any(Object), // IconCircleXFilled
        loading: false,
        autoClose: true,
        withCloseButton: true,
        'data-status': 'error',
        style: {
          '--notification-offset-y': '0px',
          '--notification-offset-x': '0px',
        },
      });
    });

    it('should show notification with success status', () => {
      const props = {
        message: 'Test success message',
        status: 'success' as const,
      };

      tableNotifications.show(props);

      expect(notifications.show).toHaveBeenCalledWith({
        message: 'Test success message',
        icon: expect.any(Object), // IconCircleCheckFilled
        loading: false,
        autoClose: true,
        withCloseButton: true,
        'data-status': 'success',
        style: {
          '--notification-offset-y': '0px',
          '--notification-offset-x': '0px',
        },
      });
    });

    it('should show notification with loading status', () => {
      const props = {
        message: 'Test loading message',
        status: 'loading' as const,
      };

      tableNotifications.show(props);

      expect(notifications.show).toHaveBeenCalledWith({
        message: 'Test loading message',
        icon: undefined, // No icon for loading status
        loading: true,
        autoClose: false,
        withCloseButton: false,
        'data-status': 'loading',
        style: {
          '--notification-offset-y': '0px',
          '--notification-offset-x': '0px',
        },
      });
    });

    it('should show notification with custom status', () => {
      const props = {
        message: 'Test custom message',
        status: 'custom' as const,
      };

      tableNotifications.show(props);

      expect(notifications.show).toHaveBeenCalledWith({
        message: 'Test custom message',
        icon: undefined, // No icon for custom status
        loading: false,
        autoClose: true,
        withCloseButton: true,
        'data-status': 'custom',
        style: {
          '--notification-offset-y': '0px',
          '--notification-offset-x': '0px',
        },
      });
    });

    it('should show notification with custom offset values', () => {
      const props = {
        message: 'Test message with offset',
        offsetY: 100,
        offsetX: 200,
      };

      tableNotifications.show(props);

      expect(notifications.show).toHaveBeenCalledWith({
        message: 'Test message with offset',
        icon: expect.any(Object),
        loading: false,
        autoClose: true,
        withCloseButton: true,
        'data-status': 'error',
        style: {
          '--notification-offset-y': '100px',
          '--notification-offset-x': '200px',
        },
      });
    });

    it('should show notification with custom style', () => {
      const customStyle = { color: 'red', fontSize: '16px' };
      const props = {
        message: 'Test message with custom style',
        style: customStyle,
      };

      tableNotifications.show(props);

      expect(notifications.show).toHaveBeenCalledWith({
        message: 'Test message with custom style',
        icon: expect.any(Object),
        loading: false,
        autoClose: true,
        withCloseButton: true,
        'data-status': 'error',
        style: {
          '--notification-offset-y': '0px',
          '--notification-offset-x': '0px',
          ...customStyle,
        },
      });
    });

    it('should show notification with classNames', () => {
      const classNames = { root: 'custom-root', notification: 'custom-notification' };
      const props = {
        message: 'Test message with classNames',
        classNames,
      };

      tableNotifications.show(props);

      expect(notifications.show).toHaveBeenCalledWith({
        message: 'Test message with classNames',
        icon: expect.any(Object),
        loading: false,
        autoClose: true,
        withCloseButton: true,
        'data-status': 'error',
        style: {
          '--notification-offset-y': '0px',
          '--notification-offset-x': '0px',
        },
        classNames,
      });
    });

    it('should show notification with additional props', () => {
      const props = {
        message: 'Test message with additional props',
        id: 'test-id',
        title: 'Test Title',
        color: 'blue',
      };

      tableNotifications.show(props);

      expect(notifications.show).toHaveBeenCalledWith({
        message: 'Test message with additional props',
        icon: expect.any(Object),
        loading: false,
        autoClose: true,
        withCloseButton: true,
        'data-status': 'error',
        style: {
          '--notification-offset-y': '0px',
          '--notification-offset-x': '0px',
        },
        id: 'test-id',
        title: 'Test Title',
        color: 'blue',
      });
    });
  });

  describe('notifications object', () => {
    it('should have hide method that calls mantine notifications hide', () => {
      tableNotifications.hide('test-id');
      expect(notifications.hide).toHaveBeenCalledWith('test-id');
    });

    it('should have update method that calls mantine notifications update', () => {
      const updateData = { message: 'Updated message', id: 'test-id' } as any;
      tableNotifications.update(updateData);
      expect(notifications.update).toHaveBeenCalledWith(updateData);
    });

    it('should have clean method that calls mantine notifications clean', () => {
      tableNotifications.clean();
      expect(notifications.clean).toHaveBeenCalled();
    });

    it('should have cleanQueue method that calls mantine notifications cleanQueue', () => {
      tableNotifications.cleanQueue();
      expect(notifications.cleanQueue).toHaveBeenCalled();
    });
  });
});
