import { describe, it, expect } from 'vitest';
import { z } from 'zod';
import { duplicateFormSchema, createFormSchema, renameFormSchema } from './schema';

describe('TableForms Schema', () => {
  describe('duplicateFormSchema', () => {
    it('should validate a valid duplicate form with all fields', () => {
      const validData = {
        name: 'Duplicate Table',
        duplicateRecord: true,
      };

      const result = duplicateFormSchema.safeParse(validData);
      expect(result.success).toBe(true);
      if (result.success) {
        expect(result.data.name).toBe('Duplicate Table');
        expect(result.data.duplicateRecord).toBe(true);
      }
    });

    it('should validate with only required fields', () => {
      const validData = {
        name: 'Duplicate Table',
      };

      const result = duplicateFormSchema.safeParse(validData);
      expect(result.success).toBe(true);
      if (result.success) {
        expect(result.data.name).toBe('Duplicate Table');
        expect(result.data.duplicateRecord).toBeUndefined();
      }
    });

    it('should validate with duplicateRecord as false', () => {
      const validData = {
        name: 'Duplicate Table',
        duplicateRecord: false,
      };

      const result = duplicateFormSchema.safeParse(validData);
      expect(result.success).toBe(true);
      if (result.success) {
        expect(result.data.name).toBe('Duplicate Table');
        expect(result.data.duplicateRecord).toBe(false);
      }
    });

    it('should fail when name is missing', () => {
      const invalidData = {
        duplicateRecord: true,
      };

      const result = duplicateFormSchema.safeParse(invalidData);
      expect(result.success).toBe(false);
      if (!result.success) {
        expect(result.error.issues[0].path).toEqual(['name']);
        expect(result.error.issues[0].code).toBe(z.ZodIssueCode.invalid_type);
      }
    });

    it('should fail when name is an empty string', () => {
      const invalidData = {
        name: '',
        duplicateRecord: true,
      };

      const result = duplicateFormSchema.safeParse(invalidData);
      expect(result.success).toBe(false);
      if (!result.success) {
        expect(result.error.issues[0].path).toEqual(['name']);
        expect(result.error.issues[0].code).toBe(z.ZodIssueCode.too_small);
      }
    });

    it('should fail when name is not a string', () => {
      const invalidData = {
        name: 123,
        duplicateRecord: true,
      };

      const result = duplicateFormSchema.safeParse(invalidData);
      expect(result.success).toBe(false);
      if (!result.success) {
        expect(result.error.issues[0].path).toEqual(['name']);
        expect(result.error.issues[0].code).toBe(z.ZodIssueCode.invalid_type);
      }
    });

    it('should fail when duplicateRecord is not a boolean', () => {
      const invalidData = {
        name: 'Duplicate Table',
        duplicateRecord: 'true',
      };

      const result = duplicateFormSchema.safeParse(invalidData);
      expect(result.success).toBe(false);
      if (!result.success) {
        expect(result.error.issues[0].path).toEqual(['duplicateRecord']);
        expect(result.error.issues[0].code).toBe(z.ZodIssueCode.invalid_type);
      }
    });
  });

  describe('createFormSchema', () => {
    it('should validate a valid create form', () => {
      const validData = {
        name: 'New Table',
      };

      const result = createFormSchema.safeParse(validData);
      expect(result.success).toBe(true);
      if (result.success) {
        expect(result.data.name).toBe('New Table');
      }
    });

    it('should fail when name is missing', () => {
      const invalidData = {};

      const result = createFormSchema.safeParse(invalidData);
      expect(result.success).toBe(false);
      if (!result.success) {
        expect(result.error.issues[0].path).toEqual(['name']);
        expect(result.error.issues[0].code).toBe(z.ZodIssueCode.invalid_type);
      }
    });

    it('should fail when name is an empty string', () => {
      const invalidData = {
        name: '',
      };

      const result = createFormSchema.safeParse(invalidData);
      expect(result.success).toBe(false);
      if (!result.success) {
        expect(result.error.issues[0].path).toEqual(['name']);
        expect(result.error.issues[0].code).toBe(z.ZodIssueCode.too_small);
      }
    });

    it('should fail when name is not a string', () => {
      const invalidData = {
        name: 123,
      };

      const result = createFormSchema.safeParse(invalidData);
      expect(result.success).toBe(false);
      if (!result.success) {
        expect(result.error.issues[0].path).toEqual(['name']);
        expect(result.error.issues[0].code).toBe(z.ZodIssueCode.invalid_type);
      }
    });

    it('should handle extra properties by ignoring them', () => {
      const validData = {
        name: 'New Table',
        extraProperty: 'should-be-ignored',
      };

      const result = createFormSchema.safeParse(validData);
      expect(result.success).toBe(true);
      if (result.success) {
        expect(result.data.name).toBe('New Table');
        expect((result.data as any).extraProperty).toBeUndefined();
      }
    });
  });

  describe('renameFormSchema', () => {
    it('should validate a valid rename form', () => {
      const validData = {
        name: 'Renamed Table',
      };

      const result = renameFormSchema.safeParse(validData);
      expect(result.success).toBe(true);
      if (result.success) {
        expect(result.data.name).toBe('Renamed Table');
      }
    });

    it('should fail when name is missing', () => {
      const invalidData = {};

      const result = renameFormSchema.safeParse(invalidData);
      expect(result.success).toBe(false);
      if (!result.success) {
        expect(result.error.issues[0].path).toEqual(['name']);
        expect(result.error.issues[0].code).toBe(z.ZodIssueCode.invalid_type);
      }
    });

    it('should fail when name is an empty string', () => {
      const invalidData = {
        name: '',
      };

      const result = renameFormSchema.safeParse(invalidData);
      expect(result.success).toBe(false);
      if (!result.success) {
        expect(result.error.issues[0].path).toEqual(['name']);
        expect(result.error.issues[0].code).toBe(z.ZodIssueCode.too_small);
      }
    });

    it('should fail when name is not a string', () => {
      const invalidData = {
        name: 123,
      };

      const result = renameFormSchema.safeParse(invalidData);
      expect(result.success).toBe(false);
      if (!result.success) {
        expect(result.error.issues[0].path).toEqual(['name']);
        expect(result.error.issues[0].code).toBe(z.ZodIssueCode.invalid_type);
      }
    });

    it('should handle extra properties by ignoring them', () => {
      const validData = {
        name: 'Renamed Table',
        extraProperty: 'should-be-ignored',
      };

      const result = renameFormSchema.safeParse(validData);
      expect(result.success).toBe(true);
      if (result.success) {
        expect(result.data.name).toBe('Renamed Table');
        expect((result.data as any).extraProperty).toBeUndefined();
      }
    });
  });

  describe('Edge cases and boundary testing', () => {
    it('should validate with minimum length name (1 character)', () => {
      const validData = {
        name: 'A',
      };

      const result = createFormSchema.safeParse(validData);
      expect(result.success).toBe(true);
      if (result.success) {
        expect(result.data.name).toBe('A');
      }
    });

    it('should validate with very long name', () => {
      const longName = 'A'.repeat(1000);
      const validData = {
        name: longName,
      };

      const result = createFormSchema.safeParse(validData);
      expect(result.success).toBe(true);
      if (result.success) {
        expect(result.data.name).toBe(longName);
      }
    });

    it('should validate with special characters in name', () => {
      const validData = {
        name: 'Table-Name_With.Special+Characters!@#$%^&*()',
      };

      const result = createFormSchema.safeParse(validData);
      expect(result.success).toBe(true);
      if (result.success) {
        expect(result.data.name).toBe('Table-Name_With.Special+Characters!@#$%^&*()');
      }
    });

    it('should validate with unicode characters in name', () => {
      const validData = {
        name: 'Táblé-ñámé-中文-العربية',
      };

      const result = createFormSchema.safeParse(validData);
      expect(result.success).toBe(true);
      if (result.success) {
        expect(result.data.name).toBe('Táblé-ñámé-中文-العربية');
      }
    });
  });

  describe('Type inference', () => {
    it('should correctly infer duplicateFormSchema type', () => {
      const data: z.infer<typeof duplicateFormSchema> = {
        name: 'Test Table',
        duplicateRecord: true,
      };

      expect(data.name).toBe('Test Table');
      expect(data.duplicateRecord).toBe(true);
    });

    it('should correctly infer createFormSchema type', () => {
      const data: z.infer<typeof createFormSchema> = {
        name: 'Test Table',
      };

      expect(data.name).toBe('Test Table');
    });

    it('should correctly infer renameFormSchema type', () => {
      const data: z.infer<typeof renameFormSchema> = {
        name: 'Test Table',
      };

      expect(data.name).toBe('Test Table');
    });
  });
});