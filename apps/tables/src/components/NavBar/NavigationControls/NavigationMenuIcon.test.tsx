import { renderWithRouter } from '@/utils/test';
import { screen, fireEvent, waitFor } from '@testing-library/react';
import { vi, describe, it, expect, beforeEach, afterEach } from 'vitest';
import NavigationMenuIcon, { MenuAction } from './NavigationMenuIcon';
import { Table } from '@/types';

// Mock dependencies using vi.hoisted
const mocks = vi.hoisted(() => {
  const mockOpenTableTrashViewModal = vi.fn();
  const mockAddPendingClearTableId = vi.fn();
  const mockDuplicateTable = vi.fn();
  const mockRemoveTable = vi.fn();
  const mockUpdateTable = vi.fn();
  const mockClearRecords = vi.fn();
  const mockRevalidateDeletedTable = vi.fn();
  const mockNotificationsShow = vi.fn();
  const mockUseTranslate = vi.fn();

  return {
    mockOpenTableTrashViewModal,
    mockAddPendingClearTableId,
    mockDuplicateTable,
    mockRemoveTable,
    mockUpdateTable,
    mockClearRecords,
    mockRevalidateDeletedTable,
    mockNotificationsShow,
    mockUseTranslate,
  };
});

// Mock contexts
vi.mock('@/contexts/AppContext', () => ({
  useAppContext: () => ({
    openTableTrashViewModal: mocks.mockOpenTableTrashViewModal,
    addPendingClearTableId: mocks.mockAddPendingClearTableId,
  }),
}));

// Mock hooks
vi.mock('@/hooks', () => ({
  useTablesMutation: () => ({
    duplicateTable: mocks.mockDuplicateTable,
    removeTable: mocks.mockRemoveTable,
    updateTable: mocks.mockUpdateTable,
  }),
  useTableRecordsMutation: () => ({
    clearRecords: mocks.mockClearRecords,
  }),
  useDeletedTablesMutation: () => ({
    revalidateQuery: mocks.mockRevalidateDeletedTable,
  }),
}));

// Mock components
vi.mock('@/components/Common', () => ({
  ConfirmForm: ({ onConfirm, onCancel, title, content, confirmText, cancelText, warningText }: any) => (
    <div data-testid="confirm-form">
      <div data-testid="confirm-title">{title}</div>
      <div data-testid="confirm-content">{content}</div>
      <div data-testid="confirm-warning">{warningText}</div>
      <button data-testid="confirm-button" onClick={onConfirm}>
        {confirmText}
      </button>
      <button data-testid="cancel-button" onClick={onCancel}>
        {cancelText}
      </button>
    </div>
  ),
  DuplicateModal: ({ opened, onSubmit, onCancel, title, inputLabel, cancelText, submitText, checkboxLabel }: any) => 
    opened ? (
      <div data-testid="duplicate-modal">
        <div data-testid="duplicate-title">{title}</div>
        <div data-testid="duplicate-input-label">{inputLabel}</div>
        <div data-testid="duplicate-checkbox-label">{checkboxLabel}</div>
        <button 
          data-testid="duplicate-submit" 
          onClick={() => onSubmit({ formData: { name: 'Test Table Duplicate', duplicateRecord: true } })}
        >
          {submitText}
        </button>
        <button data-testid="duplicate-cancel" onClick={onCancel}>
          {cancelText}
        </button>
      </div>
    ) : null,
  RenameForm: ({ onSubmit, onCancel, inputLabel, cancelText, submitText, defaultValues }: any) => (
    <div data-testid="rename-form">
      <div data-testid="rename-input-label">{inputLabel}</div>
      <div data-testid="rename-default-value">{defaultValues?.name}</div>
      <button 
        data-testid="rename-submit" 
        onClick={() => onSubmit({ formData: { name: 'Renamed Table' } })}
      >
        {submitText}
      </button>
      <button data-testid="rename-cancel" onClick={onCancel}>
        {cancelText}
      </button>
    </div>
  ),
  notifications: {
    show: mocks.mockNotificationsShow,
  },
}));

// Mock Tolgee
vi.mock('@tolgee/react', () => ({
  useTranslate: mocks.mockUseTranslate,
}));

// Mock Mantine components - use actual components but add test attributes
vi.mock('@mantine/core', async () => {
  const actual = await vi.importActual('@mantine/core');
  return {
    ...actual,
    ActionIcon: ({ children, onClick, ...props }: any) => (
      <button data-testid="action-icon" onClick={onClick} {...props}>
        {children}
      </button>
    ),
    Menu: {
      ...(actual.Menu as any),
      Target: ({ children }: any) => <div data-testid="menu-target">{children}</div>,
      Dropdown: ({ children, ...props }: any) => <div data-testid="menu-dropdown" {...props}>{children}</div>,
      Item: ({ children, onClick, leftSection, className }: any) => (
        <div data-testid="menu-item" onClick={onClick} className={className}>
          {leftSection}
          {children}
        </div>
      ),
      Divider: () => <div data-testid="menu-divider" />,
    },
  };
});

// Mock Mantine emotion
vi.mock('@mantine/emotion', () => ({
  createStyles: () => () => ({
    classes: {
      menuDropdown: 'menu-dropdown',
      menuItem: 'menu-item',
      itemLabel: 'item-label',
      itemIcon: 'item-icon',
      menuDivider: 'menu-divider',
      menuArrow: 'menu-arrow',
      goToTrashLabel: 'go-to-trash-label',
    },
  }),
}));

// Mock Tabler icons
vi.mock('@tabler/icons-react', () => ({
  IconDotsVertical: () => <div data-testid="icon-dots-vertical" />,
  IconEdit: () => <div data-testid="icon-edit" />,
  IconCopy: () => <div data-testid="icon-copy" />,
  IconX: () => <div data-testid="icon-x" />,
  IconTrash: () => <div data-testid="icon-trash" />,
}));

const mockTable: Table = {
  id: 'table-1',
  name: 'Test Table',
  baseId: 'base-1',
  description: 'Test table description',
  meta: {},
  fields: [],
  data: {},
  createdAt: '2023-01-01T00:00:00Z',
  updatedAt: '2023-01-01T00:00:00Z',
  deletedAt: '',
};

describe('NavigationMenuIcon', () => {
  const mockOnChange = vi.fn();
  const mockOnOpen = vi.fn();
  const mockOnClose = vi.fn();
  const mockOnPositionChange = vi.fn();

  beforeEach(() => {
    vi.clearAllMocks();
    mocks.mockUseTranslate.mockReturnValue({
      t: (key: string) => key,
    });
  });

  afterEach(() => {
    vi.clearAllMocks();
  });

  describe('Rendering', () => {
    it('should render without crashing', () => {
      const { container } = renderWithRouter(
        <NavigationMenuIcon
          table={mockTable}
          onChange={mockOnChange}
          onOpen={mockOnOpen}
          onClose={mockOnClose}
          onPositionChange={mockOnPositionChange}
        />
      );
      expect(container).toBeInTheDocument();
    });

    it('should render with default icon', () => {
      renderWithRouter(
        <NavigationMenuIcon
          table={mockTable}
          onChange={mockOnChange}
          onOpen={mockOnOpen}
          onClose={mockOnClose}
          onPositionChange={mockOnPositionChange}
        />
      );
      expect(screen.getByRole('button')).toBeInTheDocument();
      // Check for the actual Tabler icon class instead of test id
      expect(document.querySelector('.tabler-icon-dots-vertical')).toBeInTheDocument();
    });

    it('should render with custom icon', () => {
      const customIcon = <div data-testid="custom-icon">Custom</div>;
      renderWithRouter(
        <NavigationMenuIcon
          icon={customIcon}
          table={mockTable}
          onChange={mockOnChange}
          onOpen={mockOnOpen}
          onClose={mockOnClose}
          onPositionChange={mockOnPositionChange}
        />
      );
      expect(screen.getByTestId('custom-icon')).toBeInTheDocument();
    });

    it('should render menu when opened', () => {
      renderWithRouter(
        <NavigationMenuIcon
          table={mockTable}
          onChange={mockOnChange}
          onOpen={mockOnOpen}
          onClose={mockOnClose}
          onPositionChange={mockOnPositionChange}
          opened={true}
        />
      );
      expect(screen.getByRole('menu')).toBeInTheDocument();
    });
  });

  describe('Menu Items', () => {
    it('should render all menu items when no action is selected', () => {
      renderWithRouter(
        <NavigationMenuIcon
          table={mockTable}
          onChange={mockOnChange}
          onOpen={mockOnOpen}
          onClose={mockOnClose}
          onPositionChange={mockOnPositionChange}
          opened={true}
        />
      );
      
      expect(screen.getByText('navigation.tableMenu.rename')).toBeInTheDocument();
      expect(screen.getByText('navigation.tableMenu.duplicate')).toBeInTheDocument();
      expect(screen.getByText('navigation.tableMenu.clearData')).toBeInTheDocument();
      expect(screen.getByText('navigation.tableMenu.deleteTable')).toBeInTheDocument();
      // Check for divider by looking for the actual Mantine divider class
      expect(document.querySelector('.mantine-Menu-divider')).toBeInTheDocument();
    });

    it('should show rename form when rename is clicked', () => {
      renderWithRouter(
        <NavigationMenuIcon
          table={mockTable}
          onChange={mockOnChange}
          onOpen={mockOnOpen}
          onClose={mockOnClose}
          onPositionChange={mockOnPositionChange}
          opened={true}
        />
      );
      
      fireEvent.click(screen.getByText('navigation.tableMenu.rename'));
      expect(screen.getByTestId('rename-form')).toBeInTheDocument();
      expect(screen.getByTestId('rename-default-value')).toHaveTextContent('Test Table');
    });

    it('should show duplicate modal when duplicate is clicked', () => {
      renderWithRouter(
        <NavigationMenuIcon
          table={mockTable}
          onChange={mockOnChange}
          onOpen={mockOnOpen}
          onClose={mockOnClose}
          onPositionChange={mockOnPositionChange}
          opened={true}
        />
      );
      
      fireEvent.click(screen.getByText('navigation.tableMenu.duplicate'));
      expect(screen.getByTestId('duplicate-modal')).toBeInTheDocument();
    });

    it('should show clear data confirm form when clear data is clicked', () => {
      renderWithRouter(
        <NavigationMenuIcon
          table={mockTable}
          onChange={mockOnChange}
          onOpen={mockOnOpen}
          onClose={mockOnClose}
          onPositionChange={mockOnPositionChange}
          opened={true}
        />
      );
      
      fireEvent.click(screen.getByText('navigation.tableMenu.clearData'));
      expect(screen.getByTestId('confirm-form')).toBeInTheDocument();
      expect(screen.getByTestId('confirm-title')).toHaveTextContent('navigation.clearData.title');
    });

    it('should show delete table confirm form when delete table is clicked', () => {
      renderWithRouter(
        <NavigationMenuIcon
          table={mockTable}
          onChange={mockOnChange}
          onOpen={mockOnOpen}
          onClose={mockOnClose}
          onPositionChange={mockOnPositionChange}
          opened={true}
        />
      );
      
      fireEvent.click(screen.getByText('navigation.tableMenu.deleteTable'));
      expect(screen.getByTestId('confirm-form')).toBeInTheDocument();
      expect(screen.getByTestId('confirm-title')).toHaveTextContent('navigation.deleteTable.title');
    });
  });

  describe('Menu Interactions', () => {
    it('should call onOpen when action icon is clicked', () => {
      renderWithRouter(
        <NavigationMenuIcon
          table={mockTable}
          onChange={mockOnChange}
          onOpen={mockOnOpen}
          onClose={mockOnClose}
          onPositionChange={mockOnPositionChange}
        />
      );
      
      fireEvent.click(screen.getByRole('button'));
      expect(mockOnOpen).toHaveBeenCalledTimes(1);
    });

    it('should handle menu change and close when not duplicate action', () => {
      renderWithRouter(
        <NavigationMenuIcon
          table={mockTable}
          onChange={mockOnChange}
          onOpen={mockOnOpen}
          onClose={mockOnClose}
          onPositionChange={mockOnPositionChange}
          opened={true}
        />
      );
      
      // Click rename to set action
      fireEvent.click(screen.getByText('navigation.tableMenu.rename'));
      expect(screen.getByTestId('rename-form')).toBeInTheDocument();
      
      // Simulate menu change by clicking the main action button (the first one)
      const buttons = screen.getAllByRole('button');
      fireEvent.click(buttons[0]);
      expect(mockOnClose).toHaveBeenCalledTimes(1);
    });

    it('should keep menu open for duplicate action', () => {
      renderWithRouter(
        <NavigationMenuIcon
          table={mockTable}
          onChange={mockOnChange}
          onOpen={mockOnOpen}
          onClose={mockOnClose}
          onPositionChange={mockOnPositionChange}
          opened={true}
        />
      );
      
      // Click duplicate to set action
      fireEvent.click(screen.getByText('navigation.tableMenu.duplicate'));
      expect(screen.getByTestId('duplicate-modal')).toBeInTheDocument();
      
      // Simulate menu change by clicking the main action button - should not close
      const buttons = screen.getAllByRole('button');
      fireEvent.click(buttons[0]);
      expect(mockOnClose).not.toHaveBeenCalled();
    });

    it('should reset action when menu is exited', () => {
      renderWithRouter(
        <NavigationMenuIcon
          table={mockTable}
          onChange={mockOnChange}
          onOpen={mockOnOpen}
          onClose={mockOnClose}
          onPositionChange={mockOnPositionChange}
          opened={true}
        />
      );
      
      // Click rename to set action
      fireEvent.click(screen.getByText('navigation.tableMenu.rename'));
      expect(screen.getByTestId('rename-form')).toBeInTheDocument();
      
      // Test that resetAction is called when cancel is clicked
      fireEvent.click(screen.getByTestId('rename-cancel'));
      // The action should be reset (form should disappear)
      expect(screen.queryByTestId('rename-form')).not.toBeInTheDocument();
    });
  });

  describe('Position Handling', () => {
    it('should handle position change', () => {
      const position = { x: 100, y: 200 };
      renderWithRouter(
        <NavigationMenuIcon
          table={mockTable}
          onChange={mockOnChange}
          onOpen={mockOnOpen}
          onClose={mockOnClose}
          onPositionChange={mockOnPositionChange}
          position={position}
          opened={true}
        />
      );
      
      const menu = screen.getByRole('menu');
      expect(menu).toHaveStyle({
        left: '116px', // 100 + 16
        top: '200px',
      });
    });

    it('should handle undefined position', () => {
      renderWithRouter(
        <NavigationMenuIcon
          table={mockTable}
          onChange={mockOnChange}
          onOpen={mockOnOpen}
          onClose={mockOnClose}
          onPositionChange={mockOnPositionChange}
          position={undefined}
          opened={true}
        />
      );
      
      const menu = screen.getByRole('menu');
      expect(menu).toBeInTheDocument();
    });
  });

  describe('Form Submissions', () => {
    it('should handle successful duplicate table submission', async () => {
      const mockDuplicateResponse = {
        isOk: () => true,
        value: { data: { id: 'duplicate-table-id', name: 'Test Table Duplicate' } }
      };
      mocks.mockDuplicateTable.mockResolvedValue(mockDuplicateResponse);

      renderWithRouter(
        <NavigationMenuIcon
          table={mockTable}
          onChange={mockOnChange}
          onOpen={mockOnOpen}
          onClose={mockOnClose}
          onPositionChange={mockOnPositionChange}
          opened={true}
        />
      );
      
      // Click duplicate to show modal
      fireEvent.click(screen.getByText('navigation.tableMenu.duplicate'));
      expect(screen.getByTestId('duplicate-modal')).toBeInTheDocument();
      
      // Submit duplicate form
      fireEvent.click(screen.getByTestId('duplicate-submit'));
      
      await waitFor(() => {
        expect(mocks.mockDuplicateTable).toHaveBeenCalledWith('base-1', 'table-1', {
          name: 'Test Table Duplicate',
          fields: mockTable.fields,
          duplicateRecord: true,
        });
        expect(mockOnChange).toHaveBeenCalledWith(MenuAction.DUPLICATE_TABLE, mockDuplicateResponse.value.data);
        expect(mocks.mockNotificationsShow).toHaveBeenCalledWith({
          message: 'success.table.duplicate',
          status: 'success',
        });
        expect(mockOnClose).toHaveBeenCalled();
      });
    });

    it('should handle failed duplicate table submission', async () => {
      const mockDuplicateResponse = {
        isOk: () => false,
        error: 'Duplicate failed'
      };
      mocks.mockDuplicateTable.mockResolvedValue(mockDuplicateResponse);

      renderWithRouter(
        <NavigationMenuIcon
          table={mockTable}
          onChange={mockOnChange}
          onOpen={mockOnOpen}
          onClose={mockOnClose}
          onPositionChange={mockOnPositionChange}
          opened={true}
        />
      );
      
      // Click duplicate to show modal
      fireEvent.click(screen.getByText('navigation.tableMenu.duplicate'));
      expect(screen.getByTestId('duplicate-modal')).toBeInTheDocument();
      
      // Submit duplicate form
      fireEvent.click(screen.getByTestId('duplicate-submit'));
      
      await waitFor(() => {
        expect(mocks.mockDuplicateTable).toHaveBeenCalled();
        expect(mockOnChange).not.toHaveBeenCalled();
        expect(mocks.mockNotificationsShow).not.toHaveBeenCalled();
        expect(mockOnClose).not.toHaveBeenCalled();
      });
    });

    it('should handle successful rename table submission', async () => {
      const mockUpdateResponse = {
        isOk: () => true,
        value: { data: { id: 'table-1', name: 'Renamed Table' } }
      };
      mocks.mockUpdateTable.mockResolvedValue(mockUpdateResponse);

      renderWithRouter(
        <NavigationMenuIcon
          table={mockTable}
          onChange={mockOnChange}
          onOpen={mockOnOpen}
          onClose={mockOnClose}
          onPositionChange={mockOnPositionChange}
          opened={true}
        />
      );
      
      // Click rename to show form
      fireEvent.click(screen.getByText('navigation.tableMenu.rename'));
      expect(screen.getByTestId('rename-form')).toBeInTheDocument();
      
      // Submit rename form
      fireEvent.click(screen.getByTestId('rename-submit'));
      
      await waitFor(() => {
        expect(mocks.mockUpdateTable).toHaveBeenCalledWith('base-1', 'table-1', {
          name: 'Renamed Table',
          fields: mockTable.fields,
        });
        expect(mockOnChange).toHaveBeenCalledWith(MenuAction.RENAME_TABLE, mockTable);
        expect(mocks.mockNotificationsShow).toHaveBeenCalledWith({
          message: 'success.table.rename',
          status: 'success',
        });
        expect(mockOnClose).toHaveBeenCalled();
      });
    });

    it('should handle failed rename table submission', async () => {
      const mockUpdateResponse = {
        isOk: () => false,
        error: 'Rename failed'
      };
      mocks.mockUpdateTable.mockResolvedValue(mockUpdateResponse);

      renderWithRouter(
        <NavigationMenuIcon
          table={mockTable}
          onChange={mockOnChange}
          onOpen={mockOnOpen}
          onClose={mockOnClose}
          onPositionChange={mockOnPositionChange}
          opened={true}
        />
      );
      
      // Click rename to show form
      fireEvent.click(screen.getByText('navigation.tableMenu.rename'));
      expect(screen.getByTestId('rename-form')).toBeInTheDocument();
      
      // Submit rename form
      fireEvent.click(screen.getByTestId('rename-submit'));
      
      await waitFor(() => {
        expect(mocks.mockUpdateTable).toHaveBeenCalled();
        expect(mockOnChange).not.toHaveBeenCalled();
        expect(mocks.mockNotificationsShow).not.toHaveBeenCalled();
        expect(mockOnClose).not.toHaveBeenCalled();
      });
    });

    it('should handle successful delete table submission', async () => {
      const mockRemoveResponse = {
        isOk: () => true,
        value: { data: { id: 'table-1' } }
      };
      mocks.mockRemoveTable.mockResolvedValue(mockRemoveResponse);

      renderWithRouter(
        <NavigationMenuIcon
          table={mockTable}
          onChange={mockOnChange}
          onOpen={mockOnOpen}
          onClose={mockOnClose}
          onPositionChange={mockOnPositionChange}
          opened={true}
        />
      );
      
      // Click delete to show confirm form
      fireEvent.click(screen.getByText('navigation.tableMenu.deleteTable'));
      expect(screen.getByTestId('confirm-form')).toBeInTheDocument();
      
      // Submit delete form
      fireEvent.click(screen.getByTestId('confirm-button'));
      
      await waitFor(() => {
        expect(mocks.mockRemoveTable).toHaveBeenCalledWith('base-1', 'table-1');
        expect(mockOnChange).toHaveBeenCalledWith(MenuAction.DELETE_TABLE, mockTable);
        expect(mocks.mockRevalidateDeletedTable).toHaveBeenCalledWith('base-1');
        expect(mockOnClose).toHaveBeenCalled();
      });
    });

    it('should handle failed delete table submission', async () => {
      const mockRemoveResponse = {
        isOk: () => false,
        error: 'Delete failed'
      };
      mocks.mockRemoveTable.mockResolvedValue(mockRemoveResponse);

      renderWithRouter(
        <NavigationMenuIcon
          table={mockTable}
          onChange={mockOnChange}
          onOpen={mockOnOpen}
          onClose={mockOnClose}
          onPositionChange={mockOnPositionChange}
          opened={true}
        />
      );
      
      // Click delete to show confirm form
      fireEvent.click(screen.getByText('navigation.tableMenu.deleteTable'));
      expect(screen.getByTestId('confirm-form')).toBeInTheDocument();
      
      // Submit delete form
      fireEvent.click(screen.getByTestId('confirm-button'));
      
      await waitFor(() => {
        expect(mocks.mockRemoveTable).toHaveBeenCalled();
        expect(mockOnChange).not.toHaveBeenCalled();
        expect(mocks.mockRevalidateDeletedTable).not.toHaveBeenCalled();
        expect(mockOnClose).not.toHaveBeenCalled();
      });
    });

    it('should handle clear data action', async () => {
      renderWithRouter(
        <NavigationMenuIcon
          table={mockTable}
          onChange={mockOnChange}
          onOpen={mockOnOpen}
          onClose={mockOnClose}
          onPositionChange={mockOnPositionChange}
          opened={true}
        />
      );
      
      // Click clear data to show confirm form
      fireEvent.click(screen.getByText('navigation.tableMenu.clearData'));
      expect(screen.getByTestId('confirm-form')).toBeInTheDocument();
      
      // Submit clear data form
      fireEvent.click(screen.getByTestId('confirm-button'));
      
      await waitFor(() => {
        expect(mocks.mockAddPendingClearTableId).toHaveBeenCalledWith('table-1');
        expect(mockOnChange).toHaveBeenCalledWith(MenuAction.CLEAR_DATA, mockTable);
        expect(mockOnClose).toHaveBeenCalled();
        expect(mocks.mockClearRecords).toHaveBeenCalledWith('base-1', 'table-1');
      });
    });
  });

  describe('Notification Callbacks', () => {
    it('should handle go to trash click in delete notification', async () => {
      const mockRemoveResponse = {
        isOk: () => true,
        value: { data: { id: 'table-1' } }
      };
      mocks.mockRemoveTable.mockResolvedValue(mockRemoveResponse);

      renderWithRouter(
        <NavigationMenuIcon
          table={mockTable}
          onChange={mockOnChange}
          onOpen={mockOnOpen}
          onClose={mockOnClose}
          onPositionChange={mockOnPositionChange}
          opened={true}
        />
      );
      
      // Click delete to show confirm form
      fireEvent.click(screen.getByText('navigation.tableMenu.deleteTable'));
      expect(screen.getByTestId('confirm-form')).toBeInTheDocument();
      
      // Submit delete form
      fireEvent.click(screen.getByTestId('confirm-button'));
      
      await waitFor(() => {
        expect(mocks.mockNotificationsShow).toHaveBeenCalledWith({
          message: expect.any(Object),
          status: 'success',
        });
        
        // Get the notification message element and click the go to trash link
        const notificationCall = mocks.mockNotificationsShow.mock.calls[0][0];
        const messageElement = notificationCall.message;
        expect(messageElement).toBeDefined();
      });
    });
  });

  describe('Menu Arrow Display', () => {
    it('should show arrow when action is rename table', () => {
      renderWithRouter(
        <NavigationMenuIcon
          table={mockTable}
          onChange={mockOnChange}
          onOpen={mockOnOpen}
          onClose={mockOnClose}
          onPositionChange={mockOnPositionChange}
          opened={true}
        />
      );
      
      // Click rename to set action
      fireEvent.click(screen.getByText('navigation.tableMenu.rename'));
      expect(screen.getByTestId('rename-form')).toBeInTheDocument();
      
      // The arrow should be shown (this is tested by the component's internal logic)
      // We can verify by checking that the rename form is rendered
      expect(screen.getByTestId('rename-form')).toBeInTheDocument();
    });

    it('should not show arrow when action is not rename table', () => {
      renderWithRouter(
        <NavigationMenuIcon
          table={mockTable}
          onChange={mockOnChange}
          onOpen={mockOnOpen}
          onClose={mockOnClose}
          onPositionChange={mockOnPositionChange}
          opened={true}
        />
      );
      
      // Click duplicate to set action
      fireEvent.click(screen.getByText('navigation.tableMenu.duplicate'));
      expect(screen.getByTestId('duplicate-modal')).toBeInTheDocument();
      
      // The arrow should not be shown for duplicate action
      // This is tested by the component's internal logic
    });
  });

  describe('Default Props', () => {
    it('should use default props when not provided', () => {
      renderWithRouter(
        <NavigationMenuIcon
          table={mockTable}
        />
      );
      
      expect(screen.getByRole('button')).toBeInTheDocument();
      // Check for the actual Tabler icon class instead of test id
      expect(document.querySelector('.tabler-icon-dots-vertical')).toBeInTheDocument();
    });
  });

  describe('Edge Cases', () => {
    it('should handle table without fields', () => {
      const tableWithoutFields = { ...mockTable, fields: undefined };
      
      renderWithRouter(
        <NavigationMenuIcon
          table={tableWithoutFields}
          onChange={mockOnChange}
          onOpen={mockOnOpen}
          onClose={mockOnClose}
          onPositionChange={mockOnPositionChange}
          opened={true}
        />
      );
      
      // Click duplicate to test form submission with undefined fields
      fireEvent.click(screen.getByText('navigation.tableMenu.duplicate'));
      expect(screen.getByTestId('duplicate-modal')).toBeInTheDocument();
      
      // Submit duplicate form
      fireEvent.click(screen.getByTestId('duplicate-submit'));
      
      // Should handle undefined fields gracefully
      expect(mocks.mockDuplicateTable).toHaveBeenCalledWith('base-1', 'table-1', {
        name: 'Test Table Duplicate',
        fields: [],
        duplicateRecord: true,
      });
    });

    it('should handle missing onChange callback', () => {
      renderWithRouter(
        <NavigationMenuIcon
          table={mockTable}
          onOpen={mockOnOpen}
          onClose={mockOnClose}
          onPositionChange={mockOnPositionChange}
          opened={true}
        />
      );
      
      // Click duplicate to show modal
      fireEvent.click(screen.getByText('navigation.tableMenu.duplicate'));
      expect(screen.getByTestId('duplicate-modal')).toBeInTheDocument();
      
      // Should not crash when onChange is undefined
      expect(() => {
        fireEvent.click(screen.getByTestId('duplicate-submit'));
      }).not.toThrow();
    });
  });
});