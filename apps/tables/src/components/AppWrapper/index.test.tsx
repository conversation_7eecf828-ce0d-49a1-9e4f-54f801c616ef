import { renderWithRouter } from '@/utils/test';
import { screen } from '@testing-library/react';
import { vi, describe, it, expect, beforeEach } from 'vitest';
import React from 'react';
import AppWrapper from './index';

// Mock dependencies
vi.mock('@/App', () => ({
  default: () => <div data-testid="app-component">App Component</div>,
}));

vi.mock('../BaseLayout', () => ({
  default: ({ children }: { children: React.ReactNode }) => (
    <div data-testid="base-layout">
      <div data-testid="base-layout-children">{children}</div>
    </div>
  ),
}));

describe('AppWrapper', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('should render without crashing', () => {
    const { container } = renderWithRouter(<AppWrapper />);
    
    expect(container).toBeInTheDocument();
  });

  it('should render BaseLayout component', () => {
    renderWithRouter(<AppWrapper />);
    
    expect(screen.getByTestId('base-layout')).toBeInTheDocument();
  });

  it('should render App component inside BaseLayout', () => {
    renderWithRouter(<AppWrapper />);
    
    expect(screen.getByTestId('base-layout')).toBeInTheDocument();
    expect(screen.getByTestId('base-layout-children')).toBeInTheDocument();
    expect(screen.getByTestId('app-component')).toBeInTheDocument();
  });

  it('should pass children to BaseLayout correctly', () => {
    renderWithRouter(<AppWrapper />);
    
    const baseLayoutChildren = screen.getByTestId('base-layout-children');
    const appComponent = screen.getByTestId('app-component');
    
    expect(baseLayoutChildren).toContainElement(appComponent);
  });

  it('should have correct component structure', () => {
    renderWithRouter(<AppWrapper />);
    
    // Verify the component hierarchy
    const baseLayout = screen.getByTestId('base-layout');
    const baseLayoutChildren = screen.getByTestId('base-layout-children');
    const appComponent = screen.getByTestId('app-component');
    
    expect(baseLayout).toContainElement(baseLayoutChildren);
    expect(baseLayoutChildren).toContainElement(appComponent);
  });
});
