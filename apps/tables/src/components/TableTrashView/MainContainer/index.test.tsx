import * as useTables from '@/hooks/useTables';
import { mockLibraries, renderWithMantine } from '@/utils/test';
import { fireEvent, screen } from '@testing-library/react';
import { vi } from 'vitest';
import MainContainer from './index';

mockLibraries();

// Mock the hooks
vi.mock('@/hooks/useTables', () => ({
  useDeletedTablesQuery: vi.fn(() => ({
    tables: [
      { id: 'table1', name: 'Test Table 1', deletedAt: '2023-01-01', baseId: 'base1' },
      { id: 'table2', name: 'Another Table', deletedAt: '2023-01-02', baseId: 'base1' },
      { id: 'table3', name: 'Sample Table', deletedAt: '2023-01-03', baseId: 'base1' },
    ],
    isInitialLoading: false,
    isLoading: false,
    isValidating: false,
    pagination: null,
    error: null,
  })),
}));

// Mock react-router-dom
vi.mock('react-router-dom', () => ({
  useParams: vi.fn(() => ({ baseId: 'test-base' })),
}));

// Mock lunr
const mockAdd = vi.fn();
const mockField = vi.fn();
const mockRef = vi.fn();
const mockSearch = vi.fn(() => [{ ref: 'table1' }, { ref: 'table2' }]);

vi.mock('lunr', () => ({
  default: function (builder: any) {
    const context = {
      field: mockField,
      ref: mockRef,
      add: mockAdd,
    };
    builder.call(context);
    return {
      search: mockSearch,
    };
  },
}));

// Mock the child components
vi.mock('./Search', () => ({
  __esModule: true,
  default: ({ searchText, setSearchText }: any) => (
    <div data-testid='search-control'>
      <input
        data-testid='search-input'
        value={searchText}
        onChange={(e) => setSearchText(e.target.value)}
      />
    </div>
  ),
}));

vi.mock('./TableItem', () => ({
  __esModule: true,
  default: ({ name, id }: any) => <div data-testid={`table-item-${id}`}>{name}</div>,
}));

describe('TableTrashView/MainContainer', () => {
  beforeEach(() => {
    vi.clearAllMocks();
    mockAdd.mockClear();
    mockField.mockClear();
    mockRef.mockClear();
    mockSearch.mockClear();
  });

  describe('Component Rendering', () => {
    it('should render component with search control and table items', () => {
      renderWithMantine(<MainContainer />);

      // Verify basic rendering
      expect(screen.getByTestId('search-control')).toBeInTheDocument();
      expect(screen.getByTestId('search-input')).toBeInTheDocument();

      // Verify table items are rendered
      expect(screen.getByTestId('table-item-table1')).toBeInTheDocument();
      expect(screen.getByTestId('table-item-table2')).toBeInTheDocument();
      expect(screen.getByTestId('table-item-table3')).toBeInTheDocument();

      // Verify table names are displayed
      expect(screen.getByText('Test Table 1')).toBeInTheDocument();
      expect(screen.getByText('Another Table')).toBeInTheDocument();
      expect(screen.getByText('Sample Table')).toBeInTheDocument();
    });

    it('should handle empty tables state', () => {
      vi.mocked(useTables.useDeletedTablesQuery).mockReturnValueOnce({
        tables: [],
        isInitialLoading: false,
        isLoading: false,
        isValidating: false,
        pagination: null,
        error: null,
      });

      renderWithMantine(<MainContainer />);
      expect(screen.getByTestId('search-control')).toBeInTheDocument();
      expect(screen.queryByTestId('table-item-table1')).not.toBeInTheDocument();
    });

    it('should handle loading state', () => {
      vi.mocked(useTables.useDeletedTablesQuery).mockReturnValueOnce({
        tables: [],
        isInitialLoading: true,
        isLoading: true,
        isValidating: false,
        pagination: null,
        error: null,
      });

      renderWithMantine(<MainContainer />);
      expect(screen.getByTestId('search-control')).toBeInTheDocument();
    });

    it('should handle error state', () => {
      vi.mocked(useTables.useDeletedTablesQuery).mockReturnValueOnce({
        tables: [],
        isInitialLoading: false,
        isLoading: false,
        isValidating: false,
        pagination: null,
        error: new Error('Failed to fetch tables'),
      });

      renderWithMantine(<MainContainer />);
      expect(screen.getByTestId('search-control')).toBeInTheDocument();
    });

    it('should handle malformed table data gracefully', () => {
      vi.mocked(useTables.useDeletedTablesQuery).mockReturnValueOnce({
        tables: [
          { id: 'table1', name: undefined as any, deletedAt: undefined as any, baseId: undefined as any },
          { id: undefined as any, name: 'Test Table', deletedAt: '2023-01-01', baseId: 'base1' },
        ] as any,
        isInitialLoading: false,
        isLoading: false,
        isValidating: false,
        pagination: null,
        error: null,
      });

      renderWithMantine(<MainContainer />);
      expect(screen.getByTestId('search-control')).toBeInTheDocument();
    });
  });

  describe('Search Functionality', () => {
    it('should handle comprehensive search scenarios', () => {
      renderWithMantine(<MainContainer />);
      const searchInput = screen.getByTestId('search-input');

      // Initial empty state - should show all tables
      expect(searchInput).toHaveValue('');
      expect(screen.getByText('Test Table 1')).toBeInTheDocument();
      expect(screen.getByText('Another Table')).toBeInTheDocument();
      expect(screen.getByText('Sample Table')).toBeInTheDocument();

      // Test search input changes and state persistence
      fireEvent.change(searchInput, { target: { value: 'Test' } });
      expect(searchInput).toHaveValue('Test');

      fireEvent.change(searchInput, { target: { value: 'Another' } });
      expect(searchInput).toHaveValue('Another');

      // Test clearing search
      fireEvent.change(searchInput, { target: { value: '' } });
      expect(searchInput).toHaveValue('');

      // Test special characters and edge cases
      fireEvent.change(searchInput, { target: { value: 'special!@#$%' } });
      expect(mockSearch).toHaveBeenCalledWith('special!@#$%');

      fireEvent.change(searchInput, { target: { value: '   ' } });
      expect(mockSearch).toHaveBeenCalledWith('   ');
    });

    it('should handle search edge cases', () => {
      // Test empty search results
      mockSearch.mockReturnValueOnce([]);
      renderWithMantine(<MainContainer />);

      const searchInput = screen.getByTestId('search-input');
      fireEvent.change(searchInput, { target: { value: 'no results' } });
      expect(mockSearch).toHaveBeenCalledWith('no results');

      // Test mixed valid/invalid search results
      mockSearch.mockReturnValueOnce([{ ref: 'non-existent' }, { ref: 'table1' }]);
      fireEvent.change(searchInput, { target: { value: 'mixed results' } });
      expect(screen.getByText('Test Table 1')).toBeInTheDocument();
    });
  });

  describe('Lunr Search Integration', () => {
    it('should initialize index and handle search operations', () => {
      mockSearch.mockReturnValueOnce([{ ref: 'table1' }]);
      renderWithMantine(<MainContainer />);

      // Verify lunr index initialization
      expect(mockField).toHaveBeenCalledWith('name');
      expect(mockRef).toHaveBeenCalledWith('id');
      expect(mockAdd).toHaveBeenCalledTimes(3);
      expect(mockAdd).toHaveBeenCalledWith({ id: 'table1', name: 'Test Table 1' });
      expect(mockAdd).toHaveBeenCalledWith({ id: 'table2', name: 'Another Table' });
      expect(mockAdd).toHaveBeenCalledWith({ id: 'table3', name: 'Sample Table' });

      // Verify search functionality
      const searchInput = screen.getByTestId('search-input');
      fireEvent.change(searchInput, { target: { value: 'specific search' } });
      expect(mockSearch).toHaveBeenCalledWith('specific search');
    });
  });
});
