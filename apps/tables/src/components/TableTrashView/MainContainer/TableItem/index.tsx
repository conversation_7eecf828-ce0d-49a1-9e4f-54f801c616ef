import PernamentlyDeletedDate from '@/components/Common/PernamentlyDeletedDate';
import { useDeletedTablesMutation, useTablesMutation } from '@/hooks';
import { Text, rem } from '@mantine/core';
import { createStyles } from '@mantine/emotion';
import { IconReload } from '@tabler/icons-react';
import React, { useCallback, memo } from 'react';

const useStyles = createStyles((theme) => ({
  root: {
    display: 'flex',
    flexDirection: 'row',
    gap: rem(16),
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  tableName: {
    fontWeight: 500,
    fontSize: rem(14),
    color: theme.colors.decaGrey[9],
    maxWidth: '80%',
  },
  controls: {
    display: 'flex',
    flexDirection: 'row',
    gap: rem(16),
    alignItems: 'center',
  },
  icon: {
    width: rem(18),
    height: rem(18),
    color: theme.colors.decaNavy[6],
    cursor: 'pointer',
  },
}));

interface TableItemProps {
  deletedAt: string;
  name: string;
  id: string;
  baseId: string;
}

const TableItem: React.FC<TableItemProps> = ({ deletedAt, name, id, baseId }) => {
  const { classes } = useStyles();
  const { restoreTable, revalidateQuery: revalidateDeletedTable } = useDeletedTablesMutation();
  const { revalidateQuery: revalidateTable } = useTablesMutation();

  const handleRestore = useCallback(async () => {
    const res = await restoreTable(baseId, id);

    if (res.isOk()) {
      // Caching 1-2s in server side, so we need to wait for it
      setTimeout(() => {
        revalidateTable(baseId);
        revalidateDeletedTable(baseId);
      }, 100);
    }
  }, [restoreTable, revalidateTable, revalidateDeletedTable, id, baseId]);

  return (
    <div className={classes.root}>
      <Text className={classes.tableName} truncate>
        {name}
      </Text>
      <div className={classes.controls}>
        <div>
          <PernamentlyDeletedDate date={deletedAt} />
        </div>
        <IconReload className={classes.icon} onClick={handleRestore} />
      </div>
    </div>
  );
};

export default memo(TableItem);
