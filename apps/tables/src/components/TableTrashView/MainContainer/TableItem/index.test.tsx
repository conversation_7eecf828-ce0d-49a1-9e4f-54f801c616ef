import { mockLibraries, renderWithRouter } from '@/utils/test';
import { screen, fireEvent, waitFor } from '@testing-library/react';
import { vi, describe, it, expect, beforeEach } from 'vitest';
import TableItem from './index';
import { Ok, Err } from 'ts-results-es';

const mockRestoreTable = vi.fn();
const mockRevalidateDeletedTable = vi.fn();
const mockRevalidateTable = vi.fn();

vi.mock('@/hooks', () => ({
  useDeletedTablesMutation: () => ({
    restoreTable: mockRestoreTable,
    revalidateQuery: mockRevalidateDeletedTable,
  }),
  useTablesMutation: () => ({
    revalidateQuery: mockRevalidateTable,
  }),
}));

vi.mock('@/components/Common/PernamentlyDeletedDate', () => ({
  __esModule: true,
  default: ({ date }: { date: string }) => <div data-testid="deleted-date">{date}</div>,
}));

mockLibraries();

describe('TableItem', () => {
  const defaultProps = {
    baseId: 'test-base-id',
    deletedAt: '2023-01-01T00:00:00.000Z',
    name: 'Test Table',
    id: 'test-table-id',
  };

  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('displays table name, deletion date, and restore icon', () => {
    renderWithRouter(<TableItem {...defaultProps} />);
    
    expect(screen.getByText('Test Table')).toBeInTheDocument();
    expect(screen.getByTestId('deleted-date')).toHaveTextContent('2023-01-01T00:00:00.000Z');
    
    const restoreIcon = document.querySelector('.tabler-icon-reload');
    expect(restoreIcon).toBeInTheDocument();
  });

  it('calls restore function when user clicks restore icon', () => {
    mockRestoreTable.mockResolvedValueOnce(Ok({}));
    
    renderWithRouter(<TableItem {...defaultProps} />);
    
    const restoreIcon = document.querySelector('.tabler-icon-reload');
    fireEvent.click(restoreIcon!);
    
    expect(mockRestoreTable).toHaveBeenCalledWith('test-base-id', 'test-table-id');
  });

  it('refreshes table data after successful restore', async () => {
    mockRestoreTable.mockResolvedValueOnce(Ok({}));
    
    renderWithRouter(<TableItem {...defaultProps} />);
    
    const restoreIcon = document.querySelector('.tabler-icon-reload');
    fireEvent.click(restoreIcon!);
    
    await waitFor(() => {
      expect(mockRevalidateTable).toHaveBeenCalledWith('test-base-id');
      expect(mockRevalidateDeletedTable).toHaveBeenCalledWith('test-base-id');
    }, { timeout: 1000 });
  });

  it('does not refresh data when restore fails', async () => {
    mockRestoreTable.mockResolvedValueOnce(Err('Restore failed'));
    
    renderWithRouter(<TableItem {...defaultProps} />);
    
    const restoreIcon = document.querySelector('.tabler-icon-reload');
    fireEvent.click(restoreIcon!);
    
    await waitFor(() => {
      expect(mockRestoreTable).toHaveBeenCalled();
    });
    
    await new Promise(resolve => setTimeout(resolve, 500));
    
    expect(mockRevalidateTable).not.toHaveBeenCalled();
    expect(mockRevalidateDeletedTable).not.toHaveBeenCalled();
  });

  it('displays custom table names and dates correctly', () => {
    renderWithRouter(
      <TableItem 
        {...defaultProps} 
        name="My Custom Table"
        deletedAt="2024-12-25T12:00:00.000Z"
      />
    );
    
    expect(screen.getByText('My Custom Table')).toBeInTheDocument();
    expect(screen.getByTestId('deleted-date')).toHaveTextContent('2024-12-25T12:00:00.000Z');
  });

  it('passes correct IDs to restore function', () => {
    mockRestoreTable.mockResolvedValueOnce(Ok({}));
    
    renderWithRouter(
      <TableItem 
        baseId="my-base-123"
        deletedAt={defaultProps.deletedAt}
        name={defaultProps.name}
        id="my-table-456"
      />
    );
    
    const restoreIcon = document.querySelector('.tabler-icon-reload');
    fireEvent.click(restoreIcon!);
    
    expect(mockRestoreTable).toHaveBeenCalledWith('my-base-123', 'my-table-456');
  });
});
