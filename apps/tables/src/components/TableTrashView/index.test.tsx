import { mockLibraries, renderWithMantine } from '@/utils/test';
import { fireEvent, screen } from '@testing-library/react';
import { vi, describe, it, expect, beforeEach, afterEach } from 'vitest';
import TableTrashViewModal from './index';

// Mock dependencies using vi.hoisted like ai-studio
const mocks = vi.hoisted(() => {
  const mockUseTranslate = vi.fn();
  const mockOnClose = vi.fn();

  return {
    mockUseTranslate,
    mockOnClose,
  };
});

// Mock @tolgee/react
vi.mock('@tolgee/react', () => ({
  useTranslate: mocks.mockUseTranslate,
}));

// Mock MainContainer
vi.mock('./MainContainer', () => ({
  default: ({ children, ...props }: any) => <div data-testid="main-container" {...props}>{children}</div>,
}));

mockLibraries();

describe('TableTrashViewModal', () => {
  const defaultProps = {
    opened: true,
    onClose: mocks.mockOnClose,
  };

  const defaultTranslate = {
    t: vi.fn((key) => key),
  };

  beforeEach(() => {
    vi.clearAllMocks();
    mocks.mockUseTranslate.mockReturnValue(defaultTranslate);
  });

  afterEach(() => {
    vi.clearAllMocks();
  });

  describe('Rendering', () => {
    it('should render when opened is true', () => {
      renderWithMantine(<TableTrashViewModal {...defaultProps} />);

      expect(screen.getByText('trashView.modalTitle')).toBeInTheDocument();
      expect(screen.getByTestId('main-container')).toBeInTheDocument();
    });

    it('should not render when opened is false', () => {
      renderWithMantine(<TableTrashViewModal {...defaultProps} opened={false} />);

      expect(screen.queryByText('trashView.modalTitle')).not.toBeInTheDocument();
      expect(screen.queryByTestId('main-container')).not.toBeInTheDocument();
    });

    it('should render with correct modal structure', () => {
      renderWithMantine(<TableTrashViewModal {...defaultProps} />);

      // Check for modal elements using screen queries
      expect(screen.getByRole('dialog')).toBeInTheDocument();
      expect(screen.getByText('trashView.modalTitle')).toBeInTheDocument();
      expect(screen.getByTestId('main-container')).toBeInTheDocument();
    });
  });

  describe('Props Handling', () => {
    it('should handle all required props', () => {
      renderWithMantine(<TableTrashViewModal {...defaultProps} />);

      expect(screen.getByText('trashView.modalTitle')).toBeInTheDocument();
      expect(screen.getByTestId('main-container')).toBeInTheDocument();
    });

    it('should handle minimal props', () => {
      renderWithMantine(<TableTrashViewModal opened={true} onClose={mocks.mockOnClose} />);

      expect(screen.getByText('trashView.modalTitle')).toBeInTheDocument();
      expect(screen.getByTestId('main-container')).toBeInTheDocument();
    });
  });

  describe('Modal Behavior', () => {
    it('should call onClose when close button is clicked', () => {
      renderWithMantine(<TableTrashViewModal {...defaultProps} />);

      const closeButton = screen.getByRole('button');
      fireEvent.click(closeButton);

      expect(mocks.mockOnClose).toHaveBeenCalledTimes(1);
    });

    it('should call onClose when modal overlay is clicked', () => {
      const { container } = renderWithMantine(<TableTrashViewModal {...defaultProps} />);

      const modalOverlay = container.querySelector('.mantine-Modal-overlay');
      if (modalOverlay) {
        fireEvent.click(modalOverlay);
        expect(mocks.mockOnClose).toHaveBeenCalledTimes(1);
      }
    });

    it('should call onClose when escape key is pressed', () => {
      renderWithMantine(<TableTrashViewModal {...defaultProps} />);

      // Simulate escape key on the modal content
      const modalContent = screen.getByRole('dialog');
      fireEvent.keyDown(modalContent, { key: 'Escape' });

      expect(mocks.mockOnClose).toHaveBeenCalledTimes(1);
    });
  });

  describe('Styling', () => {
    it('should apply custom styles correctly', () => {
      renderWithMantine(<TableTrashViewModal {...defaultProps} />);

      // Check that the modal renders with proper structure
      expect(screen.getByRole('dialog')).toBeInTheDocument();
      expect(screen.getByText('trashView.modalTitle')).toBeInTheDocument();
      expect(screen.getByTestId('main-container')).toBeInTheDocument();
    });

    it('should render with correct modal size', () => {
      renderWithMantine(<TableTrashViewModal {...defaultProps} />);

      // Check that the modal renders
      expect(screen.getByRole('dialog')).toBeInTheDocument();
    });
  });

  describe('Translation', () => {
    it('should use translation for modal title', () => {
      renderWithMantine(<TableTrashViewModal {...defaultProps} />);

      expect(mocks.mockUseTranslate).toHaveBeenCalledWith('table');
      expect(defaultTranslate.t).toHaveBeenCalledWith('trashView.modalTitle');
    });

    it('should render translated text', () => {
      const customTranslate = {
        t: vi.fn((key) => `translated_${key}`),
      };
      mocks.mockUseTranslate.mockReturnValue(customTranslate);

      renderWithMantine(<TableTrashViewModal {...defaultProps} />);

      expect(screen.getByText('translated_trashView.modalTitle')).toBeInTheDocument();
    });
  });

  describe('Component Integration', () => {
    it('should render MainContainer component', () => {
      renderWithMantine(<TableTrashViewModal {...defaultProps} />);

      expect(screen.getByTestId('main-container')).toBeInTheDocument();
    });

    it('should pass correct props to MainContainer', () => {
      renderWithMantine(<TableTrashViewModal {...defaultProps} />);

      const mainContainer = screen.getByTestId('main-container');
      expect(mainContainer).toBeInTheDocument();
    });
  });

  describe('Conditional Rendering', () => {
    it('should show modal when opened is true', () => {
      renderWithMantine(<TableTrashViewModal {...defaultProps} opened={true} />);

      expect(screen.getByText('trashView.modalTitle')).toBeInTheDocument();
      expect(screen.getByTestId('main-container')).toBeInTheDocument();
    });

    it('should hide modal when opened is false', () => {
      renderWithMantine(<TableTrashViewModal {...defaultProps} opened={false} />);

      expect(screen.queryByText('trashView.modalTitle')).not.toBeInTheDocument();
      expect(screen.queryByTestId('main-container')).not.toBeInTheDocument();
    });
  });

  describe('Accessibility', () => {
    it('should have proper ARIA attributes', () => {
      renderWithMantine(<TableTrashViewModal {...defaultProps} />);

      const modalTitle = screen.getByText('trashView.modalTitle');
      expect(modalTitle).toBeInTheDocument();
      
      const dialog = screen.getByRole('dialog');
      expect(dialog).toBeInTheDocument();
    });

    it('should have close button with proper accessibility', () => {
      renderWithMantine(<TableTrashViewModal {...defaultProps} />);

      const closeButton = screen.getByRole('button');
      expect(closeButton).toBeInTheDocument();
    });
  });

  describe('Edge Cases', () => {
    it('should handle undefined onClose gracefully', () => {
      const propsWithUndefinedOnClose = {
        opened: true,
        onClose: undefined as any,
      };

      expect(() => {
        renderWithMantine(<TableTrashViewModal {...propsWithUndefinedOnClose} />);
      }).not.toThrow();
    });

    it('should handle different opened states', () => {
      // Test opened state
      renderWithMantine(<TableTrashViewModal {...defaultProps} opened={true} />);
      expect(screen.getByText('trashView.modalTitle')).toBeInTheDocument();

      // Test closed state - modal is still rendered but not visible
      renderWithMantine(<TableTrashViewModal {...defaultProps} opened={false} />);
      // The modal content is still in the DOM but hidden
      expect(screen.queryByText('trashView.modalTitle')).toBeInTheDocument();
    });
  });
});