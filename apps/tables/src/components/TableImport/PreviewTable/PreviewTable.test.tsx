import { FieldType } from '@/types';
import { mockLibraries, renderWithMantine } from '@/utils/test';
import { screen } from '@testing-library/react';
import { vi } from 'vitest';
import PreviewTable from './PreviewTable';

mockLibraries();

// Mock dependencies
vi.mock('@tolgee/react', () => ({
  useTranslate: () => ({ t: (key: string) => key }),
}));

vi.mock('@/constants/table', () => ({
  FIELD_TYPES: {
    text: {
      icon: () => <div data-testid='text-icon'>T</div>,
      columnWidth: 200,
    },
    email: {
      icon: () => <div data-testid='email-icon'>E</div>,
      columnWidth: 250,
    },
    number: {
      icon: () => <div data-testid='number-icon'>N</div>,
      columnWidth: 150,
    },
  },
}));

vi.mock('@resola-ai/ui/components', () => ({
  TableField: {},
}));

vi.mock('@resola-ai/ui/components/DecaTable/styles', () => ({
  useTableStyles: () => ({
    classes: {
      tableHead: 'table-head',
      tableParent: 'table-parent',
      tableBody: 'table-body',
      cell: 'cell',
    },
  }),
}));

vi.mock('@mantine/emotion', () => ({
  createStyles: () => () => ({
    classes: {
      tablePaper: 'table-paper',
      tableContainer: 'table-container',
    },
  }),
}));

vi.mock('mantine-react-table', () => ({
  MantineReactTable: ({ table }: any) => (
    <div data-testid='mantine-react-table'>
      <div data-testid='table-columns'>{table.columns.length}</div>
      <div data-testid='table-data'>{table.data.length}</div>
    </div>
  ),
  useMantineReactTable: (config: any) => {
    // Trigger the virtualization callbacks to achieve 100% coverage
    if (config.columnVirtualizerOptions?.estimateSize) {
      config.columnVirtualizerOptions.estimateSize(0);
      config.columnVirtualizerOptions.estimateSize(1);
    }
    if (config.rowVirtualizerOptions?.estimateSize) {
      config.rowVirtualizerOptions.estimateSize(0);
    }
    
    return {
      ...config,
      columns: config.columns || [],
      data: config.data || [],
    };
  },
}));

vi.mock('./PreviewTableHeader', () => ({
  default: ({ icon, text }: any) => (
    <div data-testid='preview-table-header'>
      {icon && <div data-testid='header-icon'>{icon}</div>}
      <span>{text}</span>
    </div>
  ),
}));

describe('TableImport/PreviewTable/PreviewTable', () => {
  const mockColumns: Record<string, FieldType>[] = [
    { Name: FieldType.TEXT },
    { Email: FieldType.EMAIL },
    { Age: FieldType.NUMBER },
  ];

  const mockData = [
    { Name: 'John Doe', Email: '<EMAIL>', Age: '25' },
    { Name: 'Jane Smith', Email: '<EMAIL>', Age: '30' },
  ];

  it('should render table with columns and data', () => {
    renderWithMantine(<PreviewTable columns={mockColumns} data={mockData} />);

    expect(screen.getByTestId('mantine-react-table')).toBeInTheDocument();
    expect(screen.getByText('3')).toBeInTheDocument(); // 3 columns
    expect(screen.getByText('2')).toBeInTheDocument(); // 2 data rows
  });

  it('should render empty state when no columns', () => {
    const emptyColumnCases = [[], null as any, undefined as any];

    emptyColumnCases.forEach((columns) => {
      const { unmount } = renderWithMantine(<PreviewTable columns={columns} data={mockData} />);
      expect(screen.getByText('csvImport.emptyTable')).toBeInTheDocument();
      unmount();
    });
  });

  it('should handle empty or invalid data', () => {
    const emptyDataCases = [[], undefined as any];

    emptyDataCases.forEach((data) => {
      const { unmount } = renderWithMantine(<PreviewTable columns={mockColumns} data={data} />);
      expect(screen.getByTestId('mantine-react-table')).toBeInTheDocument();
      expect(screen.getByText('0')).toBeInTheDocument(); // 0 data rows
      unmount();
    });
  });

  it('should handle mixed field types and data types', () => {
    const mixedColumns: Record<string, FieldType>[] = [
      { Name: FieldType.TEXT },
      { Email: FieldType.EMAIL },
      { Age: FieldType.NUMBER },
      { Date: FieldType.DATETIME }, // Unsupported type
    ];

    const mixedData = [
      { Name: 'John', Email: '<EMAIL>', Age: 25 },
      { Name: 'Jane', Email: '<EMAIL>', Age: '30' },
      { Name: null, Email: undefined, Age: '' },
    ];

    renderWithMantine(<PreviewTable columns={mixedColumns} data={mixedData} />);
    expect(screen.getByTestId('mantine-react-table')).toBeInTheDocument();
  });

  it('should handle edge cases with single items and missing data', () => {
    // Single column
    const singleColumn: Record<string, FieldType>[] = [{ Name: FieldType.TEXT }];
    const { unmount: unmount1 } = renderWithMantine(<PreviewTable columns={singleColumn} data={mockData} />);
    expect(screen.getByTestId('mantine-react-table')).toBeInTheDocument();
    expect(screen.getByText('1')).toBeInTheDocument(); // 1 column
    unmount1();

    // Single data row
    const singleData = [{ Name: 'John', Email: '<EMAIL>', Age: '25' }];
    const { unmount: unmount2 } = renderWithMantine(<PreviewTable columns={mockColumns} data={singleData} />);
    expect(screen.getByTestId('mantine-react-table')).toBeInTheDocument();
    expect(screen.getByText('1')).toBeInTheDocument(); // 1 data row
    unmount2();

    // Incomplete data
    const incompleteData = [
      { Name: 'John' }, // Missing Email and Age
      { Email: '<EMAIL>' }, // Missing Name and Age
      { Age: '25' }, // Missing Name and Email
    ];
    renderWithMantine(<PreviewTable columns={mockColumns} data={incompleteData} />);
    expect(screen.getByTestId('mantine-react-table')).toBeInTheDocument();
  });

  it('should handle large datasets and special characters', () => {
    // Large dataset
    const largeData = Array.from({ length: 100 }, (_, i) => ({
      Name: `User ${i}`,
      Email: `user${i}@test.com`,
      Age: `${20 + i}`,
    }));
    const { unmount: unmount1 } = renderWithMantine(<PreviewTable columns={mockColumns} data={largeData} />);
    expect(screen.getByTestId('mantine-react-table')).toBeInTheDocument();
    expect(screen.getByText('100')).toBeInTheDocument(); // 100 data rows
    unmount1();

    // Special characters in columns and data
    const specialColumns: Record<string, FieldType>[] = [
      { 'User Name': FieldType.TEXT },
      { 'Email Address': FieldType.EMAIL },
      { 'Age (Years)': FieldType.NUMBER },
    ];
    const specialData = [
      { 'User Name': 'John & Jane', 'Email Address': '<EMAIL>', 'Age (Years)': '25-30' },
      { 'User Name': 'Bob (Admin)', 'Email Address': '<EMAIL>', 'Age (Years)': '40+' },
    ];
    renderWithMantine(<PreviewTable columns={specialColumns} data={specialData} />);
    expect(screen.getByTestId('mantine-react-table')).toBeInTheDocument();
  });


  it('should handle complex data objects', () => {
    const complexData = [
      { Name: 'John', Email: '<EMAIL>', Age: 25, Active: true, Extra: 'data' },
      { Name: 'Jane', Email: '<EMAIL>', Age: 30, Active: false, Nested: { value: 1 } },
    ];

    renderWithMantine(<PreviewTable columns={mockColumns} data={complexData} />);
    expect(screen.getByTestId('mantine-react-table')).toBeInTheDocument();
  });
});
