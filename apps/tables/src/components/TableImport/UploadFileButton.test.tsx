import { renderWithMantine } from '@/utils/test';
import { screen, fireEvent, waitFor } from '@testing-library/react';
import { vi, describe, it, expect, beforeEach, afterEach } from 'vitest';
import UploadFileButton from './UploadFileButton';

// Mock dependencies using vi.hoisted
const mocks = vi.hoisted(() => {
  const mockUseTranslate = vi.fn();
  const mockOnUpload = vi.fn();
  const mockNotificationsShow = vi.fn();

  return {
    mockUseTranslate,
    mockOnUpload,
    mockNotificationsShow,
  };
});

// Mock Tolgee
vi.mock('@tolgee/react', () => ({
  useTranslate: mocks.mockUseTranslate,
}));

// Mock Common components
vi.mock('@/components/Common', () => ({
  notifications: {
    show: mocks.mockNotificationsShow,
  },
}));

// Mock constants
vi.mock('@/constants', () => ({
  UPLOAD_CSV_IMAGE_URL: 'https://example.com/upload-csv-image.png',
}));

// Mock TableImport constants
vi.mock('./constants', () => ({
  ACCEPT_FILE_TYPES: ['text/csv'],
  ACCEPT_FILE_TYPES_STRING: 'text/csv',
  MAX_FILE_SIZE: 5 * 1024 * 1024, // 5MB
}));

// Mock @resola-ai/ui components
vi.mock('@resola-ai/ui', () => ({
  CustomImage: ({ url, ...props }: any) => (
    <img data-testid="custom-image" src={url} alt="Upload CSV" {...props} />
  ),
  DecaButton: ({ children, onClick, ...props }: any) => (
    <button data-testid="deca-button" onClick={onClick} {...props}>
      {children}
    </button>
  ),
}));

describe('UploadFileButton', () => {
  const defaultProps = {
    onUpload: mocks.mockOnUpload,
  };

  beforeEach(() => {
    vi.clearAllMocks();
    mocks.mockUseTranslate.mockReturnValue({
      t: (key: string) => key,
    });
  });

  afterEach(() => {
    vi.clearAllMocks();
  });

  describe('Rendering', () => {
    it('should render without crashing', () => {
      const { container } = renderWithMantine(
        <UploadFileButton {...defaultProps} />
      );
      expect(container).toBeInTheDocument();
    });

    it('should render CustomImage with correct URL', () => {
      renderWithMantine(<UploadFileButton {...defaultProps} />);
      
      const image = screen.getByTestId('custom-image');
      expect(image).toBeInTheDocument();
      expect(image).toHaveAttribute('src', 'https://example.com/upload-csv-image.png');
      expect(image).toHaveAttribute('alt', 'Upload CSV');
    });

    it('should render upload button when no file is selected', () => {
      renderWithMantine(<UploadFileButton {...defaultProps} />);
      
      expect(screen.getByText('csvImport.uploadFileBtn')).toBeInTheDocument();
      expect(screen.getByText('csvImport.fileSizeExceeded')).toBeInTheDocument();
    });

    it('should render file input with correct accept attribute', () => {
      renderWithMantine(<UploadFileButton {...defaultProps} />);
      
      const fileInput = screen.getByRole('button', { hidden: true });
      expect(fileInput).toBeInTheDocument();
    });
  });

  describe('File Upload Handling', () => {
    it('should call onUpload with valid CSV file', async () => {
      const mockFile = new File(['test content'], 'test.csv', { type: 'text/csv' });
      
      renderWithMantine(<UploadFileButton {...defaultProps} />);
      
      // Find the hidden file input
      const fileInput = document.querySelector('input[type="file"]') as HTMLInputElement;
      expect(fileInput).toBeInTheDocument();
      
      // Simulate file selection
      Object.defineProperty(fileInput, 'files', {
        value: [mockFile],
        writable: false,
      });
      
      fireEvent.change(fileInput);
      
      await waitFor(() => {
        expect(mocks.mockOnUpload).toHaveBeenCalledWith(mockFile);
      });
    });

    it('should not call onUpload when no file is selected', () => {
      renderWithMantine(<UploadFileButton {...defaultProps} />);
      
      const fileInput = document.querySelector('input[type="file"]') as HTMLInputElement;
      Object.defineProperty(fileInput, 'files', {
        value: [],
        writable: false,
      });
      
      fireEvent.change(fileInput);
      
      expect(mocks.mockOnUpload).not.toHaveBeenCalled();
    });

    it('should not call onUpload when file is null', () => {
      renderWithMantine(<UploadFileButton {...defaultProps} />);
      
      const fileInput = document.querySelector('input[type="file"]') as HTMLInputElement;
      // Use empty FileList instead of null to avoid Mantine error
      Object.defineProperty(fileInput, 'files', {
        value: [],
        writable: false,
      });
      
      fireEvent.change(fileInput);
      
      expect(mocks.mockOnUpload).not.toHaveBeenCalled();
    });
  });

  describe('File Validation', () => {
    it('should show error for invalid file type', async () => {
      const mockFile = new File(['test content'], 'test.txt', { type: 'text/plain' });
      
      renderWithMantine(<UploadFileButton {...defaultProps} />);
      
      const fileInput = document.querySelector('input[type="file"]') as HTMLInputElement;
      Object.defineProperty(fileInput, 'files', {
        value: [mockFile],
        writable: false,
      });
      
      fireEvent.change(fileInput);
      
      await waitFor(() => {
        expect(mocks.mockNotificationsShow).toHaveBeenCalledWith({
          message: 'csvImport.notification.invalidUploadFileType',
          status: 'error',
        });
      });
      
      expect(mocks.mockOnUpload).not.toHaveBeenCalled();
    });

    it('should show error for file exceeding size limit', async () => {
      // Create a file larger than 5MB
      const largeContent = 'x'.repeat(6 * 1024 * 1024); // 6MB
      const mockFile = new File([largeContent], 'large.csv', { type: 'text/csv' });
      
      renderWithMantine(<UploadFileButton {...defaultProps} />);
      
      const fileInput = document.querySelector('input[type="file"]') as HTMLInputElement;
      Object.defineProperty(fileInput, 'files', {
        value: [mockFile],
        writable: false,
      });
      
      fireEvent.change(fileInput);
      
      await waitFor(() => {
        expect(mocks.mockNotificationsShow).toHaveBeenCalledWith({
          message: 'csvImport.notification.fileSizeExceeded',
          status: 'error',
        });
      });
      
      expect(mocks.mockOnUpload).not.toHaveBeenCalled();
    });

    it('should accept file at exact size limit', async () => {
      // Create a file exactly at the 5MB limit
      const exactSizeContent = 'x'.repeat(5 * 1024 * 1024); // 5MB
      const mockFile = new File([exactSizeContent], 'exact-size.csv', { type: 'text/csv' });
      
      renderWithMantine(<UploadFileButton {...defaultProps} />);
      
      const fileInput = document.querySelector('input[type="file"]') as HTMLInputElement;
      Object.defineProperty(fileInput, 'files', {
        value: [mockFile],
        writable: false,
      });
      
      fireEvent.change(fileInput);
      
      await waitFor(() => {
        expect(mocks.mockOnUpload).toHaveBeenCalledWith(mockFile);
      });
      
      expect(mocks.mockNotificationsShow).not.toHaveBeenCalled();
    });

    it('should accept file smaller than size limit', async () => {
      const mockFile = new File(['small content'], 'small.csv', { type: 'text/csv' });
      
      renderWithMantine(<UploadFileButton {...defaultProps} />);
      
      const fileInput = document.querySelector('input[type="file"]') as HTMLInputElement;
      Object.defineProperty(fileInput, 'files', {
        value: [mockFile],
        writable: false,
      });
      
      fireEvent.change(fileInput);
      
      await waitFor(() => {
        expect(mocks.mockOnUpload).toHaveBeenCalledWith(mockFile);
      });
      
      expect(mocks.mockNotificationsShow).not.toHaveBeenCalled();
    });
  });

  describe('State Management', () => {
    it('should update state when valid file is uploaded', async () => {
      const mockFile = new File(['test content'], 'test.csv', { type: 'text/csv' });
      
      renderWithMantine(<UploadFileButton {...defaultProps} />);
      
      const fileInput = document.querySelector('input[type="file"]') as HTMLInputElement;
      Object.defineProperty(fileInput, 'files', {
        value: [mockFile],
        writable: false,
      });
      
      fireEvent.change(fileInput);
      
      await waitFor(() => {
        expect(screen.getByText('test.csv')).toBeInTheDocument();
        expect(screen.getByText('csvImport.changeFileBtn')).toBeInTheDocument();
      });
    });

    it('should not update state when invalid file is uploaded', async () => {
      const mockFile = new File(['test content'], 'test.txt', { type: 'text/plain' });
      
      renderWithMantine(<UploadFileButton {...defaultProps} />);
      
      const fileInput = document.querySelector('input[type="file"]') as HTMLInputElement;
      Object.defineProperty(fileInput, 'files', {
        value: [mockFile],
        writable: false,
      });
      
      fireEvent.change(fileInput);
      
      await waitFor(() => {
        expect(screen.queryByText('test.txt')).not.toBeInTheDocument();
        expect(screen.getByText('csvImport.uploadFileBtn')).toBeInTheDocument();
      });
    });

    it('should allow changing file after initial upload', async () => {
      const firstFile = new File(['first content'], 'first.csv', { type: 'text/csv' });
      const secondFile = new File(['second content'], 'second.csv', { type: 'text/csv' });
      
      renderWithMantine(<UploadFileButton {...defaultProps} />);
      
      // Upload first file
      let fileInput = document.querySelector('input[type="file"]') as HTMLInputElement;
      Object.defineProperty(fileInput, 'files', {
        value: [firstFile],
        writable: true,
        configurable: true,
      });
      fireEvent.change(fileInput);
      
      await waitFor(() => {
        expect(screen.getByText('first.csv')).toBeInTheDocument();
      });
      
      // After first upload, a new FileButton is rendered for "Change File"
      // Find the new file input (there should be only one at this point)
      fileInput = document.querySelector('input[type="file"]') as HTMLInputElement;
      
      // Upload second file using the new file input
      Object.defineProperty(fileInput, 'files', {
        value: [secondFile],
        writable: true,
        configurable: true,
      });
      fireEvent.change(fileInput);
      
      await waitFor(() => {
        expect(screen.getByText('second.csv')).toBeInTheDocument();
        expect(screen.queryByText('first.csv')).not.toBeInTheDocument();
        expect(mocks.mockOnUpload).toHaveBeenCalledTimes(2);
      });
    });
  });

  describe('Props Handling', () => {
    it('should handle missing onUpload prop gracefully', () => {
      expect(() => {
        renderWithMantine(<UploadFileButton onUpload={undefined as any} />);
      }).not.toThrow();
    });

    it('should call onUpload when provided', async () => {
      const mockFile = new File(['test content'], 'test.csv', { type: 'text/csv' });
      
      renderWithMantine(<UploadFileButton {...defaultProps} />);
      
      const fileInput = document.querySelector('input[type="file"]') as HTMLInputElement;
      Object.defineProperty(fileInput, 'files', {
        value: [mockFile],
        writable: false,
      });
      
      fireEvent.change(fileInput);
      
      await waitFor(() => {
        expect(mocks.mockOnUpload).toHaveBeenCalledWith(mockFile);
      });
    });
  });

  describe('Translation Integration', () => {
    it('should use translation function for all text content', () => {
      renderWithMantine(<UploadFileButton {...defaultProps} />);
      
      expect(mocks.mockUseTranslate).toHaveBeenCalledWith('common');
      expect(screen.getByText('csvImport.uploadFileBtn')).toBeInTheDocument();
      expect(screen.getByText('csvImport.fileSizeExceeded')).toBeInTheDocument();
    });

    it('should use translation function for change button text', async () => {
      const mockFile = new File(['test content'], 'test.csv', { type: 'text/csv' });
      
      renderWithMantine(<UploadFileButton {...defaultProps} />);
      
      const fileInput = document.querySelector('input[type="file"]') as HTMLInputElement;
      Object.defineProperty(fileInput, 'files', {
        value: [mockFile],
        writable: false,
      });
      
      fireEvent.change(fileInput);
      
      await waitFor(() => {
        expect(screen.getByText('csvImport.changeFileBtn')).toBeInTheDocument();
      });
    });

    it('should use translation function for error messages', async () => {
      const mockFile = new File(['test content'], 'test.txt', { type: 'text/plain' });
      
      renderWithMantine(<UploadFileButton {...defaultProps} />);
      
      const fileInput = document.querySelector('input[type="file"]') as HTMLInputElement;
      Object.defineProperty(fileInput, 'files', {
        value: [mockFile],
        writable: false,
      });
      
      fireEvent.change(fileInput);
      
      await waitFor(() => {
        expect(mocks.mockNotificationsShow).toHaveBeenCalledWith({
          message: 'csvImport.notification.invalidUploadFileType',
          status: 'error',
        });
      });
    });
  });

  describe('File Input Attributes', () => {
    it('should set correct accept attribute on file input', () => {
      renderWithMantine(<UploadFileButton {...defaultProps} />);
      
      const fileInput = document.querySelector('input[type="file"]') as HTMLInputElement;
      expect(fileInput).toHaveAttribute('accept', 'text/csv');
    });
  });

  describe('Edge Cases', () => {
    it('should handle file with empty name', async () => {
      const mockFile = new File(['test content'], '', { type: 'text/csv' });
      
      renderWithMantine(<UploadFileButton {...defaultProps} />);
      
      const fileInput = document.querySelector('input[type="file"]') as HTMLInputElement;
      Object.defineProperty(fileInput, 'files', {
        value: [mockFile],
        writable: false,
      });
      
      fireEvent.change(fileInput);
      
      await waitFor(() => {
        expect(mocks.mockOnUpload).toHaveBeenCalledWith(mockFile);
      });
    });

    it('should handle file with very long name', async () => {
      const longName = 'a'.repeat(300) + '.csv';
      const mockFile = new File(['test content'], longName, { type: 'text/csv' });
      
      renderWithMantine(<UploadFileButton {...defaultProps} />);
      
      const fileInput = document.querySelector('input[type="file"]') as HTMLInputElement;
      Object.defineProperty(fileInput, 'files', {
        value: [mockFile],
        writable: false,
      });
      
      fireEvent.change(fileInput);
      
      await waitFor(() => {
        expect(screen.getByText(longName)).toBeInTheDocument();
        expect(mocks.mockOnUpload).toHaveBeenCalledWith(mockFile);
      });
    });

    it('should handle file with special characters in name', async () => {
      const specialName = 'test-file_with.special+chars!.csv';
      const mockFile = new File(['test content'], specialName, { type: 'text/csv' });
      
      renderWithMantine(<UploadFileButton {...defaultProps} />);
      
      const fileInput = document.querySelector('input[type="file"]') as HTMLInputElement;
      Object.defineProperty(fileInput, 'files', {
        value: [mockFile],
        writable: false,
      });
      
      fireEvent.change(fileInput);
      
      await waitFor(() => {
        expect(screen.getByText(specialName)).toBeInTheDocument();
        expect(mocks.mockOnUpload).toHaveBeenCalledWith(mockFile);
      });
    });
  });
});