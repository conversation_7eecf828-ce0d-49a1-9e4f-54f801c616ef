import { mockLibraries, renderWithMantine } from '@/utils/test';
import { screen } from '@testing-library/react';
import { vi } from 'vitest';
import { TableImportProvider, useTableImportContext } from './TableImportContext';
import { TableImportScreen } from './constants';

mockLibraries();

// Test component that uses the context
const TestComponent = () => {
  const context = useTableImportContext();
  return (
    <div>
      <div data-testid="active-screen">{context.activeScreen}</div>
      <div data-testid="import-data">{context.importData ? 'has-data' : 'no-data'}</div>
      <div data-testid="is-show-loading">{context.isShowLoadingOverlay.toString()}</div>
      <button 
        data-testid="set-active-screen" 
        onClick={() => context.setActiveScreen(TableImportScreen.FIELD_MAPPING_SCREEN)}
      >
        Set Field Mapping
      </button>
      <button 
        data-testid="set-import-data" 
        onClick={() => context.setImportData({ headers: [] } as any)}
      >
        Set Import Data
      </button>
      <button data-testid="on-close" onClick={context.onClose}>
        Close
      </button>
      <button data-testid="open-loading" onClick={context.openLoadingOverlay}>
        Open Loading
      </button>
      <button data-testid="close-loading" onClick={context.closeLoadingOverlay}>
        Close Loading
      </button>
    </div>
  );
};

// Test component that uses context outside provider
const TestComponentOutsideProvider = () => {
  try {
    useTableImportContext();
    return <div data-testid="no-error">No error</div>;
  } catch (error) {
    return <div data-testid="error">{(error as Error).message}</div>;
  }
};

describe('TableImport/TableImportContext', () => {
  const mockOnClose = vi.fn();
  const mockOpenLoadingOverlay = vi.fn();
  const mockCloseLoadingOverlay = vi.fn();

  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe('TableImportProvider', () => {
    it('should render without crashing', () => {
      renderWithMantine(
        <TableImportProvider
          onClose={mockOnClose}
          isShowLoadingOverlay={false}
          openLoadingOverlay={mockOpenLoadingOverlay}
          closeLoadingOverlay={mockCloseLoadingOverlay}
        >
          <div>Test</div>
        </TableImportProvider>
      );
    });

    it('should provide context values to children', () => {
      renderWithMantine(
        <TableImportProvider
          onClose={mockOnClose}
          isShowLoadingOverlay={true}
          openLoadingOverlay={mockOpenLoadingOverlay}
          closeLoadingOverlay={mockCloseLoadingOverlay}
        >
          <TestComponent />
        </TableImportProvider>
      );

      expect(screen.getByTestId('active-screen')).toHaveTextContent(TableImportScreen.IMPORT_SCREEN);
      expect(screen.getByTestId('import-data')).toHaveTextContent('no-data');
      expect(screen.getByTestId('is-show-loading')).toHaveTextContent('true');
    });

    it('should merge hook state with props in context value', () => {
      renderWithMantine(
        <TableImportProvider
          onClose={mockOnClose}
          isShowLoadingOverlay={false}
          openLoadingOverlay={mockOpenLoadingOverlay}
          closeLoadingOverlay={mockCloseLoadingOverlay}
        >
          <TestComponent />
        </TableImportProvider>
      );

      // Test that all props are available in context
      expect(screen.getByTestId('on-close')).toBeInTheDocument();
      expect(screen.getByTestId('open-loading')).toBeInTheDocument();
      expect(screen.getByTestId('close-loading')).toBeInTheDocument();
    });
  });

  describe('useTableImportContext hook', () => {
    it('should return context value when used within provider', () => {
      renderWithMantine(
        <TableImportProvider
          onClose={mockOnClose}
          isShowLoadingOverlay={false}
          openLoadingOverlay={mockOpenLoadingOverlay}
          closeLoadingOverlay={mockCloseLoadingOverlay}
        >
          <TestComponent />
        </TableImportProvider>
      );

      expect(screen.getByTestId('active-screen')).toHaveTextContent(TableImportScreen.IMPORT_SCREEN);
    });

    it('should throw error when used outside provider', () => {
      renderWithMantine(<TestComponentOutsideProvider />);
      expect(screen.getByTestId('error')).toHaveTextContent('useTableImportContext must be used within a TableImportProvider');
    });
  });

  describe('useTableImport hook state management', () => {
    it('should initialize with default values', () => {
      renderWithMantine(
        <TableImportProvider
          onClose={mockOnClose}
          isShowLoadingOverlay={false}
          openLoadingOverlay={mockOpenLoadingOverlay}
          closeLoadingOverlay={mockCloseLoadingOverlay}
        >
          <TestComponent />
        </TableImportProvider>
      );

      expect(screen.getByTestId('active-screen')).toHaveTextContent(TableImportScreen.IMPORT_SCREEN);
      expect(screen.getByTestId('import-data')).toHaveTextContent('no-data');
    });

    it('should update activeScreen when setActiveScreen is called', async () => {
      const user = await import('@testing-library/user-event').then(m => m.default);
      
      renderWithMantine(
        <TableImportProvider
          onClose={mockOnClose}
          isShowLoadingOverlay={false}
          openLoadingOverlay={mockOpenLoadingOverlay}
          closeLoadingOverlay={mockCloseLoadingOverlay}
        >
          <TestComponent />
        </TableImportProvider>
      );

      expect(screen.getByTestId('active-screen')).toHaveTextContent(TableImportScreen.IMPORT_SCREEN);
      
      await user.click(screen.getByTestId('set-active-screen'));
      
      expect(screen.getByTestId('active-screen')).toHaveTextContent(TableImportScreen.FIELD_MAPPING_SCREEN);
    });

    it('should update importData when setImportData is called', async () => {
      const user = await import('@testing-library/user-event').then(m => m.default);
      
      renderWithMantine(
        <TableImportProvider
          onClose={mockOnClose}
          isShowLoadingOverlay={false}
          openLoadingOverlay={mockOpenLoadingOverlay}
          closeLoadingOverlay={mockCloseLoadingOverlay}
        >
          <TestComponent />
        </TableImportProvider>
      );

      expect(screen.getByTestId('import-data')).toHaveTextContent('no-data');
      
      await user.click(screen.getByTestId('set-import-data'));
      
      expect(screen.getByTestId('import-data')).toHaveTextContent('has-data');
    });

    it('should call onClose when close button is clicked', async () => {
      const user = await import('@testing-library/user-event').then(m => m.default);
      
      renderWithMantine(
        <TableImportProvider
          onClose={mockOnClose}
          isShowLoadingOverlay={false}
          openLoadingOverlay={mockOpenLoadingOverlay}
          closeLoadingOverlay={mockCloseLoadingOverlay}
        >
          <TestComponent />
        </TableImportProvider>
      );

      await user.click(screen.getByTestId('on-close'));
      
      expect(mockOnClose).toHaveBeenCalledTimes(1);
    });

    it('should call openLoadingOverlay when open loading button is clicked', async () => {
      const user = await import('@testing-library/user-event').then(m => m.default);
      
      renderWithMantine(
        <TableImportProvider
          onClose={mockOnClose}
          isShowLoadingOverlay={false}
          openLoadingOverlay={mockOpenLoadingOverlay}
          closeLoadingOverlay={mockCloseLoadingOverlay}
        >
          <TestComponent />
        </TableImportProvider>
      );

      await user.click(screen.getByTestId('open-loading'));
      
      expect(mockOpenLoadingOverlay).toHaveBeenCalledTimes(1);
    });

    it('should call closeLoadingOverlay when close loading button is clicked', async () => {
      const user = await import('@testing-library/user-event').then(m => m.default);
      
      renderWithMantine(
        <TableImportProvider
          onClose={mockOnClose}
          isShowLoadingOverlay={false}
          openLoadingOverlay={mockOpenLoadingOverlay}
          closeLoadingOverlay={mockCloseLoadingOverlay}
        >
          <TestComponent />
        </TableImportProvider>
      );

      await user.click(screen.getByTestId('close-loading'));
      
      expect(mockCloseLoadingOverlay).toHaveBeenCalledTimes(1);
    });
  });
});
