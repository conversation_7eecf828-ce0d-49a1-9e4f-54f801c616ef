import { mockLibraries, renderWithRouter } from '@/utils/test';
import { screen } from '@testing-library/react';
import { vi, describe, it, expect, beforeEach, afterEach } from 'vitest';
import TableImportLoadingOverlay from './LoadingOverlay';
import { IWebsocketResponse } from '@/types';

// Mock dependencies
const mocks = vi.hoisted(() => {
  const mockCloseOverlay = vi.fn();
  const mockCreateCustomEventListener = vi.fn();
  const mockUnregister = vi.fn();
  const mockUseWindowEvent = vi.fn();
  const mockRevalidateQuery = vi.fn();
  const mockNotificationsShow = vi.fn();
  let dataImportHandler: Function | undefined;

  return {
    mockCloseOverlay,
    mockCreateCustomEventListener,
    mockUnregister,
    mockUseWindowEvent,
    mockRevalidateQuery,
    mockNotificationsShow,
    dataImportHandler,
  };
});

vi.mock('@resola-ai/utils', () => ({
  createCustomEventListener: mocks.mockCreateCustomEventListener,
}));

vi.mock('@mantine/hooks', () => ({
  useWindowEvent: mocks.mockUseWindowEvent,
}));

vi.mock('@/hooks', () => ({
  useTableRecordsMutation: () => ({
    revalidateQuery: mocks.mockRevalidateQuery,
  }),
}));

vi.mock('../Common', () => ({
  LoaderSpinner: () => <div data-testid="loader-spinner">Loading...</div>,
  notifications: {
    show: mocks.mockNotificationsShow,
  },
}));

vi.mock('@tolgee/react', () => ({
  useTranslate: () => ({ t: (key: string) => key }),
}));

vi.mock('react-router-dom', async () => {
  const actual = await vi.importActual('react-router-dom');
  return {
    ...actual,
    useParams: () => ({ baseId: 'test-base', tableId: 'test-table' }),
  };
});

mockLibraries();

describe('TableImportLoadingOverlay', () => {
  const defaultProps = {
    isShow: true,
    closeOverlay: mocks.mockCloseOverlay,
  };

  beforeEach(() => {
    vi.clearAllMocks();
    
    // Set up mock to capture the data import handler
    mocks.mockCreateCustomEventListener.mockImplementation((eventType: string, handler: Function) => {
      if (eventType === 'data.import') {
        mocks.dataImportHandler = handler;
      }
      return mocks.mockUnregister;
    });
    mocks.mockUseWindowEvent.mockImplementation(() => {});
  });

  afterEach(() => {
    vi.clearAllMocks();
  });

  describe('Rendering', () => {
    it('should render loading overlay when isShow is true', () => {
      renderWithRouter(<TableImportLoadingOverlay {...defaultProps} />);
      
      expect(screen.getByTestId('loader-spinner')).toBeInTheDocument();
      expect(screen.getByText('csvImport.importingData')).toBeInTheDocument();
    });

    it('should not render when isShow is false', () => {
      renderWithRouter(<TableImportLoadingOverlay {...defaultProps} isShow={false} />);
      
      expect(screen.queryByTestId('loader-spinner')).not.toBeInTheDocument();
      expect(screen.queryByText('csvImport.importingData')).not.toBeInTheDocument();
    });
  });


  describe('When data import completes', () => {
    it('shows success message and closes overlay when import succeeds', () => {
      renderWithRouter(<TableImportLoadingOverlay {...defaultProps} />);

      const successEvent = {
        detail: {
          data: {
            importCompleted: true,
            hasError: false
          }
        }
      } as unknown as CustomEvent<IWebsocketResponse<{ importCompleted?: boolean; hasError?: boolean }>>;

      if (mocks.dataImportHandler) {
        mocks.dataImportHandler(successEvent);
        expect(mocks.mockCloseOverlay).toHaveBeenCalled();
        expect(mocks.mockNotificationsShow).toHaveBeenCalledWith({
          message: 'csvImport.notification.importSuccess',
          status: 'success',
        });
        expect(mocks.mockRevalidateQuery).toHaveBeenCalledWith('test-base', 'test-table');
      }
    });

    it('shows error message and closes overlay when import fails', () => {
      renderWithRouter(<TableImportLoadingOverlay {...defaultProps} />);

      const errorEvent = {
        detail: {
          data: {
            importCompleted: false,
            hasError: true
          }
        }
      } as unknown as CustomEvent<IWebsocketResponse<{ importCompleted?: boolean; hasError?: boolean }>>;

      if (mocks.dataImportHandler) {
        mocks.dataImportHandler(errorEvent);
        expect(mocks.mockCloseOverlay).toHaveBeenCalled();
        expect(mocks.mockNotificationsShow).toHaveBeenCalledWith({
          message: 'csvImport.notification.importError',
          status: 'error',
        });
      }
    });

    it('does nothing when import is still in progress', () => {
      renderWithRouter(<TableImportLoadingOverlay {...defaultProps} />);

      const incompleteEvent = {
        detail: {
          data: {
            importCompleted: false,
            hasError: false
          }
        }
      } as unknown as CustomEvent<IWebsocketResponse<{ importCompleted?: boolean; hasError?: boolean }>>;

      if (mocks.dataImportHandler) {
        mocks.dataImportHandler(incompleteEvent);
        expect(mocks.mockCloseOverlay).not.toHaveBeenCalled();
        expect(mocks.mockNotificationsShow).not.toHaveBeenCalled();
      }
    });
  });

  describe('When user tries to leave during import', () => {
    it('prevents page unload and shows warning when overlay is visible', () => {
      // Capture the real beforeunload handler using nuclear approach
      const originalAddEventListener = global.addEventListener;
      let realBeforeUnloadHandler: ((event: BeforeUnloadEvent) => string | undefined) | null = null;
      
      global.addEventListener = vi.fn((event: string, handler: any, options?: any) => {
        if (event === 'beforeunload') {
          realBeforeUnloadHandler = handler as (event: BeforeUnloadEvent) => string | undefined;
        }
        return originalAddEventListener?.call(global, event, handler, options);
      }) as any;
      
      renderWithRouter(<TableImportLoadingOverlay {...defaultProps} />);

      // Execute the REAL component handler
      if (realBeforeUnloadHandler) {
        const beforeUnloadEvent = new Event('beforeunload') as BeforeUnloadEvent;
        const preventDefaultSpy = vi.spyOn(beforeUnloadEvent, 'preventDefault');

        const result = (realBeforeUnloadHandler as any)(beforeUnloadEvent);
        
        expect(preventDefaultSpy).toHaveBeenCalled();
        expect(result).toBe('csvImport.leaveWarning');
      }
      
      if (originalAddEventListener) {
        global.addEventListener = originalAddEventListener;
      }
    });
  });

  describe('When user presses keys during import', () => {
    it('blocks all keyboard events to prevent interference', () => {
      // Capture the real keydown handler using nuclear approach
      const originalAddEventListener = global.addEventListener;
      let realKeydownHandler: Function | null = null;
      
      global.addEventListener = vi.fn((event: string, handler: any, options?: any) => {
        if (event === 'keydown') {
          realKeydownHandler = handler;
        }
        return originalAddEventListener?.call(global, event, handler, options);
      }) as any;
      
      renderWithRouter(<TableImportLoadingOverlay {...defaultProps} />);

      // Execute the REAL component handler
      if (realKeydownHandler) {
        const keyEvent = new KeyboardEvent('keydown', {
          key: 'Enter',
          bubbles: true,
          cancelable: true
        });
        
        const preventDefaultSpy = vi.spyOn(keyEvent, 'preventDefault');
        const stopPropagationSpy = vi.spyOn(keyEvent, 'stopPropagation');

        (realKeydownHandler as any)(keyEvent);
        
        expect(preventDefaultSpy).toHaveBeenCalled();
        expect(stopPropagationSpy).toHaveBeenCalled();
      }
      
      if (originalAddEventListener) {
        global.addEventListener = originalAddEventListener;
      }
    });

  });
});
