import { renderWithMantine } from '@/utils/test';
import { screen, fireEvent, waitFor } from '@testing-library/react';
import { vi, describe, it, expect, beforeEach, afterEach } from 'vitest';
import ImportScreen from './ImportScreen';
import { TableImportProvider } from '../TableImportContext';
import { TableImportScreen, TableImportType } from '../constants';

// Mock dependencies using vi.hoisted
const mocks = vi.hoisted(() => {
  const mockUseTranslate = vi.fn();
  const mockUseParams = vi.fn();
  const mockUseTablesQuery = vi.fn();
  const mockCsvToJson = vi.fn();
  const mockNotificationsShow = vi.fn();
  const mockSetImportData = vi.fn();
  const mockSetActiveScreen = vi.fn();
  const mockOnClose = vi.fn();
  const mockUseForm = vi.fn();

  return {
    mockUseTranslate,
    mockUseParams,
    mockUseTablesQuery,
    mockCsvToJson,
    mockNotificationsShow,
    mockSetImportData,
    mockSetActiveScreen,
    mockOnClose,
    mockUseForm,
  };
});

// Mock Tolgee
vi.mock('@tolgee/react', () => ({
  useTranslate: mocks.mockUseTranslate,
  useTolgee: vi.fn(),
  TolgeeProvider: ({ children }: any) => children,
  Tolgee: vi.fn(() => ({
    use: vi.fn().mockReturnThis(),
    init: vi.fn().mockResolvedValue(undefined),
  })),
  FormatSimple: vi.fn(),
}));

// Mock Tolgee web tools
vi.mock('@tolgee/web/tools', () => ({
  InContextTools: vi.fn(),
}));

// Mock tolgee instance
vi.mock('@/tolgee', () => ({
  tolgee: {
    t: vi.fn((key: string) => key),
  },
}));

// Mock React Router
vi.mock('react-router-dom', () => ({
  useParams: mocks.mockUseParams,
}));

// Mock hooks
vi.mock('@/hooks', () => ({
  useTablesQuery: mocks.mockUseTablesQuery,
}));

// Mock utils
vi.mock('@/utils', () => ({
  csvToJson: mocks.mockCsvToJson,
}));

// Mock Common components
vi.mock('@/components/Common', () => ({
  notifications: {
    show: mocks.mockNotificationsShow,
  },
}));

// Mock @resola-ai/ui components
vi.mock('@resola-ai/ui', () => ({
  DecaButton: ({ children, onClick, disabled, type, variant, ...props }: any) => (
    <button
      data-testid="deca-button"
      onClick={onClick}
      disabled={disabled}
      type={type}
      data-variant={variant}
      {...props}
    >
      {children}
    </button>
  ),
  DecaRadio: ({ value, label, disabled, ...props }: any) => (
    <label data-testid="deca-radio" data-value={value} data-disabled={disabled}>
      <input
        type="radio"
        value={value}
        disabled={disabled}
        {...props}
      />
      {label}
    </label>
  ),
  HFRadio: {
    Group: ({ children, control, name, onChange, ...props }: any) => {
      // Store the onChange callback so tests can access it
      if (onChange) {
        (global as any).mockHFRadioGroupOnChange = onChange;
      }
      return (
        <div data-testid="hf-radio-group" data-name={name} {...props}>
          {children}
        </div>
      );
    },
  },
  HFSwitch: ({ name, label, defaultChecked, defaultValue, ...props }: any) => (
    <label data-testid="hf-switch" data-name={name}>
      <input
        type="checkbox"
        name={name}
        defaultChecked={defaultChecked}
        defaultValue={defaultValue}
        {...props}
      />
      {label}
    </label>
  ),
}));

// Mock Mantine components
vi.mock('@mantine/core', async () => {
  const actual = await vi.importActual('@mantine/core');
  return {
    ...actual,
    Flex: ({ children, ...props }: any) => (
      <div data-testid="flex" {...props}>
        {children}
      </div>
    ),
    Group: ({ children, ...props }: any) => (
      <div data-testid="group" {...props}>
        {children}
      </div>
    ),
    Stack: ({ children, ...props }: any) => (
      <div data-testid="stack" {...props}>
        {children}
      </div>
    ),
    Text: ({ children, ...props }: any) => (
      <span data-testid="text" {...props}>
        {children}
      </span>
    ),
  };
});

// Mock react-hook-form-mantine
vi.mock('react-hook-form-mantine', () => ({
  Select: ({ control, name, placeholder, data, ...props }: any) => (
    <select data-testid="select" data-name={name} {...props}>
      <option value="">{placeholder}</option>
      {data?.map((option: any) => (
        <option key={option.value} value={option.value}>
          {option.label}
        </option>
      ))}
    </select>
  ),
  TextInput: ({ control, name, placeholder, ...props }: any) => (
    <input
      data-testid="text-input"
      type="text"
      name={name}
      placeholder={placeholder}
      {...props}
    />
  ),
}));

// Mock Tabler icons
vi.mock('@tabler/icons-react', () => ({
  IconChevronDown: () => <span data-testid="icon-chevron-down">▼</span>,
}));

// Mock UploadFileButton
vi.mock('../UploadFileButton', () => ({
  default: ({ onUpload }: any) => (
    <button
      data-testid="upload-file-button"
      onClick={() => {
        const mockFile = new File(['test content'], 'test.csv', { type: 'text/csv' });
        onUpload(mockFile);
      }}
    >
      Upload File
    </button>
  ),
}));

// Mock LoaderText
vi.mock('../LoaderText', () => ({
  default: ({ text }: any) => <div data-testid="loader-text">{text}</div>,
}));

// Mock react-hook-form
vi.mock('react-hook-form', () => ({
  useForm: mocks.mockUseForm,
  FormProvider: ({ children, ...props }: any) => (
    <div data-testid="form-provider" {...props}>
      {children}
    </div>
  ),
}));

// Mock @hookform/resolvers/zod
vi.mock('@hookform/resolvers/zod', () => ({
  zodResolver: vi.fn(),
}));

// Mock @mantine/emotion
vi.mock('@mantine/emotion', () => ({
  createStyles: vi.fn(() => ({
    classes: {
      uploadImage: 'upload-image',
      radioButton: 'radio-button',
      borderLeft: 'border-left',
      borderTop: 'border-top',
      loadingOverlay: 'loading-overlay',
      selectOption: 'select-option',
      selectIcon: 'select-icon',
    },
  })),
}));

describe('ImportScreen', () => {
  const defaultContextValue = {
    setImportData: mocks.mockSetImportData,
    setActiveScreen: mocks.mockSetActiveScreen,
    onClose: mocks.mockOnClose,
    isShowLoadingOverlay: false,
    openLoadingOverlay: vi.fn(),
    closeLoadingOverlay: vi.fn(),
  };

  const mockTables = [
    { id: 'table-1', name: 'Table 1' },
    { id: 'table-2', name: 'Table 2' },
  ];

  const mockFormMethods = {
    control: {},
    watch: vi.fn(),
    setValue: vi.fn(),
    handleSubmit: vi.fn(),
    formState: {
      isSubmitting: false,
      isValid: true,
    },
  };

  beforeEach(() => {
    vi.clearAllMocks();
    
    mocks.mockUseTranslate.mockReturnValue({
      t: (key: string) => key,
    });
    
    mocks.mockUseParams.mockReturnValue({
      baseId: 'base-123',
    });
    
    mocks.mockUseTablesQuery.mockReturnValue({
      tables: mockTables,
    });
    
    mocks.mockCsvToJson.mockResolvedValue({
      columns: ['col1', 'col2'],
      rows: [{ col1: 'value1', col2: 'value2' }],
    });
    
    // Mock useForm
    mocks.mockUseForm.mockReturnValue(mockFormMethods);
    
    // Mock watch to return different values based on test
    mockFormMethods.watch.mockReturnValue(TableImportType.IMPORT_TO_EXISTING_TABLE);
  });

  afterEach(() => {
    vi.clearAllMocks();
  });

  const renderWithProvider = (contextValue = defaultContextValue) => {
    return renderWithMantine(
      <TableImportProvider {...contextValue}>
        <ImportScreen />
      </TableImportProvider>
    );
  };

  describe('Rendering', () => {
    it('should render all components correctly', () => {
      const { container } = renderWithProvider();
      expect(container).toBeInTheDocument();
      
      // Form components
      expect(screen.getByTestId('form-provider')).toBeInTheDocument();
      expect(screen.getByTestId('upload-file-button')).toBeInTheDocument();
      
      // Content sections
      expect(screen.getByText('csvImport.chooseTableHeader')).toBeInTheDocument();
      
      // Radio buttons and their labels
      expect(screen.getByText('csvImport.importExistingTable')).toBeInTheDocument();
      expect(screen.getByText('csvImport.createNewTable')).toBeInTheDocument();
      
      // Options switches
      expect(screen.getByText('csvImport.useFirstRowAsHeader')).toBeInTheDocument();
      
      // Action buttons
      expect(screen.getByText('csvImport.cancelBtn')).toBeInTheDocument();
      expect(screen.getByText('csvImport.nextBtn')).toBeInTheDocument();
    });
  });

  describe('Table Selection Logic', () => {
    it('should enable existing table option when tables exist', () => {
      renderWithProvider();
      const radioButtons = screen.getAllByTestId('deca-radio');
      const existingTableRadio = radioButtons.find(radio => radio.getAttribute('data-value') === 'importToExistingTable');
      expect(existingTableRadio).toHaveAttribute('data-disabled', 'false');
    });

    it('should disable existing table option when no tables exist', () => {
      mocks.mockUseTablesQuery.mockReturnValue({ tables: [] });
      renderWithProvider();
      const radioButtons = screen.getAllByTestId('deca-radio');
      const existingTableRadio = radioButtons.find(radio => radio.getAttribute('data-value') === 'importToExistingTable');
      expect(existingTableRadio).toHaveAttribute('data-disabled', 'true');
    });

    it('should show table select and options when importing to existing table', () => {
      mockFormMethods.watch.mockReturnValue(TableImportType.IMPORT_TO_EXISTING_TABLE);
      renderWithProvider();
      expect(screen.getByTestId('select')).toBeInTheDocument();
      expect(screen.getByText('csvImport.chooseTable')).toBeInTheDocument();
      expect(screen.getByText('csvImport.createNewFieldsForMissingOptions')).toBeInTheDocument();
      
      // Verify table options are rendered
      expect(screen.getByText('Table 1')).toBeInTheDocument();
      expect(screen.getByText('Table 2')).toBeInTheDocument();
    });

    it('should show table name input when creating new table', () => {
      mockFormMethods.watch.mockReturnValue(TableImportType.CREATE_NEW_TABLE);
      renderWithProvider();
      expect(screen.getByTestId('text-input')).toBeInTheDocument();
      expect(screen.getByPlaceholderText('csvImport.enterTableName')).toBeInTheDocument();
      expect(screen.queryByText('csvImport.createNewFieldsForMissingOptions')).not.toBeInTheDocument();
    });
  });

  describe('Form Submission', () => {
    it('should handle successful form submission', async () => {
      const mockFile = new File(['test content'], 'test.csv', { type: 'text/csv' });
      mockFormMethods.handleSubmit.mockImplementation((callback) => (e: any) => {
        e?.preventDefault?.();
        callback({
          importType: TableImportType.IMPORT_TO_EXISTING_TABLE,
          file: mockFile,
          options: {
            table: { id: 'table-1' },
            useFirstRowAsHeader: false,
            createNewFieldsForMissingOptions: false,
          },
        });
      });

      renderWithProvider();

      const nextButton = screen.getByText('csvImport.nextBtn');
      fireEvent.click(nextButton);

      await waitFor(() => {
        expect(mocks.mockCsvToJson).toHaveBeenCalledWith(mockFile, {
          timeout: 1000,
          onProgress: expect.any(Function),
        });
      });

      await waitFor(() => {
        expect(mocks.mockSetImportData).toHaveBeenCalledWith({
          isCreate: false,
          table: { id: 'table-1', name: 'Table 1' },
          uploadFile: {
            rows: [{ col1: 'value1', col2: 'value2' }],
            columns: ['col1', 'col2'],
            file: mockFile,
          },
          options: {
            useFirstRowAsHeader: false,
            createNewFieldsFromMissingOptions: false,
          },
        });
      });

      expect(mocks.mockSetActiveScreen).toHaveBeenCalledWith(TableImportScreen.FIELD_MAPPING_SCREEN);
    });

    it('should handle CSV parsing error', async () => {
      const mockFile = new File(['test content'], 'test.csv', { type: 'text/csv' });
      mocks.mockCsvToJson.mockRejectedValue(new Error('Parse error'));
      
      mockFormMethods.handleSubmit.mockImplementation((callback) => (e: any) => {
        e?.preventDefault?.();
        callback({
          importType: TableImportType.CREATE_NEW_TABLE,
          file: mockFile,
          options: {
            table: { name: 'New Table' },
            useFirstRowAsHeader: true,
            createNewFieldsForMissingOptions: false,
          },
        });
      });

      renderWithProvider();

      const nextButton = screen.getByText('csvImport.nextBtn');
      fireEvent.click(nextButton);

      await waitFor(() => {
        expect(mocks.mockNotificationsShow).toHaveBeenCalledWith({
          message: 'csvImport.notification.parseCSVError',
          status: 'error',
        });
      });

      expect(mocks.mockSetActiveScreen).toHaveBeenCalledWith(TableImportScreen.FIELD_MAPPING_SCREEN);
    });

    it('should handle new table creation', async () => {
      const mockFile = new File(['test content'], 'test.csv', { type: 'text/csv' });
      mockFormMethods.handleSubmit.mockImplementation((callback) => (e: any) => {
        e?.preventDefault?.();
        callback({
          importType: TableImportType.CREATE_NEW_TABLE,
          file: mockFile,
          options: {
            table: { name: 'New Table' },
            useFirstRowAsHeader: true,
            createNewFieldsForMissingOptions: false,
          },
        });
      });

      renderWithProvider();

      const nextButton = screen.getByText('csvImport.nextBtn');
      fireEvent.click(nextButton);

      await waitFor(() => {
        expect(mocks.mockSetImportData).toHaveBeenCalledWith({
          isCreate: true,
          table: { id: undefined, name: 'New Table' },
          uploadFile: {
            rows: [{ col1: 'value1', col2: 'value2' }],
            columns: ['col1', 'col2'],
            file: mockFile,
          },
          options: {
            useFirstRowAsHeader: true,
            createNewFieldsFromMissingOptions: false,
          },
        });
      });
    });
  });

  describe('Loading State', () => {
    it('should show loading overlay when submitting', () => {
      mockFormMethods.formState.isSubmitting = true;
      
      renderWithProvider();
      
      expect(screen.getByTestId('loader-text')).toBeInTheDocument();
      expect(screen.getByText('csvImport.loadingData')).toBeInTheDocument();
    });

    it('should not show loading overlay when not submitting', () => {
      mockFormMethods.formState.isSubmitting = false;
      
      renderWithProvider();
      
      expect(screen.queryByTestId('loader-text')).not.toBeInTheDocument();
    });
  });

  describe('Button States', () => {
    it('should disable next button when form is invalid', () => {
      mockFormMethods.formState.isValid = false;
      mockFormMethods.formState.isSubmitting = false;
      renderWithProvider();
      
      const nextButton = screen.getByText('csvImport.nextBtn');
      expect(nextButton).toBeDisabled();
    });

    it('should disable next button when submitting', () => {
      mockFormMethods.formState.isValid = true;
      mockFormMethods.formState.isSubmitting = true;
      renderWithProvider();
      
      const nextButton = screen.getByText('csvImport.nextBtn');
      expect(nextButton).toBeDisabled();
    });

    it('should enable next button when form is valid and not submitting', () => {
      mockFormMethods.formState.isValid = true;
      mockFormMethods.formState.isSubmitting = false;
      renderWithProvider();
      
      const nextButton = screen.getByText('csvImport.nextBtn');
      expect(nextButton).not.toBeDisabled();
    });
  });

  describe('File Upload', () => {
    it('should handle file upload', () => {
      renderWithProvider();
      
      const uploadButton = screen.getByTestId('upload-file-button');
      fireEvent.click(uploadButton);
      
      expect(mockFormMethods.setValue).toHaveBeenCalledWith('file', expect.any(File), {
        shouldValidate: true,
      });
    });
  });

  describe('Radio Button Changes', () => {
    it('should handle radio change to existing table', () => {
      // Start with create new table selected
      mockFormMethods.watch.mockReturnValue(TableImportType.CREATE_NEW_TABLE);
      
      renderWithProvider();
      
      // Get the captured onChange callback from the component
      const onRadioChange = (global as any).mockHFRadioGroupOnChange;
      expect(onRadioChange).toBeDefined();
      
      // Trigger the radio change to existing table
      onRadioChange(TableImportType.IMPORT_TO_EXISTING_TABLE);
      
      // Verify setValue was called with correct values for existing table
      expect(mockFormMethods.setValue).toHaveBeenCalledWith(
        'options.table',
        { id: 'table-1', name: '' },
        { shouldValidate: true }
      );
    });

    it('should handle radio change to new table', () => {
      // Start with existing table selected
      mockFormMethods.watch.mockReturnValue(TableImportType.IMPORT_TO_EXISTING_TABLE);
      
      renderWithProvider();
      
      // Get the captured onChange callback from the component
      const onRadioChange = (global as any).mockHFRadioGroupOnChange;
      expect(onRadioChange).toBeDefined();
      
      // Trigger the radio change to new table
      onRadioChange(TableImportType.CREATE_NEW_TABLE);
      
      // Verify setValue was called with correct values for new table
      expect(mockFormMethods.setValue).toHaveBeenCalledWith(
        'options.table',
        { id: undefined, name: '' }
      );
    });
  });

  describe('Cancel Action', () => {
    it('should call onClose when cancel button is clicked', () => {
      renderWithProvider();
      
      const cancelButton = screen.getByText('csvImport.cancelBtn');
      fireEvent.click(cancelButton);
      
      expect(mocks.mockOnClose).toHaveBeenCalled();
    });
  });


  describe('Progress Updates', () => {
    it('should update progress during CSV parsing', async () => {
      let progressCallback: (progress: number) => void = () => {};
      mocks.mockCsvToJson.mockImplementation((_file, options) => {
        progressCallback = options.onProgress;
        return Promise.resolve({
          columns: ['col1'],
          rows: [{ col1: 'value1' }],
        });
      });

      const mockFile = new File(['test content'], 'test.csv', { type: 'text/csv' });
      mockFormMethods.handleSubmit.mockImplementation((callback) => (e: any) => {
        e?.preventDefault?.();
        callback({
          importType: TableImportType.IMPORT_TO_EXISTING_TABLE,
          file: mockFile,
          options: {
            table: { id: 'table-1' },
            useFirstRowAsHeader: false,
            createNewFieldsForMissingOptions: false,
          },
        });
      });

      renderWithProvider();

      const nextButton = screen.getByText('csvImport.nextBtn');
      fireEvent.click(nextButton);

      await waitFor(() => {
        expect(mocks.mockCsvToJson).toHaveBeenCalled();
      });

      // Simulate progress update
      if (progressCallback) {
        progressCallback(50);
        // The progress state would be updated in the component
      }
    });
  });

  describe('Default Values', () => {
    it('should set appropriate default values based on table availability', () => {
      // Test with existing tables - should default to import to existing table
      renderWithProvider();
      expect(mockFormMethods.setValue).toBeDefined();

      // Test with no existing tables - should default to create new table
      mocks.mockUseTablesQuery.mockReturnValue({ tables: [] });
      renderWithProvider();
      expect(mockFormMethods.setValue).toBeDefined();
    });
  });
});