import { describe, it, expect, vi } from 'vitest';
import { z } from 'zod';
import { importScreenFormSchema, fieldMappingFormSchema } from './schema';
import { TableImportType } from '../constants';

// Mock tolgee
vi.mock('@/tolgee', () => ({
  tolgee: {
    t: vi.fn((key: string) => key),
  },
}));

describe('TableImport Screen Schema', () => {
  describe('importScreenFormSchema', () => {
    const validFile = new File(['test'], 'test.csv', { type: 'text/csv' });

    it('should validate a valid import to existing table', () => {
      const validData = {
        importType: TableImportType.IMPORT_TO_EXISTING_TABLE,
        options: {
          table: {
            id: 'table-123',
            name: 'Test Table',
          },
          createNewFieldsForMissingOptions: true,
          useFirstRowAsHeader: false,
        },
        file: validFile,
      };

      const result = importScreenFormSchema.safeParse(validData);
      expect(result.success).toBe(true);
    });

    it('should validate a valid create new table import', () => {
      const validData = {
        importType: TableImportType.CREATE_NEW_TABLE,
        options: {
          table: {
            name: 'New Table',
          },
          createNewFieldsForMissingOptions: false,
          useFirstRowAsHeader: true,
        },
        file: validFile,
      };

      const result = importScreenFormSchema.safeParse(validData);
      expect(result.success).toBe(true);
    });

    it('should validate with minimal required fields for existing table', () => {
      const validData = {
        importType: TableImportType.IMPORT_TO_EXISTING_TABLE,
        options: {
          table: {
            id: 'table-123',
          },
        },
        file: validFile,
      };

      const result = importScreenFormSchema.safeParse(validData);
      expect(result.success).toBe(true);
    });

    it('should fail when importType is missing', () => {
      const invalidData = {
        options: {},
        file: validFile,
      };

      const result = importScreenFormSchema.safeParse(invalidData);
      expect(result.success).toBe(false);
      if (!result.success) {
        expect(result.error.issues[0].path).toEqual(['importType']);
      }
    });

    it('should fail when file is missing', () => {
      const invalidData = {
        importType: TableImportType.CREATE_NEW_TABLE,
        options: {},
      };

      const result = importScreenFormSchema.safeParse(invalidData);
      expect(result.success).toBe(false);
      if (!result.success) {
        expect(result.error.issues[0].path).toEqual(['file']);
      }
    });

    it('should fail when file is not a File instance', () => {
      const invalidData = {
        importType: TableImportType.CREATE_NEW_TABLE,
        options: {},
        file: 'not-a-file',
      };

      const result = importScreenFormSchema.safeParse(invalidData);
      expect(result.success).toBe(false);
      if (!result.success) {
        expect(result.error.issues[0].path).toEqual(['file']);
      }
    });

    it('should fail when importType is invalid', () => {
      const invalidData = {
        importType: 'invalid-type',
        options: {},
        file: validFile,
      };

      const result = importScreenFormSchema.safeParse(invalidData);
      expect(result.success).toBe(false);
      if (!result.success) {
        expect(result.error.issues[0].path).toEqual(['importType']);
      }
    });

    it('should fail when importing to existing table without table id', () => {
      const invalidData = {
        importType: TableImportType.IMPORT_TO_EXISTING_TABLE,
        options: {
          table: {
            name: 'Test Table',
          },
        },
        file: validFile,
      };

      const result = importScreenFormSchema.safeParse(invalidData);
      expect(result.success).toBe(false);
      if (!result.success) {
        expect(result.error.issues[0].path).toEqual(['options', 'table', 'id']);
        expect(result.error.issues[0].code).toBe(z.ZodIssueCode.custom);
      }
    });

    it('should fail when creating new table without table name', () => {
      const invalidData = {
        importType: TableImportType.CREATE_NEW_TABLE,
        options: {
          table: {
            id: 'table-123',
          },
        },
        file: validFile,
      };

      const result = importScreenFormSchema.safeParse(invalidData);
      expect(result.success).toBe(false);
      if (!result.success) {
        expect(result.error.issues[0].path).toEqual(['options', 'table', 'name']);
        expect(result.error.issues[0].code).toBe(z.ZodIssueCode.custom);
      }
    });

    it('should handle optional fields correctly', () => {
      const validData = {
        importType: TableImportType.CREATE_NEW_TABLE,
        options: {
          table: {
            name: 'New Table',
          },
          createNewFieldsForMissingOptions: true,
          useFirstRowAsHeader: false,
        },
        file: validFile,
      };

      const result = importScreenFormSchema.safeParse(validData);
      expect(result.success).toBe(true);
      if (result.success) {
        expect(result.data.options.createNewFieldsForMissingOptions).toBe(true);
        expect(result.data.options.useFirstRowAsHeader).toBe(false);
      }
    });

    it('should fail with empty options object for create new table', () => {
      const invalidData = {
        importType: TableImportType.CREATE_NEW_TABLE,
        options: {},
        file: validFile,
      };

      const result = importScreenFormSchema.safeParse(invalidData);
      expect(result.success).toBe(false);
      if (!result.success) {
        expect(result.error.issues[0].path).toEqual(['options', 'table', 'name']);
        expect(result.error.issues[0].code).toBe(z.ZodIssueCode.custom);
      }
    });

    it('should fail when missing table object in options for create new table', () => {
      const invalidData = {
        importType: TableImportType.CREATE_NEW_TABLE,
        options: {
          createNewFieldsForMissingOptions: true,
        },
        file: validFile,
      };

      const result = importScreenFormSchema.safeParse(invalidData);
      expect(result.success).toBe(false);
      if (!result.success) {
        expect(result.error.issues[0].path).toEqual(['options', 'table', 'name']);
        expect(result.error.issues[0].code).toBe(z.ZodIssueCode.custom);
      }
    });
  });

  describe('fieldMappingFormSchema', () => {
    it('should validate a valid field mapping', () => {
      const validData = {
        fields: {
          field1: { id: 'id1', type: 'text' },
          field2: { id: 'id2', type: 'number' },
          field3: { id: 'id3', type: 'date' },
        },
      };

      const result = fieldMappingFormSchema.safeParse(validData);
      expect(result.success).toBe(true);
    });

    it('should validate with empty fields object', () => {
      const validData = {
        fields: {},
      };

      const result = fieldMappingFormSchema.safeParse(validData);
      expect(result.success).toBe(true);
    });

    it('should validate with optional id and type fields', () => {
      const validData = {
        fields: {
          field1: {},
          field2: { id: 'id2' },
          field3: { type: 'text' },
        },
      };

      const result = fieldMappingFormSchema.safeParse(validData);
      expect(result.success).toBe(true);
    });

    it('should fail when fields is missing', () => {
      const invalidData = {};

      const result = fieldMappingFormSchema.safeParse(invalidData);
      expect(result.success).toBe(false);
      if (!result.success) {
        expect(result.error.issues[0].path).toEqual(['fields']);
      }
    });

    it('should fail when fields is not an object', () => {
      const invalidData = {
        fields: 'not-an-object',
      };

      const result = fieldMappingFormSchema.safeParse(invalidData);
      expect(result.success).toBe(false);
      if (!result.success) {
        expect(result.error.issues[0].path).toEqual(['fields']);
      }
    });

    it('should fail when field values are not objects', () => {
      const invalidData = {
        fields: {
          field1: 'not-an-object',
          field2: 123,
        },
      };

      const result = fieldMappingFormSchema.safeParse(invalidData);
      expect(result.success).toBe(false);
      if (!result.success) {
        expect(result.error.issues[0].path).toEqual(['fields', 'field1']);
      }
    });

    it('should fail when field id is not a string', () => {
      const invalidData = {
        fields: {
          field1: { id: 123, type: 'text' },
        },
      };

      const result = fieldMappingFormSchema.safeParse(invalidData);
      expect(result.success).toBe(false);
      if (!result.success) {
        expect(result.error.issues[0].path).toEqual(['fields', 'field1', 'id']);
      }
    });

    it('should fail when field type is not a string', () => {
      const invalidData = {
        fields: {
          field1: { id: 'id1', type: 123 },
        },
      };

      const result = fieldMappingFormSchema.safeParse(invalidData);
      expect(result.success).toBe(false);
      if (!result.success) {
        expect(result.error.issues[0].path).toEqual(['fields', 'field1', 'type']);
      }
    });
  });

  describe('Type inference', () => {
    it('should correctly infer ImportScreenFormData type', () => {
      // This test ensures the type is properly exported and can be used
      const data: z.infer<typeof importScreenFormSchema> = {
        importType: TableImportType.CREATE_NEW_TABLE,
        options: {
          table: {
            name: 'Test Table',
          },
        },
        file: new File(['test'], 'test.csv'),
      };

      expect(data.importType).toBe(TableImportType.CREATE_NEW_TABLE);
      expect(data.options.table?.name).toBe('Test Table');
      expect(data.file).toBeInstanceOf(File);
    });

    it('should correctly infer FieldMappingFormData type', () => {
      // This test ensures the type is properly exported and can be used
      const data: z.infer<typeof fieldMappingFormSchema> = {
        fields: {
          field1: { id: 'id1', type: 'text' },
        },
      };

      expect(data.fields.field1.id).toBe('id1');
      expect(data.fields.field1.type).toBe('text');
    });
  });
});