import { mockLibraries, renderWithRouter } from '@/utils/test';
import { TableImportProvider } from '../TableImportContext';
import FieldMappingScreen from './FieldMappingScreen';
import { screen, fireEvent, waitFor } from '@testing-library/react';
import { vi } from 'vitest';

mockLibraries();

// Mock react-hook-form
const mockWatch = vi.fn();
const mockHandleSubmit = vi.fn();
const mockFormState = { isSubmitting: false };

vi.mock('react-hook-form', () => ({
  FormProvider: ({ children }: { children: React.ReactNode }) => <div>{children}</div>,
  useForm: () => ({
    watch: mockWatch,
    handleSubmit: mockHandleSubmit,
    formState: mockFormState,
  }),
}));

// Mock @tolgee/react
vi.mock('@tolgee/react', () => ({
  TolgeeProvider: ({ children }: { children: React.ReactNode }) => children,
  useTranslate: () => ({
    t: vi.fn((key: string) => key),
  }),
  useTolgee: () => ({
    tolgee: {
      getLanguage: vi.fn(),
      changeLanguage: vi.fn(),
    },
  }),
  Tolgee: vi.fn(() => ({
    use: vi.fn().mockReturnThis(),
    init: vi.fn().mockResolvedValue(undefined),
  })),
  FormatSimple: vi.fn(() => ({})),
}));

// Mock @tolgee/web/tools
vi.mock('@tolgee/web/tools', () => ({
  InContextTools: vi.fn(() => ({})),
}));

// Mock the tolgee object from tolgee/index.ts
vi.mock('@/tolgee', () => ({
  tolgee: {
    t: vi.fn((key: string) => key),
  },
}));

// Mock react-router-dom
  const mockNavigate = vi.fn();
const mockUseParams = vi.fn();

vi.mock('react-router-dom', () => ({
  useNavigate: () => mockNavigate,
  useParams: () => mockUseParams(),
  MemoryRouter: ({ children }: { children: React.ReactNode }) => <div>{children}</div>,
}));

// Mock hooks
vi.mock('@/hooks', () => ({
  useTablesQuery: vi.fn(),
  useTablesMutation: vi.fn(),
}));

// Mock API calls
vi.mock('@/services/api', () => ({
  CommonAPI: {
    presignUrl: vi.fn(),
    uploadFile: vi.fn(),
  },
  DataAPI: {
    importTableData: vi.fn(),
  },
}));

// Mock Mantine notifications
vi.mock('@mantine/notifications', () => ({
  notifications: {
    show: vi.fn(),
  },
}));

// Mock child components
vi.mock('../FieldMappingControl', () => ({
  FieldMappingControl: ({ name }: { name: string }) => <div data-testid="field-mapping-control">{name}</div>,
}));

vi.mock('../PreviewTable', () => ({
  PreviewTable: ({ columns, data }: { columns: any[]; data: any[] }) => (
    <div data-testid="preview-table">
      <div>Columns: {columns.length}</div>
      <div>Data: {data.length}</div>
    </div>
  ),
}));

// Mock the TableImportContext to provide setImportData
const mockSetImportData = vi.fn();
let currentImportData: any = null;

// Create mock functions that can be accessed in tests
const mockOpenLoadingOverlay = vi.fn();
const mockCloseLoadingOverlay = vi.fn();
const mockOnClose = vi.fn();

vi.mock('../TableImportContext', () => ({
  TableImportProvider: ({ children }: { children: React.ReactNode }) => <div>{children}</div>,
  useTableImportContext: () => ({
    importData: currentImportData,
    setImportData: mockSetImportData,
    openLoadingOverlay: mockOpenLoadingOverlay,
    closeLoadingOverlay: mockCloseLoadingOverlay,
    onClose: mockOnClose,
  }),
}));

// Mock utility functions
vi.mock('@/utils', () => ({
  getTablePath: (baseId: string, tableId: string) => `/tables/bases/${baseId}/tables/${tableId}`,
}));

// Mock lodash
vi.mock('lodash', () => ({
  partition: (array: any[], predicate: (item: any) => boolean) => {
    const truthy = array.filter(predicate);
    const falsy = array.filter(item => !predicate(item));
    return [truthy, falsy];
  },
}));

describe('TableImport/Screen/FieldMappingScreen', () => {

  const mockImportData = {
    isCreate: true,
    table: {
      id: 'new-table-1',
      name: 'New Table',
    fields: [
      { id: 'field-1', name: 'Name', type: 'text' },
      { id: 'field-2', name: 'Email', type: 'email' },
        { id: 'field-3', name: 'Age', type: 'number' },
      ],
    },
  uploadFile: {
    file: new File(['test'], 'test.csv', { type: 'text/csv' }),
    columns: ['Name', 'Email', 'Age'],
    rows: [
        ['John', '<EMAIL>', '25'],
        ['Jane', '<EMAIL>', '30'],
    ],
  },
  options: {
      hasHeader: true,
      delimiter: ',',
  },
};

const mockImportDataExisting = {
  ...mockImportData,
  isCreate: false,
  table: {
      ...mockImportData.table,
      id: 'table-1',
    },
  };

  const mockTablesQuery = {
    tables: [
      {
    id: 'table-1',
    name: 'Existing Table',
        fields: [
          { id: 'field-1', name: 'Name', type: 'text' },
          { id: 'field-2', name: 'Email', type: 'email' },
        ],
      },
    ],
  };

  const mockTablesMutation = {
    createTable: vi.fn(),
    updateTable: vi.fn(),
  };

  // Helper function to set import data for tests
  const setImportData = (data: typeof mockImportData) => {
    currentImportData = data;
  };

  beforeEach(async () => {
    vi.clearAllMocks();
    
    // Setup default mocks
    mockUseParams.mockReturnValue({ baseId: 'base-1' });
    mockWatch.mockReturnValue({});
    mockFormState.isSubmitting = false;
    
    // Setup handleSubmit mock to actually call the onSubmit function
    mockHandleSubmit.mockImplementation((onSubmit) => {
      return (e: any) => {
        e?.preventDefault?.();
        onSubmit({ fields: {} });
      };
    });

    // Setup API mocks
    const { useTablesQuery, useTablesMutation } = await import('@/hooks');
    const { CommonAPI, DataAPI } = await import('@/services/api');
    
    (useTablesQuery as any).mockReturnValue(mockTablesQuery);
    (useTablesMutation as any).mockReturnValue(mockTablesMutation);
    
    (CommonAPI.presignUrl as any).mockResolvedValue({
      isOk: () => true,
      value: { file: { path: 'test-path' }, uploadUrl: 'test-url' }
    });
    (CommonAPI.uploadFile as any).mockResolvedValue(undefined);
    (DataAPI.importTableData as any).mockResolvedValue({
      isOk: () => true,
      value: { success: true }
    });

    // Reset the mock importData to default
    mockSetImportData.mockClear();
    currentImportData = mockImportData;
  });

  it('should render field mapping controls for create new table', () => {
    renderWithRouter(
      <TableImportProvider
        onClose={mockOnClose}
        isShowLoadingOverlay={false}
        openLoadingOverlay={mockOpenLoadingOverlay}
        closeLoadingOverlay={mockCloseLoadingOverlay}
      >
        <FieldMappingScreen />
      </TableImportProvider>
    );
    
    expect(screen.getAllByTestId('field-mapping-control')).toHaveLength(3);
  });

  it('should render field mapping controls for existing table', () => {
    setImportData(mockImportDataExisting);
    
    renderWithRouter(
      <TableImportProvider
        onClose={mockOnClose}
        isShowLoadingOverlay={false}
        openLoadingOverlay={mockOpenLoadingOverlay}
        closeLoadingOverlay={mockCloseLoadingOverlay}
      >
        <FieldMappingScreen />
      </TableImportProvider>
    );
    
    expect(screen.getAllByTestId('field-mapping-control')).toHaveLength(3);
  });

  it('should render preview table when preview tab is selected', async () => {
      renderWithRouter(
        <TableImportProvider
          onClose={mockOnClose}
          isShowLoadingOverlay={false}
          openLoadingOverlay={mockOpenLoadingOverlay}
          closeLoadingOverlay={mockCloseLoadingOverlay}
        >
          <FieldMappingScreen />
        </TableImportProvider>
      );
      
      const previewTab = screen.getByText('csvImport.fieldMapping.tabPreview');
      fireEvent.click(previewTab);
      
    await waitFor(() => {
      expect(screen.getByTestId('preview-table')).toBeInTheDocument();
    });
    });

  it('should render all UI elements and handle interactions correctly', () => {
    renderWithRouter(
      <TableImportProvider
        onClose={mockOnClose}
        isShowLoadingOverlay={false}
        openLoadingOverlay={mockOpenLoadingOverlay}
        closeLoadingOverlay={mockCloseLoadingOverlay}
      >
        <FieldMappingScreen />
      </TableImportProvider>
    );
    
    // Check all UI elements are rendered
    expect(screen.getByText('csvImport.fieldMapping.fileTitle')).toBeInTheDocument();
    expect(screen.getByText('csvImport.fieldMapping.tableTitle')).toBeInTheDocument();
    expect(screen.getByText(/csvImport\.fieldMapping\.importingRecords/)).toBeInTheDocument();
    expect(screen.getByText('csvImport.cancelBtn')).toBeInTheDocument();
    expect(screen.getByText('csvImport.importBtn')).toBeInTheDocument();
    
    // Test cancel button interaction
    const cancelButton = screen.getByText('csvImport.cancelBtn');
    fireEvent.click(cancelButton);
    expect(mockOnClose).toHaveBeenCalled();
  });

  it('should handle form submission setup', () => {
      renderWithRouter(
        <TableImportProvider
          onClose={mockOnClose}
          isShowLoadingOverlay={false}
          openLoadingOverlay={mockOpenLoadingOverlay}
          closeLoadingOverlay={mockCloseLoadingOverlay}
        >
          <FieldMappingScreen />
        </TableImportProvider>
      );
      
    expect(mockFormState.isSubmitting).toBe(false);
  });

  it('should handle form submission with loading state', () => {
    mockFormState.isSubmitting = true;

      renderWithRouter(
        <TableImportProvider
          onClose={mockOnClose}
          isShowLoadingOverlay={false}
          openLoadingOverlay={mockOpenLoadingOverlay}
          closeLoadingOverlay={mockCloseLoadingOverlay}
        >
          <FieldMappingScreen />
        </TableImportProvider>
      );

      const importButton = screen.getByText('csvImport.importBtn');
    expect(importButton).toHaveAttribute('data-loading', 'true');
  });


  it('should sort columns when table fields exist', () => {
      renderWithRouter(
        <TableImportProvider
          onClose={mockOnClose}
          isShowLoadingOverlay={false}
          openLoadingOverlay={mockOpenLoadingOverlay}
          closeLoadingOverlay={mockCloseLoadingOverlay}
        >
          <FieldMappingScreen />
        </TableImportProvider>
      );

    expect(screen.getByText('csvImport.fieldMapping.tabFieldMapping')).toBeInTheDocument();
  });

  it('should not sort columns when no table fields exist', async () => {
    const emptyTablesQuery = { tables: [] };
    const { useTablesQuery } = await import('@/hooks');
    (useTablesQuery as any).mockReturnValue(emptyTablesQuery);

      renderWithRouter(
        <TableImportProvider
          onClose={mockOnClose}
          isShowLoadingOverlay={false}
          openLoadingOverlay={mockOpenLoadingOverlay}
          closeLoadingOverlay={mockCloseLoadingOverlay}
        >
          <FieldMappingScreen />
        </TableImportProvider>
      );

      expect(screen.getByText('csvImport.fieldMapping.tabFieldMapping')).toBeInTheDocument();
    });

  it('should handle case-insensitive column sorting', () => {
      renderWithRouter(
        <TableImportProvider
          onClose={mockOnClose}
          isShowLoadingOverlay={false}
          openLoadingOverlay={mockOpenLoadingOverlay}
          closeLoadingOverlay={mockCloseLoadingOverlay}
        >
          <FieldMappingScreen />
        </TableImportProvider>
      );

      expect(screen.getByText('csvImport.fieldMapping.tabFieldMapping')).toBeInTheDocument();
    });

  it('should render correct subtitle for create new table', () => {
    renderWithRouter(
      <TableImportProvider
        onClose={mockOnClose}
        isShowLoadingOverlay={false}
        openLoadingOverlay={mockOpenLoadingOverlay}
        closeLoadingOverlay={mockCloseLoadingOverlay}
      >
        <FieldMappingScreen />
      </TableImportProvider>
    );

    expect(screen.getByText('csvImport.fieldMapping.tableSubtitleCreateNew')).toBeInTheDocument();
    const fieldControls = screen.getAllByTestId('field-mapping-control');
    expect(fieldControls).toHaveLength(3);
    expect(fieldControls[0]).toHaveTextContent('Name');
    expect(fieldControls[1]).toHaveTextContent('Email');
    expect(fieldControls[2]).toHaveTextContent('Age');
  });

  it('should render correct subtitle for existing table', () => {
    setImportData(mockImportDataExisting);
    
    renderWithRouter(
      <TableImportProvider
        onClose={mockOnClose}
        isShowLoadingOverlay={false}
        openLoadingOverlay={mockOpenLoadingOverlay}
        closeLoadingOverlay={mockCloseLoadingOverlay}
      >
        <FieldMappingScreen />
      </TableImportProvider>
    );

    expect(screen.getByText('csvImport.fieldMapping.tableSubtitleExisting')).toBeInTheDocument();
    expect(screen.getAllByTestId('field-mapping-control')).toHaveLength(3);
  });

  it('should handle form submission for create new table', async () => {
      const mockCreateTable = vi.fn().mockResolvedValue({
        isOk: () => true,
        value: {
          data: {
            id: 'new-table-id',
            fields: [
              { id: 'field-1', name: 'Name', type: 'text' },
              { id: 'field-2', name: 'Email', type: 'email' },
            ]
          }
        }
      });

      const { useTablesMutation } = await import('@/hooks');
      (useTablesMutation as any).mockReturnValue({
        createTable: mockCreateTable,
        updateTable: vi.fn(),
      });


      renderWithRouter(
        <TableImportProvider
          onClose={mockOnClose}
          isShowLoadingOverlay={false}
          openLoadingOverlay={mockOpenLoadingOverlay}
          closeLoadingOverlay={mockCloseLoadingOverlay}
        >
          <FieldMappingScreen />
        </TableImportProvider>
      );

      const importButton = screen.getByText('csvImport.importBtn');
      fireEvent.click(importButton);

      await waitFor(() => {
        expect(mockOpenLoadingOverlay).toHaveBeenCalled();
        expect(mockCreateTable).toHaveBeenCalled();
      });
    });

  it('should handle form submission for existing table', async () => {
      setImportData(mockImportDataExisting);
      
      const mockUpdateTable = vi.fn().mockResolvedValue({
        isOk: () => true,
        value: {
          data: {
            fields: [
              { id: 'field-1', name: 'Name', type: 'text' },
              { id: 'field-2', name: 'Email', type: 'email' },
            ]
          }
        }
      });

      const { useTablesMutation } = await import('@/hooks');
      (useTablesMutation as any).mockReturnValue({
        createTable: vi.fn(),
        updateTable: mockUpdateTable,
      });


      renderWithRouter(
        <TableImportProvider
          onClose={mockOnClose}
          isShowLoadingOverlay={false}
          openLoadingOverlay={mockOpenLoadingOverlay}
          closeLoadingOverlay={mockCloseLoadingOverlay}
        >
          <FieldMappingScreen />
        </TableImportProvider>
      );

      const importButton = screen.getByText('csvImport.importBtn');
      fireEvent.click(importButton);

      await waitFor(() => {
        expect(mockOpenLoadingOverlay).toHaveBeenCalled();
        expect(mockUpdateTable).toHaveBeenCalled();
      });
    });

  it('should handle form submission error', async () => {
      const mockCreateTable = vi.fn().mockRejectedValue(new Error('API Error'));
      const mockNotificationsShow = vi.fn();

      const { useTablesMutation } = await import('@/hooks');
      const { notifications } = await import('@mantine/notifications');
      
      (useTablesMutation as any).mockReturnValue({
        createTable: mockCreateTable,
        updateTable: vi.fn(),
      });
      
      (notifications.show as any).mockImplementation(mockNotificationsShow);


      renderWithRouter(
        <TableImportProvider
          onClose={mockOnClose}
          isShowLoadingOverlay={false}
          openLoadingOverlay={mockOpenLoadingOverlay}
          closeLoadingOverlay={mockCloseLoadingOverlay}
        >
          <FieldMappingScreen />
        </TableImportProvider>
      );

      const importButton = screen.getByText('csvImport.importBtn');
      fireEvent.click(importButton);

      await waitFor(() => {
        expect(mockOpenLoadingOverlay).toHaveBeenCalled();
        expect(mockCloseLoadingOverlay).toHaveBeenCalled();
        expect(mockNotificationsShow).toHaveBeenCalledWith(
          expect.objectContaining({
            message: 'csvImport.notification.importError',
          })
        );
      });
    });

  it('should handle create table failure', async () => {
      const mockCreateTable = vi.fn().mockResolvedValue({
        isOk: () => false,
        value: null
      });

      const { useTablesMutation } = await import('@/hooks');
      (useTablesMutation as any).mockReturnValue({
        createTable: mockCreateTable,
        updateTable: vi.fn(),
      });


      renderWithRouter(
        <TableImportProvider
          onClose={mockOnClose}
          isShowLoadingOverlay={false}
          openLoadingOverlay={mockOpenLoadingOverlay}
          closeLoadingOverlay={mockCloseLoadingOverlay}
        >
          <FieldMappingScreen />
        </TableImportProvider>
      );

      const importButton = screen.getByText('csvImport.importBtn');
      fireEvent.click(importButton);

      await waitFor(() => {
        expect(mockOpenLoadingOverlay).toHaveBeenCalled();
        expect(mockCreateTable).toHaveBeenCalled();
      });
    });

  it('should handle update table failure', async () => {
      setImportData(mockImportDataExisting);
      
      const mockUpdateTable = vi.fn().mockResolvedValue({
        isOk: () => false,
        value: null
      });

      const { useTablesMutation } = await import('@/hooks');
      (useTablesMutation as any).mockReturnValue({
        createTable: vi.fn(),
        updateTable: mockUpdateTable,
      });

      renderWithRouter(
        <TableImportProvider
          onClose={mockOnClose}
          isShowLoadingOverlay={false}
          openLoadingOverlay={mockOpenLoadingOverlay}
          closeLoadingOverlay={mockCloseLoadingOverlay}
        >
          <FieldMappingScreen />
        </TableImportProvider>
      );

      const importButton = screen.getByText('csvImport.importBtn');
      fireEvent.click(importButton);

      await waitFor(() => {
        expect(mockOpenLoadingOverlay).toHaveBeenCalled();
        expect(mockUpdateTable).toHaveBeenCalled();
      });
    });

  it('should handle presignUrl failure', async () => {
      const mockCreateTable = vi.fn().mockResolvedValue({
        isOk: () => true,
        value: {
          data: {
            id: 'new-table-id',
            fields: [{ id: 'field-1', name: 'Name', type: 'text' }]
          }
        }
      });

      const { useTablesMutation } = await import('@/hooks');
      const { CommonAPI } = await import('@/services/api');
      
      (useTablesMutation as any).mockReturnValue({
        createTable: mockCreateTable,
        updateTable: vi.fn(),
      });
      
      (CommonAPI.presignUrl as any).mockResolvedValue({
        isOk: () => false,
        value: null
      });


      renderWithRouter(
        <TableImportProvider
          onClose={mockOnClose}
          isShowLoadingOverlay={false}
          openLoadingOverlay={mockOpenLoadingOverlay}
          closeLoadingOverlay={mockCloseLoadingOverlay}
        >
          <FieldMappingScreen />
        </TableImportProvider>
      );

      const importButton = screen.getByText('csvImport.importBtn');
      fireEvent.click(importButton);

      await waitFor(() => {
        expect(mockOpenLoadingOverlay).toHaveBeenCalled();
        expect(mockCreateTable).toHaveBeenCalled();
      });
    });

  it('should handle import data API failure', async () => {
      const mockCreateTable = vi.fn().mockResolvedValue({
        isOk: () => true,
        value: {
          data: {
            id: 'new-table-id',
            fields: [{ id: 'field-1', name: 'Name', type: 'text' }]
          }
        }
      });

      const { useTablesMutation } = await import('@/hooks');
      const { CommonAPI, DataAPI } = await import('@/services/api');
      
      (useTablesMutation as any).mockReturnValue({
        createTable: mockCreateTable,
        updateTable: vi.fn(),
      });
      
      (CommonAPI.presignUrl as any).mockResolvedValue({
        isOk: () => true,
        value: { file: { path: 'test-path' }, uploadUrl: 'test-url' }
      });
      
      (CommonAPI.uploadFile as any).mockResolvedValue(undefined);
      
      (DataAPI.importTableData as any).mockResolvedValue({
        isOk: () => false,
        value: null
      });


      renderWithRouter(
        <TableImportProvider
          onClose={mockOnClose}
          isShowLoadingOverlay={false}
          openLoadingOverlay={mockOpenLoadingOverlay}
          closeLoadingOverlay={mockCloseLoadingOverlay}
        >
          <FieldMappingScreen />
        </TableImportProvider>
      );

      const importButton = screen.getByText('csvImport.importBtn');
      fireEvent.click(importButton);

      await waitFor(() => {
        expect(mockOpenLoadingOverlay).toHaveBeenCalled();
        expect(mockCreateTable).toHaveBeenCalled();
      });
    });

  it('should not submit when baseId is missing', async () => {
      mockUseParams.mockReturnValue({ baseId: null });

      renderWithRouter(
        <TableImportProvider
          onClose={mockOnClose}
          isShowLoadingOverlay={false}
          openLoadingOverlay={mockOpenLoadingOverlay}
          closeLoadingOverlay={mockCloseLoadingOverlay}
        >
          <FieldMappingScreen />
        </TableImportProvider>
      );

      const importButton = screen.getByText('csvImport.importBtn');
      fireEvent.click(importButton);

      expect(mockOpenLoadingOverlay).not.toHaveBeenCalled();
    });

  it('should not submit when uploadedFile is missing', async () => {
      const mockImportDataNoFile = {
        ...mockImportData,
        uploadFile: {
          ...mockImportData.uploadFile,
          file: null as any
        }
      };
      
      setImportData(mockImportDataNoFile);

      renderWithRouter(
        <TableImportProvider
          onClose={mockOnClose}
          isShowLoadingOverlay={false}
          openLoadingOverlay={mockOpenLoadingOverlay}
          closeLoadingOverlay={mockCloseLoadingOverlay}
        >
          <FieldMappingScreen />
        </TableImportProvider>
      );

      const importButton = screen.getByText('csvImport.importBtn');
      fireEvent.click(importButton);

      expect(mockOpenLoadingOverlay).not.toHaveBeenCalled();
    });

  it('should update table columns when switching to preview tab', async () => {
      setImportData(mockImportDataExisting);
      
      mockWatch.mockReturnValue({
        fields: {
          'Name': { id: 'field-1', type: 'text' },
          'Email': { id: 'field-2', type: 'email' }
        }
      });

      renderWithRouter(
        <TableImportProvider
          onClose={mockOnClose}
          isShowLoadingOverlay={false}
          openLoadingOverlay={mockOpenLoadingOverlay}
          closeLoadingOverlay={mockCloseLoadingOverlay}
        >
          <FieldMappingScreen />
        </TableImportProvider>
      );

      const previewTab = screen.getByText('csvImport.fieldMapping.tabPreview');
      fireEvent.click(previewTab);

      await waitFor(() => {
        expect(screen.getByTestId('preview-table')).toBeInTheDocument();
      });
    });

  it('should not update table columns when switching to field mapping tab', async () => {
      setImportData(mockImportDataExisting);
      
      renderWithRouter(
        <TableImportProvider
          onClose={mockOnClose}
          isShowLoadingOverlay={false}
          openLoadingOverlay={mockOpenLoadingOverlay}
          closeLoadingOverlay={mockCloseLoadingOverlay}
        >
          <FieldMappingScreen />
        </TableImportProvider>
      );

      const fieldMappingTab = screen.getByText('csvImport.fieldMapping.tabFieldMapping');
      fireEvent.click(fieldMappingTab);

      // Should not trigger updateTableOnTabChange logic
      expect(screen.getByText('csvImport.fieldMapping.tabFieldMapping')).toBeInTheDocument();
    });

  it('should create new fields payload for new table submission', async () => {
      const mockCreateTable = vi.fn().mockResolvedValue({
        isOk: () => true,
        value: {
          data: {
            id: 'new-table-id',
            fields: [
              { id: 'generated-field-1', name: 'Name', type: 'text' },
              { id: 'generated-field-2', name: 'Age', type: 'number' },
            ]
          }
        }
      });

      // Mock handleSubmit to pass fields data that will trigger lines 167-170
      mockHandleSubmit.mockImplementation((onSubmit) => {
        return (e: any) => {
          e?.preventDefault?.();
          onSubmit({
            fields: {
              'Name': { type: 'text' }, // No ID = new field
              'Age': { type: 'number' }, // No ID = new field
            }
          });
        };
      });

      const { useTablesMutation } = await import('@/hooks');
      const { CommonAPI, DataAPI } = await import('@/services/api');
      
      (useTablesMutation as any).mockReturnValue({
        createTable: mockCreateTable,
        updateTable: vi.fn(),
      });

      (CommonAPI.presignUrl as any).mockResolvedValue({
        isOk: () => true,
        value: { file: { path: 'test-path' }, uploadUrl: 'test-url' }
      });
      (CommonAPI.uploadFile as any).mockResolvedValue(true);
      (DataAPI.importTableData as any).mockResolvedValue({ isOk: () => true });

      renderWithRouter(
        <TableImportProvider
          onClose={mockOnClose}
          isShowLoadingOverlay={false}
          openLoadingOverlay={mockOpenLoadingOverlay}
          closeLoadingOverlay={mockCloseLoadingOverlay}
        >
          <FieldMappingScreen />
        </TableImportProvider>
      );

      const importButton = screen.getByText('csvImport.importBtn');
      fireEvent.click(importButton);

      await waitFor(() => {
        expect(mockCreateTable).toHaveBeenCalledWith('base-1', {
          name: 'New Table',
          fields: [
            { name: 'Name', type: 'text', options: expect.any(Object) },
            { name: 'Age', type: 'number', options: expect.any(Object) },
          ]
        });
      });
    });

  it('should handle existing fields mapping for existing table submission', async () => {
      setImportData(mockImportDataExisting);
      
      const mockUpdateTable = vi.fn().mockResolvedValue({
        isOk: () => true,
        value: {
          data: {
            fields: [
              { id: 'field-1', name: 'Name', type: 'text' },
              { id: 'field-2', name: 'Email', type: 'email' },
              { id: 'field-3', name: 'Age', type: 'number' },
            ]
          }
        }
      });

      // Mock handleSubmit to pass both existing and new fields to trigger lines 194-196
      mockHandleSubmit.mockImplementation((onSubmit) => {
        return (e: any) => {
          e?.preventDefault?.();
          onSubmit({
            fields: {
              'Name': { id: 'field-1', type: 'text' }, // Existing field with ID
              'Email': { id: 'field-2', type: 'email' }, // Existing field with ID
              'Age': { type: 'number' }, // New field without ID
            }
          });
        };
      });

      const { useTablesMutation } = await import('@/hooks');
      const { CommonAPI, DataAPI } = await import('@/services/api');
      
      (useTablesMutation as any).mockReturnValue({
        createTable: vi.fn(),
        updateTable: mockUpdateTable,
      });

      (CommonAPI.presignUrl as any).mockResolvedValue({
        isOk: () => true,
        value: { file: { path: 'test-path' }, uploadUrl: 'test-url' }
      });
      (CommonAPI.uploadFile as any).mockResolvedValue(true);
      (DataAPI.importTableData as any).mockResolvedValue({ isOk: () => true });

      renderWithRouter(
        <TableImportProvider
          onClose={mockOnClose}
          isShowLoadingOverlay={false}
          openLoadingOverlay={mockOpenLoadingOverlay}
          closeLoadingOverlay={mockCloseLoadingOverlay}
        >
          <FieldMappingScreen />
        </TableImportProvider>
      );

      const importButton = screen.getByText('csvImport.importBtn');
      fireEvent.click(importButton);

      await waitFor(() => {
        expect(mockUpdateTable).toHaveBeenCalled();
      });
    });

});
