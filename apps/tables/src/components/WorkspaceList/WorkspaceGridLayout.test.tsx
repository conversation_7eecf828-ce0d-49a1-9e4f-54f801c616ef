import { render, screen } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { MantineProvider } from '@mantine/core';
import { emotionTransform, MantineEmotionProvider } from '@mantine/emotion';
import { GridLayout } from './WorkspaceGridLayout';
import type { TableBase } from '@/types';

vi.mock('@/constants', () => ({
  BASE_PATH: '/tables/',
  HEADER_GRADIENT_OVERLAY_URL: 'images/gradient-overlay.png',
}));

// Mock react-router-dom
vi.mock('react-router-dom', () => ({
  Link: ({ children, to, className, ...props }: any) => (
    <a href={to} className={className} data-testid="base-link" {...props}>
      {children}
    </a>
  ),
}));

// Mock @tolgee/react
const mockT = vi.fn((key: string) => key);
vi.mock('@tolgee/react', () => ({
  useTranslate: () => ({ t: mockT }),
}));

// Mock @resola-ai/ui
vi.mock('@resola-ai/ui', () => ({
  CustomImage: ({ url, alt, className, ...props }: any) => (
    <img src={url} alt={alt} className={className} data-testid="custom-image" {...props} />
  ),
}));

// Mock Mantine components
vi.mock('@mantine/core', async (importOriginal) => {
  const actual = await importOriginal();
  return {
    ...(actual as any),
    Avatar: ({ children, src, size, radius, ...props }: any) => (
      <div data-testid="avatar" data-src={src} data-size={size} data-radius={radius} {...props}>
        {children}
      </div>
    ),
    Center: ({ children, className, ...props }: any) => (
      <div className={className} data-testid="center" {...props}>
        {children}
      </div>
    ),
    Flex: ({ children, className, ...props }: any) => (
      <div className={className} data-testid="flex" {...props}>
        {children}
      </div>
    ),
    LoadingOverlay: ({ visible, sx, ...props }: any) => (
      visible ? <div data-testid="loading-overlay" style={sx} {...props} /> : null
    ),
    SimpleGrid: ({ children, className, cols, spacing, verticalSpacing, ...props }: any) => (
      <div 
        className={className} 
        data-testid="simple-grid"
        data-cols={JSON.stringify(cols)}
        data-spacing={JSON.stringify(spacing)}
        data-vertical-spacing={JSON.stringify(verticalSpacing)}
        {...props}
      >
        {children}
      </div>
    ),
    Text: ({ children, className, ...props }: any) => (
      <span className={className} data-testid="text" {...props}>
        {children}
      </span>
    ),
    rem: (value: number) => `${value}px`,
  };
});

// Mock @mantine/emotion to force execution of all style code
vi.mock('@mantine/emotion', async (importOriginal) => {
  const actual = await importOriginal();
  return {
    ...(actual as any),
    createStyles: (fn: any) => {
      // Execute the style function to force coverage of all style definitions
      const mockTheme = {
        colors: {
          decaLight: [null, null, '#f0f0f0'],
          decaGrey: [null, null, null, null, null, null, null, null, null, '#999999'],
          decaDark: [null, null, null, null, null, null, null, '#333333'],
          decaNavy: ['#e6f3ff'],
          decaBlue: ['#e6f3ff'],
          decaViolet: ['#f3e8ff'],
          decaGreen: ['#e6f7e6'],
          decaTeal: ['#e6f7f7'],
          decaPink: ['#fce7f3'],
          decaYellow: ['#fef3c7'],
          decaRed: ['#fee2e2'],
          decaPurple: ['#f3e8ff'],
        },
        spacing: { xs: 10, sm: 12, md: 16, lg: 20, xl: 24 },
        radius: { xl: 16 },
        fontSizes: { xs: 12, sm: 14, md: 16, lg: 18, xl: 20 },
        lineHeights: { xs: 1.2, sm: 1.4, md: 1.6, lg: 1.8, xl: 2 },
        fontWeights: { normal: 400, medium: 500, semibold: 600, bold: 700 },
        shadows: { xs: '0 1px 3px rgba(0, 0, 0, 0.12)', sm: '0 1px 3px rgba(0, 0, 0, 0.12)' },
        breakpoints: { xs: '576px', sm: '768px', md: '992px', lg: '1200px', xl: '1400px' },
        other: {},
        fn: {
          smallerThan: (breakpoint: string) => `@media (max-width: ${breakpoint})`,
          largerThan: (breakpoint: string) => `@media (min-width: ${breakpoint})`,
          lighten: (color: string, _alpha: number) => color,
          darken: (color: string, _alpha: number) => color,
          rgba: (color: string, _alpha: number) => color,
        },
      };
      
      try {
        fn(mockTheme);
      } catch (error) {
        // Ignore errors, we just want to execute the function
      }
      
      return () => ({
        classes: {
          grid: 'grid-class',
          card: 'card-class',
          cardHeader: 'card-header-class',
          cardHeaderOverlay: 'card-header-overlay-class',
          cardBody: 'card-body-class',
          cardIcon: 'card-icon-class',
          cardBaseName: 'card-base-name-class',
          cardBaseActionMenuIcon: 'card-base-action-menu-icon-class',
          blue: 'blue-class',
          violet: 'violet-class',
          navy: 'navy-class',
          green: 'green-class',
          teal: 'teal-class',
          pink: 'pink-class',
          yellow: 'yellow-class',
          red: 'red-class',
          purple: 'purple-class',
        },
        cx: (...args: any[]) => args.filter(Boolean).join(' '),
      });
    },
  };
});

// Mock Common components
vi.mock('../Common', () => ({
  TableBaseIcon: ({ color, icon, className, ...props }: any) => (
    <div data-testid="table-base-icon" data-color={color} data-icon={icon} className={className} {...props} />
  ),
}));

// Mock WorkspaceMenuIcon
vi.mock('./WorkspaceMenuIcon', () => ({
  WorkspaceActionIcon: ({ onClick, ...props }: any) => (
    <button 
      data-testid="workspace-action-icon" 
      onClick={(e) => onClick?.({ event: e, position: { x: 100, y: 100 } })}
      {...props}
    >
      Menu
    </button>
  ),
}));

// Create a Mantine wrapper for proper theme support
const MantineWrapper = ({ children }: { children: React.ReactNode }) => (
  <MantineProvider stylesTransform={emotionTransform}>
    <MantineEmotionProvider>{children}</MantineEmotionProvider>
  </MantineProvider>
);

const renderWithMantine = (ui: React.ReactNode) => {
  return render(<MantineWrapper>{ui}</MantineWrapper>);
};

describe('WorkspaceGridLayout', () => {
  const mockOnMenuActionClick = vi.fn();
  
  const mockBases: TableBase[] = [
    {
      id: 'base1',
      name: 'Test Base 1',
      description: 'Test base 1 description',
      owner: {
        id: 'user1',
        name: 'John Doe',
        email: '<EMAIL>',
        picture: 'https://example.com/avatar1.jpg',
      },
      options: {
        theme: 'blue',
        icon: 'table',
      },
      createdAt: new Date('2024-01-01'),
      updatedAt: new Date('2024-01-01'),
      deletedAt: '',
      permanentlyDelete: false,
    },
    {
      id: 'base2',
      name: 'Test Base 2',
      description: 'Test base 2 description',
      owner: {
        id: 'user2',
        name: 'Jane Smith',
        email: '<EMAIL>',
        picture: 'https://example.com/avatar2.jpg',
      },
      options: {
        theme: 'violet',
        icon: 'database',
      },
      createdAt: new Date('2024-01-02'),
      updatedAt: new Date('2024-01-02'),
      deletedAt: '',
      permanentlyDelete: false,
    },
  ];

  const defaultProps = {
    bases: mockBases,
    isLoading: false,
    onMenuActionClick: mockOnMenuActionClick,
  };

  beforeEach(() => {
    vi.clearAllMocks();
    mockT.mockImplementation((key: string) => key);
  });

  describe('Rendering', () => {
    it('renders grid layout with bases', () => {
      renderWithMantine(<GridLayout {...defaultProps} />);
      
      expect(screen.getByTestId('simple-grid')).toBeInTheDocument();
      expect(screen.getAllByTestId('base-link')).toHaveLength(2);
      expect(screen.getAllByTestId('table-base-icon')).toHaveLength(2);
    });

    it('renders empty grid when no bases provided', () => {
      renderWithMantine(<GridLayout {...defaultProps} bases={[]} />);
      
      expect(screen.getByTestId('simple-grid')).toBeInTheDocument();
      expect(screen.queryByTestId('base-link')).not.toBeInTheDocument();
    });

    it('renders empty grid when bases is undefined', () => {
      renderWithMantine(<GridLayout {...defaultProps} bases={undefined} />);
      
      expect(screen.getByTestId('simple-grid')).toBeInTheDocument();
      expect(screen.queryByTestId('base-link')).not.toBeInTheDocument();
    });

    it('applies correct grid props', () => {
      renderWithMantine(<GridLayout {...defaultProps} />);
      
      const grid = screen.getByTestId('simple-grid');
      expect(grid).toHaveClass('grid-class');
      expect(grid).toHaveAttribute('data-cols', JSON.stringify({ xs: 2, md: 3, xl: 4 }));
      expect(grid).toHaveAttribute('data-spacing', JSON.stringify({ xs: 'sm', xl: '24px' }));
      expect(grid).toHaveAttribute('data-vertical-spacing', JSON.stringify({ xs: 'sm', xl: '24px' }));
    });

    it('applies custom className', () => {
      renderWithMantine(<GridLayout {...defaultProps} className="custom-class" />);
      
      const grid = screen.getByTestId('simple-grid');
      expect(grid).toHaveClass('grid-class custom-class');
    });
  });

  describe('Loading States', () => {
    it('shows loading overlay when isLoading is true', () => {
      renderWithMantine(<GridLayout {...defaultProps} isLoading={true} />);
      
      expect(screen.getByTestId('loading-overlay')).toBeInTheDocument();
    });

    it('hides loading overlay when isLoading is false', () => {
      renderWithMantine(<GridLayout {...defaultProps} isLoading={false} />);
      
      expect(screen.queryByTestId('loading-overlay')).not.toBeInTheDocument();
    });

    it('applies correct loading overlay styles', () => {
      renderWithMantine(<GridLayout {...defaultProps} isLoading={true} />);
      
      const overlay = screen.getByTestId('loading-overlay');
      expect(overlay).toHaveStyle({ position: 'fixed', inset: '0' });
    });
  });

  describe('Base Cards', () => {
    it('renders base cards with correct structure', () => {
      renderWithMantine(<GridLayout {...defaultProps} />);
      
      const links = screen.getAllByTestId('base-link');
      expect(links).toHaveLength(2);
      
      // Check first base link
      expect(links[0]).toHaveAttribute('href', '/tables/bases/base1');
      expect(links[0]).toHaveClass('card-class');
    });

    it('renders base card header with theme class', () => {
      renderWithMantine(<GridLayout {...defaultProps} />);
      
      const cardHeaders = screen.getAllByTestId('custom-image');
      expect(cardHeaders).toHaveLength(2);
      expect(cardHeaders[0]).toHaveAttribute('alt', 'card-gradient');
    });

    it('renders table base icons with correct props', () => {
      renderWithMantine(<GridLayout {...defaultProps} />);
      
      const icons = screen.getAllByTestId('table-base-icon');
      expect(icons[0]).toHaveAttribute('data-color', 'blue');
      expect(icons[0]).toHaveAttribute('data-icon', 'table');
      expect(icons[1]).toHaveAttribute('data-color', 'violet');
      expect(icons[1]).toHaveAttribute('data-icon', 'database');
    });

    it('renders base names', () => {
      renderWithMantine(<GridLayout {...defaultProps} />);
      
      expect(screen.getByText('Test Base 1')).toBeInTheDocument();
      expect(screen.getByText('Test Base 2')).toBeInTheDocument();
    });

    it('renders owner avatars and names', () => {
      renderWithMantine(<GridLayout {...defaultProps} />);
      
      const avatars = screen.getAllByTestId('avatar');
      expect(avatars[0]).toHaveAttribute('data-src', 'https://example.com/avatar1.jpg');
      expect(avatars[1]).toHaveAttribute('data-src', 'https://example.com/avatar2.jpg');
      
      expect(screen.getByText('John Doe')).toBeInTheDocument();
      expect(screen.getByText('Jane Smith')).toBeInTheDocument();
    });

    it('handles missing owner name with translation', () => {
      const basesWithoutOwnerName = [
        {
          ...mockBases[0],
          owner: {
            id: 'user1',
            name: '',
            email: '<EMAIL>',
            picture: 'https://example.com/avatar1.jpg',
          },
        },
      ];
      
      renderWithMantine(<GridLayout {...defaultProps} bases={basesWithoutOwnerName} />);
      
      expect(screen.getByText('table.anonymous')).toBeInTheDocument();
    });

    it('handles missing owner picture', () => {
      const basesWithoutOwnerPicture = [
        {
          ...mockBases[0],
          owner: {
            id: 'user1',
            name: 'John Doe',
            email: '<EMAIL>',
            picture: '',
          },
        },
      ];
      
      renderWithMantine(<GridLayout {...defaultProps} bases={basesWithoutOwnerPicture} />);
      
      const avatar = screen.getByTestId('avatar');
      expect(avatar).toHaveAttribute('data-src', '');
    });
  });

  describe('Menu Actions', () => {
    it('renders workspace action icons', () => {
      renderWithMantine(<GridLayout {...defaultProps} />);
      
      const actionIcons = screen.getAllByTestId('workspace-action-icon');
      expect(actionIcons).toHaveLength(2);
    });

    it('calls onMenuActionClick when action icon is clicked', async () => {
      renderWithMantine(<GridLayout {...defaultProps} />);
      
      const actionIcon = screen.getAllByTestId('workspace-action-icon')[0];
      await userEvent.click(actionIcon);
      
      expect(mockOnMenuActionClick).toHaveBeenCalledWith({
        base: mockBases[0],
        event: expect.any(Object),
        position: { x: 100, y: 100 },
      });
    });

    it('does not call onMenuActionClick when callback is not provided', async () => {
      renderWithMantine(<GridLayout {...defaultProps} onMenuActionClick={undefined} />);
      
      const actionIcon = screen.getAllByTestId('workspace-action-icon')[0];
      await userEvent.click(actionIcon);
      
      expect(mockOnMenuActionClick).not.toHaveBeenCalled();
    });

  });

  describe('Theme Variants', () => {
    const themeVariants = [
      'blue', 'violet', 'navy', 'green', 'teal', 'pink', 'yellow', 'red', 'purple'
    ];

    themeVariants.forEach(theme => {
      it(`renders ${theme} theme variant`, () => {
        const baseWithTheme = [
          {
            ...mockBases[0],
            options: {
              theme: theme as any,
              icon: 'table',
            },
          },
        ];
        
        renderWithMantine(<GridLayout {...defaultProps} bases={baseWithTheme} />);
        
        const link = screen.getByTestId('base-link');
        expect(link).toHaveClass('card-class');
        
        const icon = screen.getByTestId('table-base-icon');
        expect(icon).toHaveAttribute('data-color', theme);
      });
    });

    it('handles missing theme gracefully', () => {
      const baseWithoutTheme = [
        {
          ...mockBases[0],
          options: {
            theme: 'blue',
            icon: 'table',
          },
        },
      ];
      
      renderWithMantine(<GridLayout {...defaultProps} bases={baseWithoutTheme} />);
      
      const link = screen.getByTestId('base-link');
      expect(link).toHaveClass('card-class');
    });
  });

  describe('Translation', () => {
    it('calls translation function with correct namespace', () => {
      const basesWithoutOwnerName = [
        {
          ...mockBases[0],
          owner: {
            id: 'user1',
            name: '',
            email: '<EMAIL>',
            picture: 'https://example.com/avatar1.jpg',
          },
        },
      ];
      
      renderWithMantine(<GridLayout {...defaultProps} bases={basesWithoutOwnerName} />);
      
      expect(mockT).toHaveBeenCalledWith('table.anonymous');
    });

    it('displays translated text for anonymous users', () => {
      mockT.mockReturnValue('Anonymous User');
      
      const basesWithoutOwnerName = [
        {
          ...mockBases[0],
          owner: {
            id: 'user1',
            name: '',
            email: '<EMAIL>',
            picture: 'https://example.com/avatar1.jpg',
          },
        },
      ];
      
      renderWithMantine(<GridLayout {...defaultProps} bases={basesWithoutOwnerName} />);
      
      expect(screen.getByText('Anonymous User')).toBeInTheDocument();
    });
  });

  describe('Component Props', () => {
    it('passes through SimpleGrid props', () => {
      renderWithMantine(
        <GridLayout 
          {...defaultProps} 
          data-testid="custom-grid"
          id="test-grid"
        />
      );
      
      const grid = screen.getByTestId('custom-grid');
      expect(grid).toHaveAttribute('id', 'test-grid');
    });

    it('handles all required props correctly', () => {
      const props = {
        bases: mockBases,
        isLoading: true,
        onMenuActionClick: mockOnMenuActionClick,
      };
      
      renderWithMantine(<GridLayout {...props} />);
      
      expect(screen.getByTestId('simple-grid')).toBeInTheDocument();
      expect(screen.getByTestId('loading-overlay')).toBeInTheDocument();
      expect(screen.getAllByTestId('base-link')).toHaveLength(2);
    });

    it('handles optional props', () => {
      const props = {
        bases: mockBases,
        isLoading: false,
      };
      
      renderWithMantine(<GridLayout {...props} />);
      
      expect(screen.getByTestId('simple-grid')).toBeInTheDocument();
      expect(screen.queryByTestId('loading-overlay')).not.toBeInTheDocument();
    });
  });

  describe('Edge Cases', () => {
    it('handles empty bases array', () => {
      renderWithMantine(<GridLayout {...defaultProps} bases={[]} />);
      
      expect(screen.getByTestId('simple-grid')).toBeInTheDocument();
      expect(screen.queryByTestId('base-link')).not.toBeInTheDocument();
    });

    it('handles bases with missing options', () => {
      const basesWithMissingOptions = [
        {
          ...mockBases[0],
          options: { theme: 'blue', icon: 'table' },
        },
      ];
      
      renderWithMantine(<GridLayout {...defaultProps} bases={basesWithMissingOptions} />);
      
      expect(screen.getByTestId('base-link')).toBeInTheDocument();
    });

    it('handles bases with missing owner', () => {
      const basesWithMissingOwner = [
        {
          id: 'base1',
          name: 'Test Base 1',
          owner: { id: 'user-1', name: '', email: '<EMAIL>', picture: '' },
          options: { theme: 'blue', icon: 'table' },
          createdAt: new Date(),
          updatedAt: new Date(),
          deletedAt: '',
          permanentlyDelete: false,
          description: 'Test base description',
        },
      ];
      
      renderWithMantine(<GridLayout {...defaultProps} bases={basesWithMissingOwner} />);
      
      expect(screen.getByText('table.anonymous')).toBeInTheDocument();
    });

    it('handles very long base names', () => {
      const basesWithLongName = [
        {
          ...mockBases[0],
          name: 'This is a very long base name that should be truncated with ellipsis when it exceeds the container width',
        },
      ];
      
      renderWithMantine(<GridLayout {...defaultProps} bases={basesWithLongName} />);
      
      expect(screen.getByText('This is a very long base name that should be truncated with ellipsis when it exceeds the container width')).toBeInTheDocument();
    });
  });

  describe('Accessibility', () => {
    it('renders accessible links', () => {
      renderWithMantine(<GridLayout {...defaultProps} />);
      
      const links = screen.getAllByTestId('base-link');
      expect(links[0]).toHaveAttribute('href', '/tables/bases/base1');
      expect(links[1]).toHaveAttribute('href', '/tables/bases/base2');
    });

    it('renders accessible images', () => {
      renderWithMantine(<GridLayout {...defaultProps} />);
      
      const images = screen.getAllByTestId('custom-image');
      expect(images[0]).toHaveAttribute('alt', 'card-gradient');
    });

    it('renders accessible avatars', () => {
      renderWithMantine(<GridLayout {...defaultProps} />);
      
      const avatars = screen.getAllByTestId('avatar');
      expect(avatars[0]).toHaveAttribute('data-src', 'https://example.com/avatar1.jpg');
    });
  });
});
