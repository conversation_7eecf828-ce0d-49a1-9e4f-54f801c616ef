import { renderWithRouter } from '@/utils/test';
import { screen } from '@testing-library/react';
import { vi, describe, it, expect, beforeEach } from 'vitest';
import { WorkspaceActionMenu, useWorkspaceActionMenu } from './WorkspaceMenu';

let capturedOnClickHandlers: Function[] = [];

vi.mock('@/constants', () => ({
  WORKSPACE_MENU_ANIMATION_NAME: 'fade',
  WORKSPACE_MENU_ID: 'workspace-menu',
}));

vi.mock('@mantine/core', () => ({
  Portal: ({ children }: any) => <div data-testid="portal">{children}</div>,
  Text: ({ children }: any) => <span data-testid="text">{children}</span>,
}));

vi.mock('react-contexify', () => ({
  Menu: ({ children, ...props }: any) => <div data-testid="menu" {...props}>{children}</div>,
  Item: ({ children, onClick, ...props }: any) => {
    if (onClick) {
      capturedOnClickHandlers.push(onClick);
    }
    return (
      <div data-testid="menu-item" data-onclick-index={capturedOnClickHandlers.length - 1} {...props}>
        {children}
      </div>
    );
  },
  Separator: () => <div data-testid="menu-separator">Separator</div>,
  useContextMenu: vi.fn(() => ({
    show: vi.fn(),
    hide: vi.fn(),
  })),
}));

vi.mock('./useMenuStyles', () => ({
  useMenuStyles: () => ({
    classes: {
      menu: 'mock-menu-class',
      item: 'mock-item-class',
      itemRed: 'mock-item-red-class',
    },
    cx: (...classes: string[]) => classes.filter(Boolean).join(' '),
  }),
}));

describe('WorkspaceActionMenu', () => {
  const mockOnEdit = vi.fn();
  const mockOnDuplicate = vi.fn();
  const mockOnDelete = vi.fn();

  beforeEach(() => {
    vi.clearAllMocks();
    capturedOnClickHandlers = [];
  });

  it('displays rename, duplicate, and delete menu options', () => {
    renderWithRouter(
      <WorkspaceActionMenu
        onOpenEditTableBaseModal={mockOnEdit}
        onOpenDuplicateTableBaseModal={mockOnDuplicate}
        onRemoveTableBase={mockOnDelete}
      />
    );

    expect(screen.getAllByTestId('menu-item')).toHaveLength(3);
    expect(screen.getByTestId('menu-separator')).toBeInTheDocument();
    expect(screen.getByText('actionMenu.rename')).toBeInTheDocument();
    expect(screen.getByText('actionMenu.duplicate')).toBeInTheDocument();
    expect(screen.getByText('actionMenu.delete')).toBeInTheDocument();
  });

  it('calls appropriate callback when menu items are clicked', () => {
    const tableBase = { id: 'test-base-id', name: 'Test Base' };
    
    renderWithRouter(
      <WorkspaceActionMenu
        onOpenEditTableBaseModal={mockOnEdit}
        onOpenDuplicateTableBaseModal={mockOnDuplicate}
        onRemoveTableBase={mockOnDelete}
      />
    );

    const mockEvent = { props: { base: tableBase } };
    
    // Test edit action
    const editHandler = capturedOnClickHandlers[0];
    editHandler?.(mockEvent);
    expect(mockOnEdit).toHaveBeenCalledWith(tableBase);

    // Test duplicate action
    const duplicateHandler = capturedOnClickHandlers[1];
    duplicateHandler?.(mockEvent);
    expect(mockOnDuplicate).toHaveBeenCalledWith(tableBase);

    // Test delete action
    const deleteHandler = capturedOnClickHandlers[2];
    deleteHandler?.(mockEvent);
    expect(mockOnDelete).toHaveBeenCalledWith(tableBase);
  });

  it('handles missing callbacks gracefully', () => {
    renderWithRouter(
      <WorkspaceActionMenu
        onOpenEditTableBaseModal={undefined as any}
        onOpenDuplicateTableBaseModal={undefined as any}
        onRemoveTableBase={undefined as any}
      />
    );

    const mockEvent = { props: { base: { id: 'test', name: 'Test' } } };
    const editHandler = capturedOnClickHandlers[0];
    editHandler?.(mockEvent);

    expect(mockOnEdit).not.toHaveBeenCalled();
  });

  it('handles missing event data gracefully', () => {
    renderWithRouter(
      <WorkspaceActionMenu
        onOpenEditTableBaseModal={mockOnEdit}
        onOpenDuplicateTableBaseModal={mockOnDuplicate}
        onRemoveTableBase={mockOnDelete}
      />
    );

    const editHandler = capturedOnClickHandlers[0];
    editHandler?.({ props: undefined });
    editHandler?.({ props: { base: undefined } });

    expect(mockOnEdit).not.toHaveBeenCalled();
  });
});

describe('useWorkspaceActionMenu', () => {
  it('provides show and hide functions for context menu', () => {
    const result = useWorkspaceActionMenu();
    
    expect(result).toEqual({
      show: expect.any(Function),
      hide: expect.any(Function),
    });
  });
});
