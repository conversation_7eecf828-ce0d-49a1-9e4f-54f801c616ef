import { renderWithRouter } from '@/utils/test';
import { screen } from '@testing-library/react';
import { vi, describe, it, expect, beforeEach } from 'vitest';
import { TableTrashLayout } from './WorkspaceTableTrashLayout';

const mockTableBase = {
  id: 'base-1',
  name: 'Test Base',
  description: 'Test base description',
  tables: [],
  createdAt: new Date('2023-01-01T00:00:00Z'),
  updatedAt: new Date('2023-01-02T00:00:00Z'),
  deletedAt: '2023-01-03T00:00:00Z',
  permanentlyDelete: false,
  owner: {
    id: 'user-1',
    name: '<PERSON>',
    email: '<EMAIL>',
    picture: 'https://example.com/avatar.jpg',
  },
  options: {
    icon: 'database',
    theme: 'blue',
  },
};

vi.mock('@/constants', () => ({
  BASE_PATH: '/tables/',
  DEFAULT_TABLE_PAGINATION_LIMIT: '10',
}));

vi.mock('@mantine/core', () => ({
  Avatar: ({ src, size, radius, ...props }: any) => (
    <img 
      data-testid="avatar" 
      src={src} 
      style={{ width: size, height: size, borderRadius: radius === 'xl' ? '50%' : '0' }} 
      {...props} 
    />
  ),
  Box: ({ children, ...props }: any) => <div data-testid="box" {...props}>{children}</div>,
  Center: ({ children }: any) => <div data-testid="center">{children}</div>,
  Group: ({ children, ...props }: any) => <div data-testid="group" {...props}>{children}</div>,
  Skeleton: ({ h, ...props }: any) => <div data-testid="skeleton" style={{ height: h }} {...props} />,
  Table: ({ children, ...props }: any) => <table data-testid="table" {...props}>{children}</table>,
  Text: ({ children, ...props }: any) => <span data-testid="text" {...props}>{children}</span>,
  rem: (value: number) => `${value}px`,
}));

vi.mock('@mantine/emotion', () => ({
  createStyles: () => () => ({
    classes: {
      table: 'mock-table-class',
      name: 'mock-name-class',
      owner: 'mock-owner-class',
      deletedAt: 'mock-deleted-at-class',
      permanentlyDeleted: 'mock-permanently-deleted-class',
      action: 'mock-action-class',
      baseNameTd: 'mock-base-name-td-class',
    },
    cx: (...classes: string[]) => classes.filter(Boolean).join(' '),
  }),
}));

vi.mock('dayjs', () => ({
  default: (date: string) => ({
    format: (format: string) => {
      if (format === 'YYYY/MM/DD HH:mm') {
        return '2023/01/03 00:00';
      }
      return date;
    },
  }),
}));

vi.mock('react-router-dom', () => ({
  Link: ({ to, children, className }: any) => (
    <a data-testid="link" href={to} className={className}>
      {children}
    </a>
  ),
  MemoryRouter: ({ children }: any) => <div data-testid="memory-router">{children}</div>,
}));

vi.mock('../Common', () => ({
  TableBaseIcon: ({ icon, color }: any) => (
    <div data-testid="table-base-icon" data-icon={icon} data-color={color}>
      Icon
    </div>
  ),
  PernamentlyDeletedDate: ({ date }: any) => (
    <div data-testid="permanently-deleted-date" data-date={date}>
      Permanently Deleted Date
    </div>
  ),
}));

vi.mock('./WorkspaceMenuIcon', () => ({
  WorkspaceActionIcon: ({ onClick }: any) => (
    <button
      data-testid="workspace-action-icon"
      onClick={(e) => onClick({ event: e, position: { x: 100, y: 200 } })}
    >
      Action
    </button>
  ),
}));

vi.mock('./types', () => ({
  MenuActionClickCallback: {},
}));

describe('TableTrashLayout', () => {
  const mockOnMenuActionClick = vi.fn();

  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('should render without crashing', () => {
    const { container } = renderWithRouter(
      <TableTrashLayout isLoading={false} bases={[]} />
    );
    expect(container).toBeInTheDocument();
  });

      it('should render loading skeleton when isLoading is true', () => {
        renderWithRouter(
          <TableTrashLayout isLoading={true} bases={[]} />
        );

        // Query for actual table element instead of mocked one
        const table = document.querySelector('table');
        expect(table).toBeInTheDocument();
        
        // Query for actual skeleton elements using Mantine classes
        const skeletons = document.querySelectorAll('.mantine-Skeleton-root');
        expect(skeletons).toHaveLength(30); // 3 columns * 10 rows (action column uses Box, not Skeleton)
        expect(screen.getByText('table.base')).toBeInTheDocument();
        expect(screen.getByText('table.owner')).toBeInTheDocument();
        expect(screen.getByText('table.dateDeleted')).toBeInTheDocument();
        expect(screen.getByText('table.permanentlyDeleted')).toBeInTheDocument();
      });

      it('should render table with bases when not loading', () => {
        renderWithRouter(
          <TableTrashLayout
            isLoading={false}
            bases={[mockTableBase]}
            onMenuActionClick={mockOnMenuActionClick}
          />
        );

        // Query for actual table element instead of mocked one
        const table = document.querySelector('table');
        expect(table).toBeInTheDocument();
        expect(screen.getByText('Test Base')).toBeInTheDocument();
        expect(screen.getByText('John Doe')).toBeInTheDocument();
        expect(screen.getByText('2023/01/03 00:00')).toBeInTheDocument();
        expect(screen.getByTestId('table-base-icon')).toBeInTheDocument();
        expect(screen.getByTestId('permanently-deleted-date')).toBeInTheDocument();
        
        // Query for actual avatar element using src attribute
        const avatar = document.querySelector('img[src="https://example.com/avatar.jpg"]');
        expect(avatar).toBeInTheDocument();
      });

  it('should render anonymous when owner name is not available', () => {
    const baseWithoutOwner = {
      ...mockTableBase,
      owner: { id: 'user-1', name: '', email: '', picture: '' },
    };

    renderWithRouter(
      <TableTrashLayout 
        isLoading={false} 
        bases={[baseWithoutOwner]} 
      />
    );

    expect(screen.getByText('table.anonymous')).toBeInTheDocument();
  });

  it('should render link to base page', () => {
    renderWithRouter(
      <TableTrashLayout 
        isLoading={false} 
        bases={[mockTableBase]} 
      />
    );

    const link = screen.getByTestId('link');
    expect(link).toHaveAttribute('href', '/tables/bases/base-1');
  });

  it('should call onMenuActionClick when action icon is clicked', () => {
    renderWithRouter(
      <TableTrashLayout 
        isLoading={false} 
        bases={[mockTableBase]} 
        onMenuActionClick={mockOnMenuActionClick}
      />
    );

    const actionButton = screen.getByTestId('workspace-action-icon');
    actionButton.click();

    expect(mockOnMenuActionClick).toHaveBeenCalledWith({
      base: mockTableBase,
      event: expect.any(Object),
      position: { x: 100, y: 200 },
    });
  });

  it('should return empty fragment when not loading and no bases', () => {
    renderWithRouter(
      <TableTrashLayout isLoading={false} bases={[]} />
    );

    // Check that no content is rendered (only Mantine styles)
    expect(screen.queryByTestId('table')).not.toBeInTheDocument();
  });

  it('should handle multiple bases', () => {
    const secondBase = {
      ...mockTableBase,
      id: 'base-2',
      name: 'Second Base',
      deletedAt: '2023-01-04T00:00:00Z',
    };

    renderWithRouter(
      <TableTrashLayout 
        isLoading={false} 
        bases={[mockTableBase, secondBase]} 
      />
    );

    expect(screen.getByText('Test Base')).toBeInTheDocument();
    expect(screen.getByText('Second Base')).toBeInTheDocument();
  });

  it('should render permanently deleted date component', () => {
    renderWithRouter(
      <TableTrashLayout 
        isLoading={false} 
        bases={[mockTableBase]} 
      />
    );

    const permanentlyDeletedDate = screen.getByTestId('permanently-deleted-date');
    expect(permanentlyDeletedDate).toBeInTheDocument();
    expect(permanentlyDeletedDate).toHaveAttribute('data-date', '2023-01-03T00:00:00Z');
  });

  it('should render avatar with xl radius', () => {
    renderWithRouter(
      <TableTrashLayout 
        isLoading={false} 
        bases={[mockTableBase]} 
      />
    );

    // Query for actual avatar element instead of mocked one
    const avatar = document.querySelector('img[src="https://example.com/avatar.jpg"]');
    expect(avatar).toBeInTheDocument();
    expect(avatar).toHaveClass('mantine-Avatar-image');
  });
});
