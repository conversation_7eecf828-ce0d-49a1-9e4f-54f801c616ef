import { renderWithRouter } from '@/utils/test';
import { screen } from '@testing-library/react';
import { vi, describe, it, expect, beforeEach } from 'vitest';
import { TableEmptyStatus } from './TableEmptyStatus';

vi.mock('@/constants', () => ({
  NO_BASES_IMAGE_URL: 'no-bases-image-url',
  NO_SEARCH_RESULT_IMAGE_URL: 'no-search-result-image-url',
}));

vi.mock('@mantine/core', () => ({
  Stack: ({ children, ...props }: any) => <div data-testid="stack" {...props}>{children}</div>,
  Text: ({ children, ...props }: any) => <span data-testid="text" {...props}>{children}</span>,
  rem: (value: number) => `${value}px`,
}));

vi.mock('@mantine/emotion', () => ({
  createStyles: () => () => ({
    classes: {
      img: 'mock-img-class',
      icon: 'mock-icon-class',
    },
  }),
}));

vi.mock('@resola-ai/ui', () => ({
  CustomImage: ({ url, className }: any) => <img data-testid="custom-image" src={url} className={className} alt="Custom Image" />,
  DecaButton: ({ children, onClick, ...props }: any) => (
    <button data-testid="deca-button" onClick={onClick} {...props}>
      {children}
    </button>
  ),
}));

vi.mock('@tabler/icons-react', () => ({
  IconPlus: () => <div data-testid="icon-plus">+</div>,
}));

vi.mock('react-router-dom', () => ({
  useLocation: () => ({ pathname: '/tables' }),
  MemoryRouter: ({ children }: any) => <div data-testid="memory-router">{children}</div>,
}));

describe('TableEmptyStatus', () => {
  const mockOnCreateButton = vi.fn();

  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('should render without crashing', () => {
    renderWithRouter(
      <TableEmptyStatus onCreateButton={mockOnCreateButton} />
    );
    expect(screen.getByText('baseEmpty.noBasesTitle')).toBeInTheDocument();
  });

  it('should render no search result when search has value and no bases', () => {
    renderWithRouter(
      <TableEmptyStatus 
        onCreateButton={mockOnCreateButton}
        isLoading={false}
        bases={[]}
        search="test search"
      />
    );

    expect(screen.getByTestId('custom-image')).toHaveAttribute('src', 'no-search-result-image-url');
    expect(screen.getByText('baseEmpty.noResultTitle')).toBeInTheDocument();
    expect(screen.getByText('baseEmpty.noResultDescription')).toBeInTheDocument();
  });

  it('should render no bases when no search and no bases', () => {
    renderWithRouter(
      <TableEmptyStatus 
        onCreateButton={mockOnCreateButton}
        isLoading={false}
        bases={[]}
        search=""
      />
    );

    expect(screen.getByTestId('custom-image')).toHaveAttribute('src', 'no-bases-image-url');
    expect(screen.getByText('baseEmpty.noBasesTitle')).toBeInTheDocument();
    expect(screen.getByText('baseEmpty.noBasesDescription')).toBeInTheDocument();
    expect(screen.getByTestId('deca-button')).toBeInTheDocument();
    expect(screen.getByText('baseEmpty.noBasesCreateBtn')).toBeInTheDocument();
  });

  it('should return null when loading', () => {
    renderWithRouter(
      <TableEmptyStatus 
        onCreateButton={mockOnCreateButton}
        isLoading={true}
        bases={[]}
        search=""
      />
    );

    // Check that no content is rendered (only Mantine styles)
    expect(screen.queryByTestId('custom-image')).not.toBeInTheDocument();
    expect(screen.queryByText('baseEmpty.noBasesTitle')).not.toBeInTheDocument();
  });

  it('should return null when has bases', () => {
    renderWithRouter(
      <TableEmptyStatus 
        onCreateButton={mockOnCreateButton}
        isLoading={false}
        bases={[{ id: 'base-1', name: 'Test Base', description: 'Test base', deletedAt: '', permanentlyDelete: false, owner: { id: 'user-1', name: 'Test User', email: '<EMAIL>', picture: '' }, options: { theme: 'blue', icon: 'table' }, createdAt: new Date(), updatedAt: new Date() }]}
        search=""
      />
    );

    // Check that no content is rendered (only Mantine styles)
    expect(screen.queryByTestId('custom-image')).not.toBeInTheDocument();
    expect(screen.queryByText('baseEmpty.noBasesTitle')).not.toBeInTheDocument();
  });
});
