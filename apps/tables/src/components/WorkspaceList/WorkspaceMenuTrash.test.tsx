import { renderWithRouter } from '@/utils/test';
import { screen } from '@testing-library/react';
import { vi, describe, it, expect, beforeEach } from 'vitest';
import { WorkspaceTrashActionMenu, useWorkspaceTrashActionMenu } from './WorkspaceMenuTrash';

vi.mock('@/constants', () => ({
  WORKSPACE_MENU_ANIMATION_NAME: 'fade',
  WORKSPACE_TRASH_MENU_ID: 'workspace-trash-menu',
}));

vi.mock('@mantine/core', () => ({
  Portal: ({ children }: any) => <div data-testid="portal">{children}</div>,
  Text: ({ children }: any) => <span data-testid="text">{children}</span>,
}));

vi.mock('@tabler/icons-react', () => ({
  IconArrowBack: () => <div data-testid="icon-arrow-back">Restore</div>,
  IconTrash: () => <div data-testid="icon-trash">Trash</div>,
}));

// Array to capture onClick handlers for direct testing
const capturedOnClickHandlers: Array<(params: any) => void> = [];

vi.mock('react-contexify', () => ({
  Menu: ({ children, ...props }: any) => <div data-testid="menu" {...props}>{children}</div>,
  Item: ({ children, onClick, ...props }: any) => {
    // Capture the actual onClick handler
    if (onClick) {
      capturedOnClickHandlers.push(onClick);
    }
    return (
      <div data-testid="menu-item" onClick={onClick} {...props}>
        {children}
      </div>
    );
  },
  Separator: () => <div data-testid="menu-separator">Separator</div>,
  useContextMenu: vi.fn(() => ({
    show: vi.fn(),
    hide: vi.fn(),
  })),
}));

vi.mock('react-router-dom', () => ({
  MemoryRouter: ({ children }: any) => <div data-testid="memory-router">{children}</div>,
}));

vi.mock('./useMenuStyles', () => ({
  useMenuStyles: () => ({
    classes: {
      menu: 'mock-menu-class',
      item: 'mock-item-class',
      itemRed: 'mock-item-red-class',
    },
    cx: (...classes: string[]) => classes.filter(Boolean).join(' '),
  }),
}));

vi.mock('./types', () => ({
  MenuActionItemParams: {},
}));

describe('WorkspaceTrashActionMenu', () => {
  const mockOnRestoreTableBase = vi.fn();
  const mockOnPermanentlyDeleteTableBase = vi.fn();

  beforeEach(() => {
    vi.clearAllMocks();
    // Clear captured handlers for each test
    capturedOnClickHandlers.length = 0;
  });

  it('should render without crashing', () => {
    const { container } = renderWithRouter(
      <WorkspaceTrashActionMenu
        onRestoreTableBase={mockOnRestoreTableBase}
        onPermanentlyDeleteTableBase={mockOnPermanentlyDeleteTableBase}
      />
    );
    expect(container).toBeInTheDocument();
  });

  it('should render menu with correct props', () => {
    renderWithRouter(
      <WorkspaceTrashActionMenu
        onRestoreTableBase={mockOnRestoreTableBase}
        onPermanentlyDeleteTableBase={mockOnPermanentlyDeleteTableBase}
      />
    );

    const menu = screen.getByTestId('menu');
    expect(menu).toHaveAttribute('animation', 'fade');
    expect(menu).toHaveAttribute('id', 'workspace-trash-menu');
    expect(menu).toHaveClass('mock-menu-class');
  });

  it('should render all menu items', () => {
    renderWithRouter(
      <WorkspaceTrashActionMenu
        onRestoreTableBase={mockOnRestoreTableBase}
        onPermanentlyDeleteTableBase={mockOnPermanentlyDeleteTableBase}
      />
    );

    expect(screen.getAllByTestId('menu-item')).toHaveLength(2);
    expect(screen.getByTestId('menu-separator')).toBeInTheDocument();
    expect(screen.getByText('actionMenu.restore')).toBeInTheDocument();
    expect(screen.getByText('actionMenu.permanentlyDelete')).toBeInTheDocument();
  });

  it('should render icons correctly', () => {
    renderWithRouter(
      <WorkspaceTrashActionMenu
        onRestoreTableBase={mockOnRestoreTableBase}
        onPermanentlyDeleteTableBase={mockOnPermanentlyDeleteTableBase}
      />
    );

    // Icons are rendered as SVG elements, so we check for their presence in the DOM
    expect(screen.getByText('actionMenu.restore')).toBeInTheDocument();
    expect(screen.getByText('actionMenu.permanentlyDelete')).toBeInTheDocument();
  });

  it('should handle menu item clicks', () => {
    renderWithRouter(
      <WorkspaceTrashActionMenu
        onRestoreTableBase={mockOnRestoreTableBase}
        onPermanentlyDeleteTableBase={mockOnPermanentlyDeleteTableBase}
      />
    );

    const restoreItem = screen.getAllByTestId('menu-item')[0];
    restoreItem.click();

    // The handler expects props with base, but we're not providing it in the test
    // So we just verify the item is clickable
    expect(restoreItem).toBeInTheDocument();
  });

  it('should not call handlers when props are undefined', () => {
    renderWithRouter(
      <WorkspaceTrashActionMenu
        onRestoreTableBase={undefined as any}
        onPermanentlyDeleteTableBase={undefined as any}
      />
    );

    const restoreItem = screen.getAllByTestId('menu-item')[0];
    restoreItem.click();

    expect(mockOnRestoreTableBase).not.toHaveBeenCalled();
  });

  it('should execute onRestoreTableBase when restore handler is called with valid props (lines 25-26)', () => {
    const mockTableBase = { id: 'test-table-id', name: 'Test Table' };
    
    renderWithRouter(
      <WorkspaceTrashActionMenu
        onRestoreTableBase={mockOnRestoreTableBase}
        onPermanentlyDeleteTableBase={mockOnPermanentlyDeleteTableBase}
      />
    );

    // Verify handlers were captured
    expect(capturedOnClickHandlers).toHaveLength(2);

    // Create mock event with correct structure for restore handler (first handler)
    const mockEvent = {
      props: {
        base: mockTableBase
      }
    };

    // Execute the restore handler directly
    capturedOnClickHandlers[0](mockEvent);

    // Verify onRestoreTableBase was called with correct table base
    expect(mockOnRestoreTableBase).toHaveBeenCalledWith(mockTableBase);
  });

  it('should execute onPermanentlyDeleteTableBase when delete handler is called with valid props (lines 32-34)', () => {
    const mockTableBase = { id: 'test-table-id', name: 'Test Table' };
    
    renderWithRouter(
      <WorkspaceTrashActionMenu
        onRestoreTableBase={mockOnRestoreTableBase}
        onPermanentlyDeleteTableBase={mockOnPermanentlyDeleteTableBase}
      />
    );

    // Verify handlers were captured
    expect(capturedOnClickHandlers).toHaveLength(2);

    // Create mock event with correct structure for delete handler (second handler)
    const mockEvent = {
      props: {
        base: mockTableBase
      }
    };

    // Execute the delete handler directly
    capturedOnClickHandlers[1](mockEvent);

    // Verify onPermanentlyDeleteTableBase was called with correct table base
    expect(mockOnPermanentlyDeleteTableBase).toHaveBeenCalledWith(mockTableBase);
  });

  it('should handle early returns when props are missing', () => {
    renderWithRouter(
      <WorkspaceTrashActionMenu
        onRestoreTableBase={mockOnRestoreTableBase}
        onPermanentlyDeleteTableBase={mockOnPermanentlyDeleteTableBase}
      />
    );

    // Test restore handler with missing props
    capturedOnClickHandlers[0]({ props: null });
    expect(mockOnRestoreTableBase).not.toHaveBeenCalled();

    // Test restore handler with missing base
    capturedOnClickHandlers[0]({ props: { base: null } });
    expect(mockOnRestoreTableBase).not.toHaveBeenCalled();

    // Test delete handler with missing props
    capturedOnClickHandlers[1]({ props: null });
    expect(mockOnPermanentlyDeleteTableBase).not.toHaveBeenCalled();

    // Test delete handler with missing base
    capturedOnClickHandlers[1]({ props: { base: null } });
    expect(mockOnPermanentlyDeleteTableBase).not.toHaveBeenCalled();
  });
});

describe('useWorkspaceTrashActionMenu', () => {
  it('should return context menu hook', () => {
    const result = useWorkspaceTrashActionMenu();
    
    expect(result).toEqual({
      show: expect.any(Function),
      hide: expect.any(Function),
    });
  });
});
