import { renderWithRouter } from '@/utils/test';
import { vi, describe, it, expect, beforeEach } from 'vitest';
import React from 'react';
import App from './App';

let defaultLang = 'en';

// Mock dependencies
vi.mock('@resola-ai/ui/pages', () => ({
  ErrorPage: ({ appName }: { appName: string }) => (
    <div data-testid="error-page" data-app-name={appName}>
      Error Page
    </div>
  ),
}));

vi.mock('@resola-ai/ui/hooks', () => ({
  useSetDefaultLang: () => defaultLang,
}));

// Mock Tolgee functionality
vi.mock('@tolgee/react', () => ({
  useTranslate: () => ({ t: (key: string) => key }),
  useTolgee: () => ({
    getLanguage: vi.fn(),
    changeLanguage: vi.fn(),
    t: (key: string) => key,
  }),
}));

vi.mock('./pages', () => ({
  TableBasePage: () => <div data-testid="table-base-page">Table Base Page</div>,
  TableBaseTrashPage: () => <div data-testid="table-base-trash-page">Table Base Trash Page</div>,
  TablePage: () => <div data-testid="table-page">Table Page</div>,
}));

// Mock React Router
vi.mock('react-router-dom', () => ({
  Route: ({ path, element }: { path: string; element: React.ReactNode }) => (
    <div data-testid={`route-${path}`} data-path={path}>
      {element}
    </div>
  ),
  Routes: ({ children }: { children: React.ReactNode }) => (
    <div data-testid="routes">{children}</div>
  ),
  MemoryRouter: ({ children, initialEntries }: { children: React.ReactNode; initialEntries?: string[] }) => (
    <div data-testid="memory-router" data-initial-entries={initialEntries?.join(',')}>
      {children}
    </div>
  ),
}));

describe('App', () => {
  let tolgeeMock: any;

  beforeEach(async () => {
    vi.clearAllMocks();
    defaultLang = 'en';

    // Create a mock tolgee object
    tolgeeMock = {
      getLanguage: vi.fn(),
      changeLanguage: vi.fn(),
      t: vi.fn((key: string) => key),
    };

    const tolgeeReact = await import('@tolgee/react');
    tolgeeReact.useTolgee = vi.fn(() => tolgeeMock);
  });

  it('should render without crashing', () => {
    const { container } = renderWithRouter(<App />);
    expect(container).toBeInTheDocument();
  });

  it('should render Routes component', () => {
    renderWithRouter(<App />);
    expect(document.querySelector('[data-testid="routes"]')).toBeInTheDocument();
  });

  it('should call hooks', () => {
    renderWithRouter(<App />);
    // Just verify the component renders - hooks are called internally
    expect(document.querySelector('[data-testid="routes"]')).toBeInTheDocument();
  });

  it('changes language when the language from hook differs from current tolgee language', () => {
    // Arrange
    const currentLang = 'en';
    const newLang = 'ja';

    defaultLang = newLang;
    tolgeeMock.getLanguage.mockReturnValue(currentLang);

    renderWithRouter(<App />);

    // Verify that the language was checked and changed
    expect(tolgeeMock.getLanguage).toHaveBeenCalled();
    expect(tolgeeMock.changeLanguage).toHaveBeenCalledWith(newLang);
  });

  it('does not change language when the language from hook matches current tolgee language', () => {
    // Arrange
    const currentLang = 'en';
    const newLang = 'en';

    defaultLang = newLang;
    tolgeeMock.getLanguage.mockReturnValue(currentLang);

    renderWithRouter(<App />);

    // Verify that the language was checked but not changed
    expect(tolgeeMock.getLanguage).toHaveBeenCalled();
    expect(tolgeeMock.changeLanguage).not.toHaveBeenCalled();
  });
});