import { useUserInfor } from '@/hooks/useUserInfor';
import { ActionIcon, Box, Flex, Menu, Text, rem, useMantineTheme } from '@mantine/core';
import { DecaButton } from '@resola-ai/ui';
import { IconDotsVertical, IconEdit, IconTrash } from '@tabler/icons-react';
import { useTranslate } from '@tolgee/react';
import { useState } from 'react';
import type { TemplateItem } from '../../../constants/template';
import { DeleteTemplateModal } from './DeleteTemplateModal';
import { EditTemplateModal } from './EditTemplateModal';

interface TemplateCardProps {
  template: TemplateItem;
  isCreatingSite: boolean;
  onUseTemplate: (template: TemplateItem) => void;
  onPreviewTemplate: (templateId: string) => void;
  onEditTemplate?: (
    templateId: string,
    data: { name: string; categoryId: string }
  ) => Promise<void>;
  onDeleteTemplate?: (templateId: string) => void;
}

export const TemplateCard = ({
  template,
  isCreatingSite,
  onUseTemplate,
  onPreviewTemplate,
  onEditTemplate,
  onDeleteTemplate,
}: TemplateCardProps) => {
  const { t } = useTranslate('workspace');
  const theme = useMantineTheme();
  const { isStudioUser } = useUserInfor();
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);

  return (
    <Box
      sx={{
        cursor: 'pointer',
        transition: 'transform 0.2s ease',
        '&:hover': {
          transform: 'translateY(-2px)',
        },
        width: rem(320),
        height: rem(240),
        padding: rem(12),
        borderRadius: rem(12),
        border: `1px solid ${theme.colors.decaLight[3]}`,
        flex: '0 0 auto',
      }}
    >
      <Box
        sx={{
          position: 'relative',
          borderRadius: rem(8),
          backgroundColor: theme.colors.decaLight[0],
          height: rem(182),
        }}
      >
        <img
          src={template.visualAssets.thumbnail}
          alt={template.name}
          style={{
            width: '100%',
            height: '100%',
            objectFit: 'cover',
            borderRadius: rem(4),
            border: `1px solid ${theme.colors.decaLight[2]}`,
          }}
        />
        <Box
          sx={{
            position: 'absolute',
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            backgroundColor: 'rgba(0, 0, 0, 0.8)',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            opacity: 0,
            transition: 'opacity 0.2s ease',
            borderRadius: rem(8),
            padding: rem(16),
            '&:hover': {
              opacity: 1,
            },
          }}
        >
          <Flex direction='column' gap='sm' align='center'>
            <DecaButton
              size='sm'
              variant='secondary'
              onClick={() => onUseTemplate(template)}
              loading={isCreatingSite}
              disabled={isCreatingSite}
            >
              {isCreatingSite ? t('creatingSite') : t('useThisTemplate')}
            </DecaButton>
            <DecaButton
              size='sm'
              variant='neutral'
              bg={theme.colors.decaLight[0]}
              disabled={isCreatingSite}
              onClick={() => onPreviewTemplate(template.id)}
            >
              {t('preview')}
            </DecaButton>
          </Flex>
        </Box>
      </Box>
      <Flex justify='space-between' align='center' mt='xs'>
        <Text size='md' fw={500} truncate style={{ flex: 1 }}>
          {template.name}
        </Text>
        {isStudioUser && (
          <Menu shadow='md' width={200}>
            <Menu.Target>
              <ActionIcon
                variant='subtle'
                size='sm'
                onClick={(e) => e.stopPropagation()}
                sx={{
                  color: theme.colors.decaGrey[6],
                  '&:hover': {
                    backgroundColor: theme.colors.decaLight[2],
                  },
                }}
              >
                <IconDotsVertical size={16} />
              </ActionIcon>
            </Menu.Target>

            <Menu.Dropdown>
              <Menu.Item
                leftSection={<IconEdit size={14} />}
                onClick={() => setIsEditModalOpen(true)}
              >
                {t('edit')}
              </Menu.Item>
              <Menu.Item
                leftSection={<IconTrash size={14} />}
                color='red'
                onClick={() => setIsDeleteModalOpen(true)}
              >
                {t('delete')}
              </Menu.Item>
            </Menu.Dropdown>
          </Menu>
        )}
      </Flex>

      {/* Edit Template Modal */}
      <EditTemplateModal
        isOpen={isEditModalOpen}
        onClose={() => setIsEditModalOpen(false)}
        template={template}
        onSave={
          onEditTemplate ||
          (async (_templateId: string, _data: { name: string; categoryId: string }) => {})
        }
      />

      {/* Delete Template Modal */}
      <DeleteTemplateModal
        isOpen={isDeleteModalOpen}
        onClose={() => setIsDeleteModalOpen(false)}
        template={template}
        onDelete={onDeleteTemplate || (() => {})}
      />
    </Box>
  );
};
