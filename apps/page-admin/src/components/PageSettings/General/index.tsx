import DecaFavicon from '@/assets/images/deca-favicon.png';
import GoogleIcon from '@/assets/images/google-icon.svg';
import PlaceholderImage from '@/assets/images/placeholder-image.svg';
import { FormProvider } from '@/contexts';
import { createTypedForm } from '@/contexts/FormContext';
import useSiteSettings from '@/hooks/useSiteSettings';
import { SiteAPI } from '@/services/api/site';
import { ToolbarMenuKey } from '@/types/enum';
import { showNotificationToast } from '@/utils/notification';
import {
  Box,
  Flex,
  Group,
  Image,
  Input,
  Loader,
  LoadingOverlay,
  SimpleGrid,
  Space,
  Text,
  TextInput,
  Textarea,
  rem,
} from '@mantine/core';
import { zodResolver } from '@mantine/form';
import { DecaButton } from '@resola-ai/ui';
import { IconSettings, IconX } from '@tabler/icons-react';
import { useTranslate } from '@tolgee/react';
import { isEmpty } from 'lodash';
import { useEffect, useState } from 'react';
import { Link, useParams } from 'react-router-dom';
import { z } from 'zod';
import BaseLayout from '../BaseLayout';
import { Label } from '../Label';
import classes from './General.module.css';
import SelectFileBtn from './SelectFileBtn';

const MAX_DESCRIPTION_LENGTH = 200;
const MAX_IMAGE_SIZE = 1024 * 1024 * 2;
interface FormValues {
  name: string;
  description: string;
  visual_assets: {
    favicon: string;
    thumbnail: string;
  };
  siteUrl: string;
}

const { useForm } = createTypedForm<FormValues>();

const General = () => {
  const { t } = useTranslate(['common', 'site_settings']);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const { siteId } = useParams();
  const schema = z.object({
    name: z
      .string()
      .trim()
      .min(1, { message: t('requiredError', { ns: 'common' }) })
      .max(100, { message: t('validationFormNameLength', { max: 100, ns: 'site_settings' }) }),
  });

  const form = useForm({
    initialValues: {
      name: '',
      description: '',
      visual_assets: {
        favicon: '',
        thumbnail: '',
      },
      siteUrl: '',
    },
    validateInputOnChange: true,
    validate: zodResolver(schema),
  });

  const { data: siteSettings, isValidating } = useSiteSettings();

  // Initialize form with fetched siteSettings data
  useEffect(() => {
    if (siteSettings && !isEmpty(siteSettings)) {
      form.setValues({
        name: siteSettings.name || '',
        description: siteSettings.description || '',
        visual_assets: {
          favicon: siteSettings.visual_assets?.favicon,
          thumbnail: siteSettings.visual_assets?.thumbnail,
        },
        siteUrl: siteSettings.siteUrl || '',
      });
    }
  }, [siteSettings]);

  const formValues = form.values;

  if ((isValidating && isEmpty(siteSettings)) || isEmpty(formValues)) {
    return <LoadingOverlay visible={true} />;
  }

  const handleSubmit = (formData: any) => {
    setIsSubmitting(true);
    SiteAPI.updateSiteSettings(siteId as string, formData).then(() => {
      showNotificationToast({
        message: t('saveSuccess', { ns: 'site_settings' }),
      });
      setIsSubmitting(false);
    });
  };

  return (
    <BaseLayout title={t('generalTitle', { ns: 'site_settings' })}>
      <FormProvider form={form}>
        <form onSubmit={form.onSubmit((formData) => handleSubmit(formData) as undefined)}>
          <Box component='div' p={0} mb={rem(40)}>
            <Flex direction='column'>
              <Text tt='uppercase' mb={rem(28)} fw={500} fz={rem(12)} c='decaNavy.4'>
                {t('informationTitle', { ns: 'site_settings' })}
              </Text>
              <TextInput
                label={t('websiteNameLabel', { ns: 'site_settings' })}
                classNames={{ label: classes.label }}
                withAsterisk
                placeholder={t('siteNamePlaceholder', { ns: 'site_settings' })}
                maxLength={40}
                mb={rem(20)}
                {...form.getInputProps('name')}
              />
              <Input.Wrapper label={t('descriptionLabel', { ns: 'site_settings' })} pos='relative'>
                <Textarea
                  minRows={4}
                  maxRows={6}
                  resize='vertical'
                  maxLength={MAX_DESCRIPTION_LENGTH}
                  autosize
                  description={t('descDescription', { ns: 'site_settings' })}
                  classNames={{ label: classes.label }}
                  {...form.getInputProps('description')}
                />
                <div className={classes.characterCount}>
                  {formValues.description.length}/{MAX_DESCRIPTION_LENGTH}{' '}
                  {t('characters', { ns: 'site_settings' })}
                </div>
              </Input.Wrapper>
            </Flex>

            <Flex direction='column' gap={rem(24)}>
              <Text tt='uppercase' mt={rem(40)} fw={500} fz={rem(12)} c='decaNavy.4'>
                {t('siteImagesTitle', { ns: 'site_settings' })}
              </Text>
              <Box component='div'>
                <Label text={t('faviconLabel', { ns: 'site_settings' })} />
                <SimpleGrid cols={2} spacing={rem(40)}>
                  <Flex mah={rem(65)} className={classes.faviconPreviewWrapper}>
                    <Flex bg={'decaLight.1'} w='100%' align='center' mb={rem(10)}>
                      <Image
                        mt={rem(10)}
                        ml={rem(10)}
                        w={rem(32)}
                        h={rem(32)}
                        src={GoogleIcon}
                        alt={t('decaPageName', { ns: 'site_settings' })}
                      />
                      <Flex
                        bg='white'
                        className={classes.faviconPreviewBox}
                        align='center'
                        mx={rem(10)}
                        mt={rem(10)}
                        wrap='nowrap'
                        p={rem(5)}
                      >
                        <Group wrap='nowrap' flex={1}>
                          <Image
                            w={rem(32)}
                            h={rem(32)}
                            src={formValues.visual_assets.favicon || DecaFavicon}
                            alt={t('decaPageName', { ns: 'site_settings' })}
                          />
                          <Text>{t('decaPageName', { ns: 'site_settings' })}</Text>
                        </Group>
                        <IconX />
                      </Flex>
                    </Flex>
                  </Flex>
                  <Flex direction='column' gap={rem(8)} justify='center'>
                    <SelectFileBtn
                      onSelect={(file) => {
                        form.clearFieldError('favicon');
                        form.setFieldValue('visual_assets.favicon', file);
                      }}
                      maxSize={MAX_IMAGE_SIZE}
                      onError={(error) => form.setFieldError('favicon', error)}
                    />
                    <Text fz={rem(12)} c='decaGrey.5'>
                      {t('faviconSizeDesc', { ns: 'site_settings' })}
                    </Text>
                    <Text fz={rem(12)} c='decaRed.5' mt={rem(4)}>
                      {form.errors?.favicon as string}
                    </Text>
                  </Flex>
                </SimpleGrid>
              </Box>

              <Box component='div'>
                <Label text={t('thumbnailLabel', { ns: 'site_settings' })} />
                <Text fz={rem(12)} c='decaGrey.5'>
                  {t('thumbnailDescription', { ns: 'site_settings' })}
                </Text>
                <SimpleGrid cols={2} my={rem(16)} spacing={rem(40)}>
                  <Box component='div' className={classes.thumbnailPreviewBox}>
                    <Image
                      src={formValues.visual_assets.thumbnail || PlaceholderImage}
                      alt='placeholder image'
                      className={classes.thumbnailImage}
                      radius='sm'
                    />
                  </Box>
                  <Flex direction='column' gap={rem(8)} justify='center'>
                    <SelectFileBtn
                      onSelect={(file) => {
                        form.clearFieldError('thumbnail');
                        form.setFieldValue('visual_assets.thumbnail', file);
                      }}
                      maxSize={MAX_IMAGE_SIZE}
                      onError={(error) => form.setFieldError('thumbnail', error)}
                    />
                    <Text fz={rem(12)} c='decaGrey.5'>
                      {t('thumbnailSizeDescription', { ns: 'site_settings' })}
                    </Text>
                    <Text fz={rem(12)} c='decaRed.5' mt={rem(4)}>
                      {form.errors?.thumbnail as string}
                    </Text>
                  </Flex>
                </SimpleGrid>
              </Box>

              <Flex wrap='wrap'>
                <Text fz={rem(12)} c='decaGrey.5'>
                  {t('setupPageNameDescription', { ns: 'site_settings' })}
                </Text>
                <Link
                  to={`../builder/${siteId}?menu=${ToolbarMenuKey.PagesMenu}`}
                  className={classes.setupPageNameDescription}
                >
                  <IconSettings size={16} />{' '}
                  <Text>{t('pagesSettings', { ns: 'site_settings' })}</Text>
                </Link>
              </Flex>
            </Flex>

            <Flex direction='column'>
              <Text tt='uppercase' my={rem(28)} fw={500} fz={rem(12)} c='decaNavy.4'>
                {t('previewTitle', { ns: 'site_settings' })}
              </Text>

              <Flex gap={rem(4)} direction='column' w='50%' className={classes.previewBox}>
                <Group gap={rem(8)}>
                  <Image
                    w={rem(32)}
                    h={rem(32)}
                    src={formValues.visual_assets.favicon || DecaFavicon}
                    alt={t('decaPageName', { ns: 'site_settings' })}
                  />
                  <Text>{t('yoursiteUrl', { ns: 'site_settings' })}</Text>
                </Group>
                <Text fw={500} tt='uppercase' c='decaNavy.4'>
                  {formValues.name || t('decaWebsiteName', { ns: 'site_settings' })}
                </Text>
                <Text c='decaGrey.5' lineClamp={2}>
                  {formValues.description || t('poweredByDeca', { ns: 'site_settings' })}
                </Text>
                <Space h={rem(12)} />
                <Image
                  src={formValues.visual_assets.thumbnail || PlaceholderImage}
                  alt='placeholder image'
                  className={classes.thumbnailImage}
                  radius='sm'
                />
              </Flex>
            </Flex>
          </Box>

          <DecaButton
            radius='xl'
            color='decaNavy.5'
            fw={500}
            leftSection={isSubmitting ? <Loader size={rem(20)} color='decaLight.2' /> : null}
            fz={rem(16)}
            disabled={isSubmitting || !form.isValid()}
            type='submit'
          >
            {t('saveChanges', { ns: 'site_settings' })}
          </DecaButton>
        </form>
      </FormProvider>
    </BaseLayout>
  );
};

export default General;
