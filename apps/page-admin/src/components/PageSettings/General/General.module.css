.label {
  font-size: rem(14px);
  font-weight: 500;
  margin-bottom: rem(8px);
  text-transform: capitalize;
}

.characterCount {
  position: absolute;
  bottom: rem(10px);
  right: rem(20px);
  font-size: rem(12px);
  color: var(--mantine-color-decaGrey-5);
}

.faviconPreviewBox {
  flex: 1;
  border-top-right-radius: rem(8px);
  border-top-left-radius: rem(8px);
  background: white;
}

.faviconPreviewWrapper {
  border: 1px solid var(--mantine-color-decaLight-2);
  border-radius: rem(4px);
}

.thumbnailPreviewBox {
  border-radius: rem(14px);
  border: 1px solid var(--mantine-color-decaLight-2);
  padding: rem(14px);
}

.previewBox {
  border-radius: rem(14px);
  border: 1px solid var(--mantine-color-decaLight-2);
  padding: rem(14px);
}

.setupPageNameDescription {
  text-decoration: none;
  color: var(--mantine-color-decaBlue-5);
  display: flex;
  align-items: center;
  gap: rem(4px);
}

.thumbnailImage {
  border: 1px solid var(--mantine-color-decaLight-2);
}
