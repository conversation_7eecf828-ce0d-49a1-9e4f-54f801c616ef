import DefaultSiteThumbnail from '@/assets/images/default-site-thumbnail.svg';
import { FormProvider } from '@/contexts';
import { PagesAPI, SiteAPI } from '@/services/api';
import type { Page, Site } from '@/types';
import {
  Avatar,
  Flex,
  Group,
  Image,
  Modal,
  Paper,
  Text,
  TextInput,
  Textarea,
  rem,
} from '@mantine/core';
import { useForm } from '@mantine/form';
import { zodResolver } from '@mantine/form';
import { DecaButton } from '@resola-ai/ui';
import { useTranslate } from '@tolgee/react';
import { useEffect, useState } from 'react';
import { useParams } from 'react-router-dom';
import useSWR from 'swr';
import { z } from 'zod';
import SelectFileBtn from '../PageSettings/General/SelectFileBtn';
import classes from './CreateUpdatePageModal.module.css';

const MAX_IMAGE_SIZE = 1024 * 1024 * 2;

type CreatePageModalProps = {
  isOpen: boolean;
  selectedPage?: Page;
  onClose: () => void;
  mutatePages: () => void;
};

const CreateUpdatePageModal = ({
  isOpen,
  selectedPage,
  onClose,
  mutatePages,
}: CreatePageModalProps) => {
  const { t } = useTranslate('builder');
  const { siteId } = useParams();
  const [isSubmitting, setIsSubmitting] = useState(false);

  const schema = z.object({
    name: z
      .string()
      .trim()
      .min(1, { message: t('requiredError', { ns: 'common' }) }),
    url: z.string().regex(/^[a-zA-Z0-9_-]+$/, { message: t('invalidUrl') }),
  });

  const { data: site } = useSWR<Site>(siteId ? `/sites/${siteId}` : null, () =>
    SiteAPI.getSite(siteId || '')
  );

  const form = useForm({
    initialValues: {
      name: '',
      description: '',
      url: '',
      image: '',
    },
    validateInputOnChange: true,
    validate: zodResolver(schema),
  });

  const formValues = form.values;

  useEffect(() => {
    form.setValues({
      name: selectedPage?.name || '',
      description: selectedPage?.metadata?.description || '',
      url: selectedPage?.url || '',
      image: selectedPage?.metadata?.image || '',
    });
  }, [selectedPage]);

  const handleSubmit = async (formData: any) => {
    if (!siteId) return;
    try {
      setIsSubmitting(true);
      if (!selectedPage?.logical_id) {
        await PagesAPI.createPage(siteId, formData);
      } else {
        const updateFormData = {
          name: formData.name,
          url: formData.url,
          metadata: {
            description: formData.description,
            image: formData?.image,
          },
        };
        await PagesAPI.updatePage(siteId, selectedPage.logical_id, updateFormData);
      }
      form.reset();
      onClose();
      mutatePages();
    } catch (error: any) {
      if (error?.response?.data?.response?.type === 'page:url_already_exists') {
        form.setFieldError('url', t('urlAlreadyExists'));
      }
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleClose = () => {
    form.reset();
    onClose();
  };

  return (
    <Modal
      size={rem(1000)}
      opened={isOpen}
      onClose={handleClose}
      centered
      className={classes.modal}
      title={selectedPage ? t('settings') : t('createNewPage')}
    >
      <FormProvider form={form}>
        <form onSubmit={form.onSubmit((formData) => handleSubmit(formData))}>
          <Flex gap={rem(30)}>
            <Flex direction={'column'} gap={rem(12)} style={{ flexGrow: 1 }}>
              <Text c='decaNavy.4' fw={700} fz={12}>
                {t('information')}
              </Text>
              <TextInput
                w='100%'
                label={t('pageName')}
                withAsterisk
                maxLength={100}
                classNames={{ label: classes.label }}
                {...form.getInputProps('name')}
              />
              <TextInput
                w='100%'
                label={t('url')}
                maxLength={100}
                withAsterisk
                classNames={{ label: classes.label }}
                {...form.getInputProps('url')}
              />
              <Textarea
                w='100%'
                label={t('description')}
                classNames={{ label: classes.label }}
                description={t('descriptionHelperText')}
                minRows={4}
                maxRows={4}
                maxLength={150}
                {...form.getInputProps('description')}
              />
              <Text fw={500} size='sm' mt={rem(12)}>
                {t('socialPreviewImage')}
              </Text>
              <Text c='dimmed' size='sm' style={{ whiteSpace: 'pre-line' }} mb={rem(8)}>
                {t('socialPreviewImageHelperText')}
              </Text>
              <Flex gap={rem(4)}>
                <SelectFileBtn
                  label={formValues?.image ? 'changeFile' : 'selectFile'}
                  resourceType='page'
                  maxSize={MAX_IMAGE_SIZE}
                  onSelect={(file) => form.setFieldValue('image', file)}
                  onError={(error) => form.setFieldError('image', error)}
                />
                {form.errors.image && (
                  <Text fz={rem(12)} c='decaRed.5' mt={rem(4)}>
                    {form.errors.image}
                  </Text>
                )}
              </Flex>
            </Flex>
            <Flex direction={'column'} gap={rem(12)} w='40%' style={{ flexGrow: 0 }}>
              <Text c='decaNavy.4' fw={700} fz={12}>
                {t('preview')}
              </Text>
              <Paper p='lg' radius='lg' withBorder w='100%' h='contain' mb={rem(20)}>
                <Flex align='center' w='100%' gap={rem(10)} mb={rem(10)}>
                  <Avatar src={site?.visual_assets?.favicon} size={rem(24)} />
                  <Text truncate>{formValues.url || 'yoursite.url'}</Text>
                </Flex>
                <Text c='decaNavy.4' mb={rem(10)} fw={500}>
                  {site?.name || 'Deca'} - {formValues.name}
                </Text>
                <Text mb={rem(10)} style={{ wordBreak: 'break-word' }}>
                  {formValues.description}
                </Text>
                <Image
                  className={classes.thumbnailImage}
                  src={formValues.image || DefaultSiteThumbnail}
                  height={160}
                  radius='sm'
                />
              </Paper>
            </Flex>
          </Flex>
          <Group justify='flex-end'>
            <DecaButton variant='neutral' onClick={handleClose}>
              {t('cancel', { ns: 'common' })}
            </DecaButton>
            <DecaButton variant='primary' type='submit' disabled={isSubmitting}>
              {t('save', { ns: 'common' })}
            </DecaButton>
          </Group>
        </form>
      </FormProvider>
    </Modal>
  );
};

export default CreateUpdatePageModal;
