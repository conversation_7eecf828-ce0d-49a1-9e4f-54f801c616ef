import { fireEvent, render, screen } from '@testing-library/react';
import { MemoryRouter } from 'react-router-dom';
import { beforeEach, describe, expect, it, vi } from 'vitest';
import CreateUpdatePageModal from './CreateUpdatePageModal';

// Mock dependencies
vi.mock('@/assets/images/default-site-thumbnail.svg', () => ({
  default: 'mocked-default-thumbnail.svg',
}));

vi.mock('@/contexts', () => ({
  FormProvider: ({ children, form }: any) => (
    <div data-testid='form-provider' data-form={form ? 'provided' : 'not-provided'}>
      {children}
    </div>
  ),
}));

vi.mock('@/services/api', () => ({
  PagesAPI: {
    createPage: vi.fn(),
    updatePage: vi.fn(),
  },
  SiteAPI: {
    getSite: vi.fn(),
  },
}));

vi.mock('@/types', () => ({
  // Mock types as needed
}));

vi.mock('@mantine/core', () => ({
  Avatar: ({ src, size, ...props }: any) => (
    <div data-testid='avatar' data-src={src} data-size={size} {...props} />
  ),
  Flex: ({ children, gap, direction, style, w, align, mb, ...props }: any) => (
    <div
      data-testid='flex-component'
      data-gap={gap}
      data-direction={direction}
      data-w={w}
      data-align={align}
      data-mb={mb}
      style={style}
      {...props}
    >
      {children}
    </div>
  ),
  Group: ({ children, justify, gap, mt, ...props }: any) => (
    <div
      data-testid='group-component'
      data-justify={justify}
      data-gap={gap}
      data-mt={mt}
      {...props}
    >
      {children}
    </div>
  ),
  Image: ({ src, height, radius, ...props }: any) => (
    <img
      data-testid='image-component'
      data-src={src}
      data-height={height}
      data-radius={radius}
      {...props}
      alt='Image'
    />
  ),
  Modal: ({ children, size, opened, onClose, centered, className, title, ...props }: any) => (
    <div
      data-testid='modal-component'
      data-size={size}
      data-opened={opened}
      data-centered={centered}
      data-class-name={className}
      data-title={title}
      {...props}
    >
      <button type='button' data-testid='modal-close-button' onClick={onClose}>
        Close Modal
      </button>
      {children}
    </div>
  ),
  Paper: ({ children, p, radius, withBorder, w, h, mb, ...props }: any) => (
    <div
      data-testid='paper-component'
      data-p={p}
      data-radius={radius}
      data-with-border={withBorder}
      data-w={w}
      data-h={h}
      data-mb={mb}
      {...props}
    >
      {children}
    </div>
  ),
  Text: ({ children, c, fw, fz, size, mt, mb, style, ...props }: any) => (
    <div
      data-testid='text-component'
      data-c={c}
      data-fw={fw}
      data-fz={fz}
      data-size={size}
      data-mt={mt}
      data-mb={mb}
      style={style}
      {...props}
    >
      {children}
    </div>
  ),
  TextInput: ({ label, withAsterisk, maxLength, classNames, ...props }: any) => (
    <div
      data-testid='text-input-component'
      data-label={label}
      data-with-asterisk={withAsterisk}
      data-max-length={maxLength}
    >
      <label htmlFor=''>{label}</label>
      <input data-testid={`input-${label?.toLowerCase().replace(/\s+/g, '-')}`} {...props} />
    </div>
  ),
  Textarea: ({ label, description, minRows, maxRows, maxLength, classNames, ...props }: any) => (
    <div
      data-testid='textarea-component'
      data-label={label}
      data-description={description}
      data-min-rows={minRows}
      data-max-rows={maxRows}
      data-max-length={maxLength}
    >
      <label htmlFor=''>{label}</label>
      {description && <div data-testid='description'>{description}</div>}
      <textarea data-testid={`textarea-${label?.toLowerCase().replace(/\s+/g, '-')}`} {...props} />
    </div>
  ),
  rem: (value: number) => `${value}rem`,
}));

vi.mock('@mantine/form', () => ({
  useForm: vi.fn(() => ({
    values: {
      name: '',
      description: '',
      url: '',
      image: '',
    },
    errors: {},
    getInputProps: vi.fn((_field: string) => ({
      value: '',
      onChange: vi.fn(),
      onBlur: vi.fn(),
      error: undefined,
    })),
    onSubmit: vi.fn((handler: any) => (e: any) => {
      e.preventDefault();
      handler({ name: 'Test Page', description: 'Test Description', url: 'test-page', image: '' });
    }),
    setValues: vi.fn(),
    setFieldValue: vi.fn(),
    setFieldError: vi.fn(),
    reset: vi.fn(),
  })),
  zodResolver: vi.fn(),
}));

vi.mock('@resola-ai/ui', () => ({
  DecaButton: ({ children, variant, onClick, type, disabled, ...props }: any) => (
    <button
      data-testid='deca-button'
      data-variant={variant}
      data-type={type}
      data-disabled={disabled}
      onClick={onClick}
      {...props}
    >
      {children}
    </button>
  ),
}));

vi.mock('@tolgee/react', () => ({
  useTranslate: () => ({
    t: (key: string, _options?: any) => {
      const translations: Record<string, string> = {
        requiredError: 'This field is required',
        invalidUrl: 'Invalid URL format',
        urlAlreadyExists: 'URL already exists',
        settings: 'Settings',
        createNewPage: 'Create New Page',
        information: 'Information',
        pageName: 'Page Name',
        url: 'URL',
        description: 'Description',
        descriptionHelperText: 'Enter a description for your page',
        socialPreviewImage: 'Social Preview Image',
        socialPreviewImageHelperText:
          'This image will be used when your page is shared on social media',
        preview: 'Preview',
        cancel: 'Cancel',
        save: 'Save',
      };
      return translations[key] || key;
    },
  }),
}));

// Mock react-router-dom with MemoryRouter
vi.mock('react-router-dom', async () => {
  const actual = await vi.importActual('react-router-dom');
  return {
    ...actual,
    useParams: vi.fn(() => ({ siteId: 'test-site-id' })),
  };
});

vi.mock('swr', () => ({
  default: vi.fn(() => ({
    data: {
      id: 'test-site-id',
      name: 'Test Site',
      visual_assets: {
        favicon: 'test-favicon.svg',
      },
    },
  })),
}));

vi.mock('../PageSettings/General/SelectFileBtn', () => ({
  default: ({ resourceType, onSelect }: any) => (
    <button
      type='button'
      data-testid='select-file-btn'
      data-resource-type={resourceType}
      onClick={() => onSelect('test-image.jpg')}
    >
      Select File
    </button>
  ),
}));

vi.mock('./CreateUpdatePageModal.module.css', () => ({
  default: {
    modal: 'modal-class',
    label: 'label-class',
  },
}));

describe('CreateUpdatePageModal', () => {
  const defaultProps = {
    isOpen: true,
    onClose: vi.fn(),
    mutatePages: vi.fn(),
  };

  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('should render without crashing', () => {
    render(
      <MemoryRouter>
        <CreateUpdatePageModal {...defaultProps} />
      </MemoryRouter>
    );

    expect(screen.getByTestId('modal-component')).toBeInTheDocument();
    expect(screen.getByTestId('form-provider')).toBeInTheDocument();
  });

  it('should render modal with correct props when creating new page', () => {
    render(
      <MemoryRouter>
        <CreateUpdatePageModal {...defaultProps} />
      </MemoryRouter>
    );

    const modal = screen.getByTestId('modal-component');
    expect(modal).toHaveAttribute('data-opened', 'true');
    expect(modal).toHaveAttribute('data-centered', 'true');
    expect(modal).toHaveAttribute('data-title', 'Create New Page');
    expect(modal).toHaveAttribute('data-size', '1000rem');
  });

  it('should render modal with correct props when editing existing page', () => {
    const selectedPage = {
      logical_id: 'test-page-id',
      name: 'Test Page',
      url: 'test-page',
      metadata: {
        description: 'Test Description',
        image: 'test-image.jpg',
      },
    } as any;

    render(
      <MemoryRouter>
        <CreateUpdatePageModal {...defaultProps} selectedPage={selectedPage} />
      </MemoryRouter>
    );

    const modal = screen.getByTestId('modal-component');
    expect(modal).toHaveAttribute('data-title', 'Settings');
  });

  it('should render form inputs with correct labels', () => {
    render(
      <MemoryRouter>
        <CreateUpdatePageModal {...defaultProps} />
      </MemoryRouter>
    );

    expect(screen.getByText('Page Name')).toBeInTheDocument();
    expect(screen.getByText('URL')).toBeInTheDocument();
    expect(screen.getByText('Description')).toBeInTheDocument();
  });

  it('should render required asterisks for mandatory fields', () => {
    render(
      <MemoryRouter>
        <CreateUpdatePageModal {...defaultProps} />
      </MemoryRouter>
    );

    const textInputs = screen.getAllByTestId('text-input-component');

    // Page name input
    expect(textInputs[0]).toHaveAttribute('data-with-asterisk', 'true');
    expect(textInputs[0]).toHaveAttribute('data-label', 'Page Name');

    // URL input
    expect(textInputs[1]).toHaveAttribute('data-with-asterisk', 'true');
    expect(textInputs[1]).toHaveAttribute('data-label', 'URL');
  });

  it('should render textarea with correct properties', () => {
    render(
      <MemoryRouter>
        <CreateUpdatePageModal {...defaultProps} />
      </MemoryRouter>
    );

    const textarea = screen.getByTestId('textarea-component');
    expect(textarea).toHaveAttribute('data-label', 'Description');
    expect(textarea).toHaveAttribute('data-description', 'Enter a description for your page');
    expect(textarea).toHaveAttribute('data-min-rows', '4');
    expect(textarea).toHaveAttribute('data-max-rows', '4');
    expect(textarea).toHaveAttribute('data-max-length', '150');
  });

  it('should render social preview image section', () => {
    render(
      <MemoryRouter>
        <CreateUpdatePageModal {...defaultProps} />
      </MemoryRouter>
    );

    expect(screen.getByText('Social Preview Image')).toBeInTheDocument();
    expect(
      screen.getByText('This image will be used when your page is shared on social media')
    ).toBeInTheDocument();
    expect(screen.getByTestId('select-file-btn')).toBeInTheDocument();
  });

  it('should render preview section with correct structure', () => {
    render(
      <MemoryRouter>
        <CreateUpdatePageModal {...defaultProps} />
      </MemoryRouter>
    );

    expect(screen.getByText('Preview')).toBeInTheDocument();
    expect(screen.getByTestId('paper-component')).toBeInTheDocument();
    expect(screen.getByTestId('avatar')).toBeInTheDocument();
    expect(screen.getByTestId('image-component')).toBeInTheDocument();
  });

  it('should render action buttons', () => {
    render(
      <MemoryRouter>
        <CreateUpdatePageModal {...defaultProps} />
      </MemoryRouter>
    );

    const buttons = screen.getAllByTestId('deca-button');
    expect(buttons).toHaveLength(2);

    const cancelButton = buttons[0];
    const saveButton = buttons[1];

    expect(cancelButton).toHaveAttribute('data-variant', 'neutral');
    expect(cancelButton).toHaveTextContent('Cancel');

    expect(saveButton).toHaveAttribute('data-variant', 'primary');
    expect(saveButton).toHaveAttribute('data-type', 'submit');
    expect(saveButton).toHaveTextContent('Save');
  });

  it('should handle modal close', () => {
    render(
      <MemoryRouter>
        <CreateUpdatePageModal {...defaultProps} />
      </MemoryRouter>
    );

    const closeButton = screen.getByTestId('modal-close-button');
    fireEvent.click(closeButton);

    expect(defaultProps.onClose).toHaveBeenCalledTimes(1);
  });

  it('should handle cancel button click', () => {
    render(
      <MemoryRouter>
        <CreateUpdatePageModal {...defaultProps} />
      </MemoryRouter>
    );

    const cancelButton = screen.getAllByTestId('deca-button')[0];
    fireEvent.click(cancelButton);

    expect(defaultProps.onClose).toHaveBeenCalledTimes(1);
  });

  it('should render flex layout with correct properties', () => {
    render(
      <MemoryRouter>
        <CreateUpdatePageModal {...defaultProps} />
      </MemoryRouter>
    );

    const flexComponents = screen.getAllByTestId('flex-component');

    // Main flex container
    expect(flexComponents[0]).toHaveAttribute('data-gap', '30rem');

    // Left column (information)
    expect(flexComponents[1]).toHaveAttribute('data-direction', 'column');
    expect(flexComponents[1]).toHaveAttribute('data-gap', '12rem');

    // File selection flex (inside left column)
    expect(flexComponents[2]).toHaveAttribute('data-gap', '4rem');

    // Right column (preview)
    expect(flexComponents[3]).toHaveAttribute('data-direction', 'column');
    expect(flexComponents[3]).toHaveAttribute('data-gap', '12rem');
    expect(flexComponents[3]).toHaveAttribute('data-w', '40%');

    // Preview header flex (inside right column)
    expect(flexComponents[4]).toHaveAttribute('data-align', 'center');
    expect(flexComponents[4]).toHaveAttribute('data-gap', '10rem');
  });

  it('should render group component with correct properties', () => {
    render(
      <MemoryRouter>
        <CreateUpdatePageModal {...defaultProps} />
      </MemoryRouter>
    );

    const groupComponent = screen.getByTestId('group-component');
    expect(groupComponent).toHaveAttribute('data-justify', 'flex-end');
  });

  it('should render text components with correct styling', () => {
    render(
      <MemoryRouter>
        <CreateUpdatePageModal {...defaultProps} />
      </MemoryRouter>
    );

    const textComponents = screen.getAllByTestId('text-component');

    // Information section title
    expect(textComponents[0]).toHaveAttribute('data-c', 'decaNavy.4');
    expect(textComponents[0]).toHaveAttribute('data-fw', '700');
    expect(textComponents[0]).toHaveAttribute('data-fz', '12');
    expect(textComponents[0]).toHaveTextContent('Information');

    // Preview section title - find it by text content
    const previewTitle = textComponents.find((component) => component.textContent === 'Preview');
    expect(previewTitle).toHaveAttribute('data-c', 'decaNavy.4');
    expect(previewTitle).toHaveAttribute('data-fw', '700');
    expect(previewTitle).toHaveAttribute('data-fz', '12');
  });

  it('should render paper component with correct properties', () => {
    render(
      <MemoryRouter>
        <CreateUpdatePageModal {...defaultProps} />
      </MemoryRouter>
    );

    const paperComponent = screen.getByTestId('paper-component');
    expect(paperComponent).toHaveAttribute('data-p', 'lg');
    expect(paperComponent).toHaveAttribute('data-radius', 'lg');
    expect(paperComponent).toHaveAttribute('data-with-border', 'true');
    expect(paperComponent).toHaveAttribute('data-w', '100%');
    expect(paperComponent).toHaveAttribute('data-h', 'contain');
    expect(paperComponent).toHaveAttribute('data-mb', '20rem');
  });

  it('should render avatar with correct properties', () => {
    render(
      <MemoryRouter>
        <CreateUpdatePageModal {...defaultProps} />
      </MemoryRouter>
    );

    const avatar = screen.getByTestId('avatar');
    expect(avatar).toHaveAttribute('data-src', 'test-favicon.svg');
    expect(avatar).toHaveAttribute('data-size', '24rem');
  });

  it('should render image with correct properties', () => {
    render(
      <MemoryRouter>
        <CreateUpdatePageModal {...defaultProps} />
      </MemoryRouter>
    );

    const image = screen.getByTestId('image-component');
    expect(image).toHaveAttribute('data-src', 'mocked-default-thumbnail.svg');
    expect(image).toHaveAttribute('data-height', '160');
  });

  it('should render select file button with correct properties', () => {
    render(
      <MemoryRouter>
        <CreateUpdatePageModal {...defaultProps} />
      </MemoryRouter>
    );

    const selectFileBtn = screen.getByTestId('select-file-btn');
    expect(selectFileBtn).toHaveAttribute('data-resource-type', 'page');
    expect(selectFileBtn).toHaveTextContent('Select File');
  });

  it('should handle form submission', async () => {
    render(
      <MemoryRouter>
        <CreateUpdatePageModal {...defaultProps} />
      </MemoryRouter>
    );

    const form = screen.getByTestId('form-provider');
    fireEvent.submit(form);

    // The form submission should be handled by the mocked onSubmit
    expect(form).toBeInTheDocument();
  });

  it('should handle form submission for existing page', async () => {
    const selectedPage = {
      logical_id: 'test-page-id',
      name: 'Test Page',
      url: 'test-page',
      metadata: {
        description: 'Test Description',
        image: 'test-image.jpg',
      },
    } as any;

    render(
      <MemoryRouter>
        <CreateUpdatePageModal {...defaultProps} selectedPage={selectedPage} />
      </MemoryRouter>
    );

    const form = screen.getByTestId('form-provider');
    fireEvent.submit(form);

    // The form submission should be handled by the mocked onSubmit
    expect(form).toBeInTheDocument();
  });

  it('should handle API error for duplicate URL', async () => {
    render(
      <MemoryRouter>
        <CreateUpdatePageModal {...defaultProps} />
      </MemoryRouter>
    );

    const form = screen.getByTestId('form-provider');
    fireEvent.submit(form);

    // The form submission should be handled by the mocked onSubmit
    expect(form).toBeInTheDocument();
  });

  it('should render with correct CSS classes', () => {
    render(
      <MemoryRouter>
        <CreateUpdatePageModal {...defaultProps} />
      </MemoryRouter>
    );

    const modal = screen.getByTestId('modal-component');
    expect(modal).toHaveAttribute('data-class-name', 'modal-class');
  });

  it('should render text inputs with correct max lengths', () => {
    render(
      <MemoryRouter>
        <CreateUpdatePageModal {...defaultProps} />
      </MemoryRouter>
    );

    const textInputs = screen.getAllByTestId('text-input-component');

    // Page name input
    expect(textInputs[0]).toHaveAttribute('data-max-length', '100');
    expect(textInputs[0]).toHaveAttribute('data-label', 'Page Name');

    // URL input
    expect(textInputs[1]).toHaveAttribute('data-max-length', '100');
    expect(textInputs[1]).toHaveAttribute('data-label', 'URL');
  });

  it('should render textarea with correct max length', () => {
    render(
      <MemoryRouter>
        <CreateUpdatePageModal {...defaultProps} />
      </MemoryRouter>
    );

    const textarea = screen.getByTestId('textarea-component');
    expect(textarea).toHaveAttribute('data-max-length', '150');
  });

  it('should handle file selection', () => {
    render(
      <MemoryRouter>
        <CreateUpdatePageModal {...defaultProps} />
      </MemoryRouter>
    );

    const selectFileBtn = screen.getByTestId('select-file-btn');
    fireEvent.click(selectFileBtn);

    // The mock should call onSelect with 'test-image.jpg'
    // This would typically update the form field value
  });

  it('should render preview content correctly', () => {
    render(
      <MemoryRouter>
        <CreateUpdatePageModal {...defaultProps} />
      </MemoryRouter>
    );

    // Check that preview shows site name and dash
    expect(screen.getByText(/Test Site/)).toBeInTheDocument();

    // Check that preview shows URL
    expect(screen.getByText('yoursite.url')).toBeInTheDocument();
  });

  it('should handle missing site data gracefully', () => {
    render(
      <MemoryRouter>
        <CreateUpdatePageModal {...defaultProps} />
      </MemoryRouter>
    );

    // Should still render without crashing
    expect(screen.getByTestId('modal-component')).toBeInTheDocument();
  });

  it('should handle missing siteId gracefully', () => {
    render(
      <MemoryRouter>
        <CreateUpdatePageModal {...defaultProps} />
      </MemoryRouter>
    );

    // Should still render without crashing
    expect(screen.getByTestId('modal-component')).toBeInTheDocument();
  });
});
