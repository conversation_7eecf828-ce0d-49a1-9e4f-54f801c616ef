import DefaultSiteThumbnail from '@/assets/images/default-site-thumbnail.svg';
import { type Site, SiteStatus } from '@/types';
import { SitePath } from '@/types/enum';
import { showNotificationToast } from '@/utils/notification';
import { Card, Flex, HoverCard, Image, Menu, Text } from '@mantine/core';
import { DecaButton } from '@resola-ai/ui';
import { Colors } from '@resola-ai/ui/constants';
import {
  IconCopy,
  IconDotsVertical,
  IconPencil,
  IconPointFilled,
  IconSettings,
} from '@tabler/icons-react';
import { useTranslate } from '@tolgee/react';
import { useState } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import UpdateSiteModal from '../SiteModal/UpdateSiteModal';
import classes from './SiteItem.module.css';

const isSitePublished = (site: Site) =>
  site.status === SiteStatus.Published && !site.domains?.every((domain) => !domain.is_enabled);

const SiteItem = ({ site, mutate }: { site: Site; mutate: () => void }) => {
  const { t } = useTranslate('workspace');
  const navigate = useNavigate();
  const [openRenameModal, setOpenRenameModal] = useState(false);

  const handleRename = () => {
    mutate();
    setOpenRenameModal(false);
  };

  const handleNavigate = (path: string) => {
    navigate(`/pages/${path}/${site.id}`);
  };

  const handleCopy = () => {
    navigator.clipboard.writeText(site?.domains?.[0]?.domain ?? '');
    showNotificationToast({
      message: t('copiedToClipboard'),
    });
  };

  return (
    <Card withBorder shadow='sm' className={classes.card} radius='lg'>
      <div className={classes.thumbnail}>
        <Image
          className={classes.thumbnailImage}
          src={site?.visual_assets?.thumbnail ?? DefaultSiteThumbnail}
          alt={site.name}
          radius='sm'
        />
      </div>
      <div className={classes.content}>
        <Flex>
          <HoverCard width={280} shadow='md' position='top'>
            <HoverCard.Target>
              <IconPointFilled
                size={20}
                color={isSitePublished(site) ? Colors.decaGreen[5] : Colors.decaRed[5]}
              />
            </HoverCard.Target>
            <HoverCard.Dropdown w='auto'>
              <Text>{t(`versionStatus.${site?.version_status}`)}</Text>
            </HoverCard.Dropdown>
          </HoverCard>
          <Text fw={500} truncate>
            {site?.name}
          </Text>
        </Flex>
        <Link
          to={`https://${site?.domains?.[0]?.domain ?? ''}`}
          target='_blank'
          className={classes.domainLink}
        >
          {site?.domains?.[0]?.domain}
        </Link>

        <DecaButton
          variant='neutral'
          size='sm'
          radius='sm'
          color={Colors.decaGrey[8]}
          onClick={() => handleNavigate(SitePath.Builder)}
        >
          {t('editSite')}
        </DecaButton>
      </div>
      <Flex ml='auto'>
        <Menu shadow='md' width={200}>
          <Menu.Target>
            <IconDotsVertical size={16} cursor='pointer' color={Colors.decaGrey[6]} />
          </Menu.Target>
          <Menu.Dropdown>
            <Menu.Item
              leftSection={<IconPencil size={16} />}
              onClick={() => setOpenRenameModal(true)}
            >
              {t('rename')}
            </Menu.Item>
            {site?.domains?.[0]?.domain && (
              <Menu.Item leftSection={<IconCopy size={16} />} onClick={() => handleCopy()}>
                {t('copy')}
              </Menu.Item>
            )}
            <Menu.Item
              leftSection={<IconSettings size={16} />}
              onClick={() => handleNavigate(SitePath.Settings)}
            >
              {t('settings')}
            </Menu.Item>
          </Menu.Dropdown>
        </Menu>
      </Flex>
      <UpdateSiteModal isOpen={openRenameModal} onClose={handleRename} currentSite={site} />
    </Card>
  );
};

export default SiteItem;
