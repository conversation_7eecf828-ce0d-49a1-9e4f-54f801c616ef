.card {
  width: 100%;
  min-height: rem(160px);
  flex-direction: row !important;
  display: flex;
  align-items: flex-start;
  gap: 16px;
}

.thumbnail {
  flex-shrink: 0;
  width: rem(250px);
  height: rem(160px);
  min-width: rem(100px);
  min-height: rem(128px);
  max-width: rem(250px);
  max-height: rem(160px);
}

.content {
  flex: 1;
  min-width: 0;
  display: flex;
  flex-direction: column;
  gap: rem(8px);
  align-self: center;
}

.thumbnail img {
  object-fit: cover;
  width: 100%;
  height: 100%;
}

.thumbnailImage {
  border: 1px solid var(--mantine-color-decaLight-2);
}

.domainLink {
  text-decoration: none;
  color: var(--mantine-color-decaGrey-5);
  max-width: 100%;
  text-overflow: ellipsis;
  overflow: hidden;
  white-space: nowrap;

  &:hover {
    color: var(--mantine-color-decaBlue-5) !important;
  }
}
