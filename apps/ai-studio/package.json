{"name": "ai-studio", "private": true, "version": "1.2.0", "type": "module", "description": "DECA AI Studio", "scripts": {"fetch-schema": "node ../../packages/scripts/src/fetchNodeSchemas.js", "fetch-schema:en": "pnpm run fetch-schema -- --lang=en", "fetch-schema:ja": "pnpm run fetch-schema -- --lang=ja", "predev": "pnpm run fetch-schema", "prebuild": "pnpm run fetch-schema", "dev": "vite", "build": "tsc && vite build", "lint": "biome lint .", "prepare:web:pr": "node ../../packages/scripts/src/preparePreviewEnv.js", "preview": "vite preview", "format": "biome format --write .", "lint-staged-check": "lint-staged", "lint:eslint:fix": "biome lint --apply .", "test": "vitest --run", "test:watch": "vitest", "test:unit": "vitest --run", "coverage": "vitest --run --coverage --reporter=verbose", "release": "standard-version -t ai-studio@", "release:minor": "standard-version -t ai-studio@ --release-as minor", "release:patch": "standard-version -t ai-studio@ --release-as patch", "release:major": "standard-version -t ai-studio@ --release-as major", "tolgee:safe-push": "cd ../.. && pnpm run tolgee:safe-push ai-studio"}, "dependencies": {"@auth0/auth0-react": "^2.2.4", "@emotion/cache": "^11.14.0", "@emotion/react": "^11.14.0", "@emotion/serialize": "^1.3.3", "@emotion/styled": "^11.11.0", "@emotion/utils": "1.4.2", "@hookform/resolvers": "^3.3.1", "@mantine/core": "7.17.7", "@mantine/dates": "7.17.7", "@mantine/emotion": "7.17.7", "@mantine/form": "7.17.7", "@mantine/hooks": "7.17.7", "@mantine/modals": "7.17.7", "@mantine/notifications": "7.17.7", "@monaco-editor/react": "^4.6.0", "@resola-ai/models": "workspace:*", "@resola-ai/services-shared": "workspace:*", "@resola-ai/shared-constants": "workspace:*", "@resola-ai/ui": "workspace:*", "@resola-ai/utils": "workspace:*", "@tabler/icons-react": "3.17.0", "@tiptap/core": "2.10.4", "@tiptap/react": "^2.12.0", "@tiptap/starter-kit": "^2.12.0", "@tolgee/react": "^5.29.1", "@tolgee/web": "^5.29.2", "@xyflow/react": "12.5.1", "axios": "^1.12.0", "centrifuge": "5.0.0", "dayjs": "^1.11.12", "dompurify": "^3.2.4", "dotenv": "16.4.7", "elkjs": "^0.8.2", "eventsource-parser": "^3.0.2", "jwt-decode": "^3.1.2", "lodash": "^4.17.21", "nanoid": "^5.0.9", "react": "^18.2.0", "react-dnd": "^16.0.1", "react-dnd-html5-backend": "^16.0.1", "react-dom": "^18.2.0", "react-hook-form": "7.54.2", "react-hook-form-mantine": "^3.1.3", "react-markdown": "9.0.1", "react-router-dom": "6.21.3", "react-syntax-highlighter": "15.6.1", "remark-gfm": "4.0.1", "standard-version": "^9.5.0", "swr": "^2.3.0", "ulid": "^2.3.0", "zod": "3.24.1"}, "devDependencies": {"@biomejs/biome": "^1.5.3", "@resola-ai/biome-config": "workspace:*", "@resola-ai/typescript-config": "workspace:*", "@testing-library/jest-dom": "^6.2.0", "@testing-library/react": "^14.1.2", "@testing-library/user-event": "^14.5.2", "@types/json-schema": "^7.0.15", "@types/lodash": "^4.17.13", "@types/react": "^18.2.47", "@types/react-dom": "^18.2.18", "@vitejs/plugin-react-swc": "^3.5.0", "@vitest/coverage-v8": "2.1.9", "history": "^5.3.0", "jest-environment-jsdom": "^29.7.0", "jsdom": "^23.2.0", "lint-staged": "^15.5.0", "postcss-preset-mantine": "^1.12.3", "typescript": "5.6.3", "vite": "npm:rolldown-vite@latest", "vite-plugin-circular-dependency": "^0.4.1", "vite-tsconfig-paths": "^4.2.0", "vitest": "2.1.9"}, "lint-staged": {"*.{js,ts,tsx,jsx}": ["biome format --write", "biome lint --apply"]}}