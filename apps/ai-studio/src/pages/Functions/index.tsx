import { PageHeaderWithActions } from '@/components';
import AIPagination from '@/components/AIPagination';
import { FunctionList } from '@/components/Functions';
import { FunctionContextProvider, useFunctionContext } from '@/contexts/Function/FunctionContext';
import { useRestrictLimitParam } from '@/hooks/useRestrictLimitParam';
import type { IFunction } from '@/models/function';
import { LayoutType } from '@/types';
import { Box, rem } from '@mantine/core';
import { createStyles } from '@mantine/emotion';
import { useDisclosure } from '@mantine/hooks';
import { useTranslate } from '@tolgee/react';
import { debounce } from 'lodash';
import { useCallback, useEffect, useMemo, useState } from 'react';
import { useSearchParams } from 'react-router-dom';

const useStyles = createStyles(() => ({
  pageHeader: {
    marginBottom: rem(24),
  },
  pagination: {
    marginBottom: rem(24),
  },
}));

const Functions = () => {
  const { t } = useTranslate('function');
  const { classes } = useStyles();
  const [_, setSearchParams] = useSearchParams();
  const {
    functions,
    searchValue,
    limit,
    setLimit,
    cursor,
    setCursor,
    updateSearchText,
    isLoadingFunctions,
    importFunction,
  } = useFunctionContext();
  const [openedEditModal, { open: openEditModal, close: closeEditModal }] = useDisclosure(false);
  const [layoutType, setLayoutType] = useState<LayoutType>(LayoutType.GRID);
  const [search, setSearch] = useState('');
  const [isCreatingFunction, setIsCreatingFunction] = useState<boolean>(false);
  const [editFunction, setEditFunction] = useState<IFunction | null>(null);

  const debounceSearch = useMemo(
    () =>
      debounce((value: string) => {
        updateSearchText(value);
      }, 500),
    [updateSearchText]
  );

  useEffect(() => {
    setSearch(searchValue);
  }, [searchValue]);

  useEffect(() => {
    return () => {
      debounceSearch.cancel();
    };
  }, [debounceSearch]);

  const handleLayoutChange = (value: LayoutType) => {
    setLayoutType(value);
  };

  const handleSearchChange = useCallback(
    (val: string) => {
      setSearch(val);
      debounceSearch(val);
    },
    [debounceSearch]
  );

  const openModalCreateFunction = useCallback(() => {
    setIsCreatingFunction(true);
    openEditModal();
  }, [setIsCreatingFunction, openEditModal]);

  const handleCloseModal = useCallback(() => {
    closeEditModal();
    setIsCreatingFunction(false);
    setEditFunction(null);
  }, [setIsCreatingFunction, setEditFunction, closeEditModal]);

  const handleLimitChange = useCallback(
    (limit: number) => {
      setLimit(limit);
      setCursor('');
    },
    [setLimit, setCursor]
  );

  useRestrictLimitParam(limit, setLimit, setSearchParams);

  return (
    <Box data-testid='functions-page'>
      <PageHeaderWithActions
        title={t('title')}
        description={t('description')}
        searchPlaceholder={t('searchPlaceholder')}
        searchValue={search}
        onSearchChange={handleSearchChange}
        layoutType={layoutType}
        onLayoutChange={handleLayoutChange}
        buttonActionLabel={t('createFunctionLabel')}
        className={classes.pageHeader}
        hasData={!!functions?.data?.length}
        handleCreateFromScratch={openModalCreateFunction}
        handleImportFromFile={importFunction}
        isUsingButtonMenuActions
      />
      {!isLoadingFunctions && (
        <Box mb={rem(30)}>
          <FunctionList
            functions={functions?.data ?? []}
            isFullWidth={layoutType === LayoutType.LIST}
            openedEditModal={openedEditModal}
            openEditModal={openEditModal}
            closeEditModal={handleCloseModal}
            editFunction={editFunction}
            setEditFunction={setEditFunction}
            isCreatingFunction={isCreatingFunction}
          />
        </Box>
      )}
      {functions && functions?.data?.length > 0 && (
        <AIPagination
          key={cursor}
          limit={limit}
          onCursorChange={setCursor}
          onChangeLimit={handleLimitChange}
          nextCursor={functions?.nextCursor}
          prevCursor={functions?.prevCursor}
          onNext={() => setCursor(functions?.nextCursor ?? '')}
          onPrevious={() => setCursor(functions?.prevCursor ?? '')}
        />
      )}
    </Box>
  );
};

const FunctionPage = () => {
  return (
    <FunctionContextProvider>
      <Functions />
    </FunctionContextProvider>
  );
};
export default FunctionPage;
