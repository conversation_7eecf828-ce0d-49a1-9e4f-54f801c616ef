import { DEFAULT_PER_PAGE } from '@/constants/common';
import { useHandleApiError } from '@/hooks/useHandleApiError';
import { CredentialType, type IStudioSuccessListResponse } from '@/models';
import { CredentialAPI } from '@/services/api/credential';
import { type ICredential, type ICredentialPayload, useNodeSchemas } from '@resola-ai/ui';
import { openAuth0Popup } from '@resola-ai/utils';
import type React from 'react';
import { createContext, useContext, useState } from 'react';
import { useParams } from 'react-router-dom';
import useSWR from 'swr';

export interface CredentialContextType {
  credentials: IStudioSuccessListResponse<ICredential> | undefined;
  tools: any[];
  limit: number;
  cursor: string;
  setLimit: (limit: number) => void;
  setCursor: (cursor: string) => void;
  searchValue: string;
  setSearchValue: (value: string) => void;
  loading: boolean;
  createCredential: (credential: ICredentialPayload) => Promise<void>;
  updateCredential: (credential: ICredentialPayload) => Promise<void>;
  deleteCredential: (id: string, isHandleError?: boolean) => Promise<void>;
  testCredential: (credential: ICredentialPayload, isHandleError?: boolean) => Promise<void>;
  reconnectCredential: (id: string, isHandleError?: boolean) => Promise<void>;
  selectedCredential?: ICredential;
  setSelectedCredential: (credential: ICredential | undefined) => void;
  selectedTool?: any;
  setSelectedTool: (tool: any | undefined) => void;
  loadingCredential: boolean;
  fetchCredential: (id: string) => Promise<void>;
}

const getErrorDetails = (error: any, options?: { defaultMessage?: string }) => {
  const errorCode = error?.response?.data?.error?.errorCode;
  const errorMessage =
    error?.response?.data?.error?.details ||
    error?.response?.data?.error?.message ||
    error?.message ||
    options?.defaultMessage;
  return { errorCode, errorMessage };
};

export const useCredential = () => {
  const { workspaceId } = useParams();
  const [selectedCredential, setSelectedCredential] = useState<ICredential | undefined>();
  const [selectedTool, setSelectedTool] = useState<any>();
  const { getCredentialNodes } = useNodeSchemas();
  const [tools, _] = useState<any[]>(getCredentialNodes);
  const [limit, setLimit] = useState<number>(DEFAULT_PER_PAGE); // Ensure default is correctly set
  const [cursor, setCursor] = useState('');
  const [searchValue, setSearchValue] = useState('');
  const [loading, setLoading] = useState(false);
  const [loadingCredential, setLoadingCredential] = useState(false);
  const { handleApiError } = useHandleApiError();
  const { data: credentials, mutate } = useSWR<IStudioSuccessListResponse<ICredential>>(
    workspaceId ? ['credentials', workspaceId, limit, cursor, searchValue] : null,
    () =>
      CredentialAPI.getList(workspaceId || '', limit, cursor, {
        name: searchValue,
      }),
    {
      revalidateOnFocus: false,
    }
  );

  const fetchCredential = async (id: string) => {
    if (!workspaceId) {
      return;
    }

    try {
      setLoadingCredential(true);
      const data = await CredentialAPI.get(workspaceId, id);
      setSelectedCredential(data);
    } catch (err) {
      handleApiError(err);
    } finally {
      setLoadingCredential(false);
    }
  };

  const handleAuth0 = async (id: string) => {
    if (!workspaceId || !id) {
      return;
    }

    try {
      const auth0 = await CredentialAPI.getAuth0(workspaceId, id);
      openAuth0Popup(auth0);
    } catch (err) {
      const { errorMessage } = getErrorDetails(err);
      throw new Error(errorMessage);
    }
  };

  const createCredential = async (credential: ICredentialPayload) => {
    if (!workspaceId) {
      return;
    }
    try {
      setLoading(true);
      const result = await CredentialAPI.create(workspaceId, credential);
      setSelectedCredential(result);
      if (credential.authenticationScheme === CredentialType.OAUTH2) {
        await handleAuth0(result.id);
      }
    } catch (err) {
      const { errorMessage } = getErrorDetails(err);
      throw new Error(errorMessage);
    } finally {
      mutate();
      setLoading(false);
    }
  };

  const updateCredential = async (credential: ICredentialPayload) => {
    if (!workspaceId) {
      return;
    }

    try {
      setLoading(true);
      const result = await CredentialAPI.update(workspaceId, credential);
      if (result.authenticationScheme === CredentialType.OAUTH2 && !result?.settings?.accessToken) {
        await handleAuth0(result.id);
      }
    } catch (err) {
      const { errorMessage } = getErrorDetails(err);
      throw new Error(errorMessage);
    } finally {
      mutate();
      setLoading(false);
    }
  };

  const deleteCredential = async (id: string, isHandleError?: boolean) => {
    if (!workspaceId) {
      return;
    }

    try {
      setLoading(true);
      await CredentialAPI.delete(workspaceId, id);
      mutate();
    } catch (err) {
      if (isHandleError) {
        handleApiError(err);
      } else {
        const { errorMessage } = getErrorDetails(err);
        throw new Error(errorMessage);
      }
    } finally {
      setLoading(false);
    }
  };

  const testCredential = async (
    credential: ICredentialPayload,
    isHandleError?: boolean
  ): Promise<void> => {
    try {
      if (!workspaceId) {
        return;
      }

      setLoading(true);
      let data: any;
      if (credential.id) {
        data = await CredentialAPI.verifyWithId(workspaceId, credential.id);
      } else {
        data = await CredentialAPI.verifyWithPayload(workspaceId, credential);
      }
      if (data?.error) {
        throw new Error(data.error);
      }
      if (!data?.valid) {
        throw new Error(data.message);
      }
    } catch (err) {
      if (isHandleError) {
        handleApiError(err);
      } else {
        const { errorMessage } = getErrorDetails(err);
        throw new Error(errorMessage);
      }
    } finally {
      setLoading(false);
    }
  };

  const reconnectCredential = async (id: string, isHandleError?: boolean) => {
    if (!workspaceId) {
      return;
    }

    try {
      setLoading(true);
      await CredentialAPI.reconnect(workspaceId, id);
      setSelectedCredential(undefined);
    } catch (err) {
      if (isHandleError) {
        handleApiError(err);
      } else {
        const { errorMessage } = getErrorDetails(err);
        throw new Error(errorMessage);
      }
    } finally {
      setLoading(false);
    }
  };

  return {
    credentials,
    tools,
    limit,
    cursor,
    searchValue,
    setCursor,
    setLimit,
    setSearchValue,
    loading,
    loadingCredential,
    fetchCredential,
    createCredential,
    updateCredential,
    deleteCredential,
    testCredential,
    reconnectCredential,
    selectedCredential,
    setSelectedCredential,
    selectedTool,
    setSelectedTool,
  };
};

const CredentialContext = createContext<CredentialContextType | null>(null);

export const CredentialContextProvider = ({
  children,
  value,
}: {
  children: React.ReactNode;
  value?: CredentialContextType;
}) => {
  const defaultValue = useCredential();
  const contextValue = value ?? defaultValue;

  return <CredentialContext.Provider value={contextValue}>{children}</CredentialContext.Provider>;
};

export const useCredentialContext = () => {
  const context = useContext(CredentialContext);
  if (!context) {
    throw new Error('useCredentialContext must be used within a CredentialContextProvider');
  }
  return context;
};
