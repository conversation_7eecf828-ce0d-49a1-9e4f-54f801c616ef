import { DEFAULT_PER_PAGE } from '@/constants/common';
import { CredentialType, type ICredentialPayload, type ITool, ProviderType } from '@/models';
import { notifications } from '@mantine/notifications';
import type { ICredential } from '@resola-ai/ui';
import { act, renderHook } from '@testing-library/react';
import type { ReactNode } from 'react';
import { describe, expect, it, vi } from 'vitest';
import { CredentialContextProvider, useCredentialContext } from './index';

let mockWorkspaceId: string | undefined = '123';

// Mock router
vi.mock('react-router-dom', () => ({
  useParams: () => ({ workspaceId: mockWorkspaceId }),
  useSearchParams: () => [new URLSearchParams(), vi.fn()],
}));

// Mock API
vi.mock('@/services/api/credential', () => ({
  CredentialAPI: {
    get: vi.fn(),
    getList: vi.fn(),
    create: vi.fn(),
    update: vi.fn(),
    delete: vi.fn(),
    test: vi.fn(),
    reconnect: vi.fn(),
    verifyWithId: vi.fn(),
    verifyWithPayload: vi.fn(),
    getAuth0: vi.fn(),
  },
}));

// Mock SWR
vi.mock('swr', () => ({
  default: vi.fn(() => ({
    data: undefined,
    mutate: vi.fn(),
  })),
}));

// Mock useHandleApiError
vi.mock('@/hooks/useHandleApiError', () => ({
  useHandleApiError: () => ({
    handleApiError: (err: any) => {
      const message = err?.message || 'Failed';
      notifications.show({ message, color: 'red' });
    },
  }),
}));

describe('CredentialContext', () => {
  const wrapper = ({ children }: { children: ReactNode }) => (
    <CredentialContextProvider>{children}</CredentialContextProvider>
  );

  const mockCredential: ICredential = {
    id: '1',
    name: 'Test Credential',
    description: 'Test Description',
    authenticationScheme: CredentialType.API_KEY,
    provider: ProviderType.CUSTOM,
    settings: { apiKey: 'test-key' },
    metadata: {},
    workspaceId: '123',
    createdAt: new Date(),
    updatedAt: new Date(),
  };

  const mockCredentialPayload: ICredentialPayload = {
    name: 'Test Credential',
    description: 'Test Description',
    authenticationScheme: CredentialType.API_KEY,
    provider: ProviderType.CUSTOM,
    settings: { apiKey: 'test-key' },
    workspaceId: '123',
  };

  const mockCredentialPayloadWithId: ICredentialPayload = {
    id: '1',
    name: 'Test Credential',
    description: 'Test Description',
    authenticationScheme: CredentialType.API_KEY,
    provider: ProviderType.CUSTOM,
    settings: { apiKey: 'test-key' },
    workspaceId: '123',
  };

  const mockTool: ITool = {
    id: '1',
    name: ProviderType.CUSTOM,
    displayName: 'Test Tool',
    description: 'Test Description',
    categories: ['test'],
    settings: {
      credential: {
        credentialTypes: ['api_key'],
      },
    },
    credentials: [
      {
        name: 'api_key',
        displayName: 'API Key',
        description: 'API Key for authentication',
        properties: [
          {
            name: 'apiKey',
            displayName: 'API Key',
            description: 'The API key value',
            type: 'password',
            required: true,
          },
        ],
      },
    ],
  };

  // Mock window.open
  const mockWindowOpen = vi.fn();
  Object.defineProperty(window, 'open', {
    value: mockWindowOpen,
    writable: true,
  });

  beforeEach(() => {
    mockWorkspaceId = '123';
    vi.clearAllMocks();
    mockWindowOpen.mockClear();
    mockWindowOpen.mockReturnValue({ closed: false });
  });

  it('provides credential context with default values when workspaceId exists', () => {
    const { result } = renderHook(() => useCredentialContext(), { wrapper });

    expect(result.current.credentials).toEqual(undefined);
    expect(result.current.selectedCredential).toBeUndefined();
    expect(result.current.selectedTool).toBeUndefined();
    expect(result.current.loading).toBe(false);
    expect(result.current.limit).toBe(DEFAULT_PER_PAGE);
    expect(result.current.cursor).toBe('');
    expect(result.current.searchValue).toBe('');
  });

  it('useCredentialContext without provider', () => {
    expect(() => {
      renderHook(() => useCredentialContext());
    }).toThrow('useCredentialContext must be used within a CredentialContextProvider');
  });

  it('provides credential context with default values when workspaceId is undefined', () => {
    mockWorkspaceId = undefined;
    const { result } = renderHook(() => useCredentialContext(), { wrapper });

    expect(result.current.credentials).toEqual(undefined);
    expect(result.current.selectedCredential).toBeUndefined();
    expect(result.current.selectedTool).toBeUndefined();
    expect(result.current.loading).toBe(false);
    expect(result.current.limit).toBe(DEFAULT_PER_PAGE);
    expect(result.current.cursor).toBe('');
    expect(result.current.searchValue).toBe('');
  });

  it('Handle actions when workspaceId is undefined', async () => {
    mockWorkspaceId = undefined;
    const { result } = renderHook(() => useCredentialContext(), { wrapper });
    await act(async () => {
      // fetchCredentials removed, nothing to call here
    });

    expect(result.current.credentials).toEqual(undefined);
    expect(result.current.loading).toBe(false);
    // createCredential
    const mockCreatePayload: ICredentialPayload = {
      name: 'New Credential',
      description: 'New Description',
      authenticationScheme: CredentialType.API_KEY,
      provider: ProviderType.CUSTOM,
      settings: { apiKey: 'new-key' },
      metadata: {},
    };
    await act(async () => {
      await result.current.createCredential(mockCreatePayload);
    });

    expect(result.current.loading).toBe(false);

    // updateCredential
    await act(async () => {
      await result.current.updateCredential(mockCredentialPayload);
    });

    expect(result.current.loading).toBe(false);

    // deleteCredential
    await act(async () => {
      await result.current.deleteCredential('1');
    });

    expect(result.current.loading).toBe(false);

    // testCredential
    await act(async () => {
      await result.current.testCredential(mockCredentialPayload);
    });

    expect(result.current.loading).toBe(false);

    // reconnectCredential
    await act(async () => {
      await result.current.reconnectCredential('1');
    });

    expect(result.current.loading).toBe(false);

    // fetchCredential
    await act(async () => {
      await result.current.fetchCredential('1');
    });

    expect(result.current.loading).toBe(false);
    expect(result.current.selectedCredential).toBeUndefined();
  });

  it('updates credentials when setCredentials is called', () => {
    const { result } = renderHook(() => useCredentialContext(), { wrapper });

    act(() => {
      result.current.setSelectedCredential(mockCredential);
    });

    expect(result.current.selectedCredential).toEqual(mockCredential);
  });

  it('updates selectedTool when setSelectedTool is called', () => {
    const { result } = renderHook(() => useCredentialContext(), { wrapper });

    act(() => {
      result.current.setSelectedTool(mockTool);
    });

    expect(result.current.selectedTool).toEqual(mockTool);
  });

  it('handles search value updates', () => {
    const { result } = renderHook(() => useCredentialContext(), { wrapper });

    act(() => {
      result.current.setSearchValue('test');
    });

    expect(result.current.searchValue).toBe('test');
  });

  it('handles pagination updates', () => {
    const { result } = renderHook(() => useCredentialContext(), { wrapper });

    act(() => {
      result.current.setLimit(20);
      result.current.setCursor('2');
    });

    expect(result.current.limit).toBe(20);
    expect(result.current.cursor).toBe('2');
  });

  it('fetches credentials successfully', async () => {
    const mockData = {
      data: [mockCredential],
    };
    const { CredentialAPI } = await import('@/services/api/credential');
    vi.mocked(CredentialAPI.getList).mockResolvedValueOnce(mockData);
  });

  it('creates a credential successfully', async () => {
    const { CredentialAPI } = await import('@/services/api/credential');
    const mockCreatePayload: ICredentialPayload = {
      name: 'New Credential',
      description: 'New Description',
      authenticationScheme: CredentialType.API_KEY,
      provider: ProviderType.CUSTOM,
      settings: { apiKey: 'new-key' },
      metadata: {},
    };
    vi.mocked(CredentialAPI.create).mockResolvedValueOnce(mockCredential);

    const { result } = renderHook(() => useCredentialContext(), { wrapper });

    await act(async () => {
      await result.current.createCredential(mockCreatePayload);
    });

    expect(CredentialAPI.create).toHaveBeenCalledWith('123', mockCreatePayload);
    expect(result.current.loading).toBe(false);
  });

  it('handles createCredential error', async () => {
    const { CredentialAPI } = await import('@/services/api/credential');
    const mockCreatePayload: ICredentialPayload = {
      name: 'New Credential',
      description: 'New Description',
      authenticationScheme: CredentialType.API_KEY,
      provider: ProviderType.CUSTOM,
      settings: { apiKey: 'new-key' },
      metadata: {},
    };
    vi.mocked(CredentialAPI.create).mockRejectedValueOnce(new Error('Failed to create credential'));

    const { result } = renderHook(() => useCredentialContext(), { wrapper });

    await expect(async () => {
      await act(async () => {
        await result.current.createCredential(mockCreatePayload);
      });
    }).rejects.toThrow('Failed to create credential');

    expect(CredentialAPI.create).toHaveBeenCalledWith('123', mockCreatePayload);
    expect(result.current.loading).toBe(false);
  });

  it('updates a credential successfully', async () => {
    const { CredentialAPI } = await import('@/services/api/credential');
    vi.mocked(CredentialAPI.update).mockResolvedValueOnce(mockCredential);

    const { result } = renderHook(() => useCredentialContext(), { wrapper });

    await act(async () => {
      await result.current.updateCredential(mockCredentialPayload);
    });

    expect(CredentialAPI.update).toHaveBeenCalledWith('123', mockCredentialPayload);
    expect(result.current.loading).toBe(false);
  });

  it('handles updateCredential error', async () => {
    const { CredentialAPI } = await import('@/services/api/credential');
    vi.mocked(CredentialAPI.update).mockRejectedValueOnce(new Error('Failed to update credential'));

    const { result } = renderHook(() => useCredentialContext(), { wrapper });

    await expect(async () => {
      await act(async () => {
        await result.current.updateCredential(mockCredentialPayload);
      });
    }).rejects.toThrow('Failed to update credential');

    expect(CredentialAPI.update).toHaveBeenCalledWith('123', mockCredentialPayload);
    expect(result.current.loading).toBe(false);
  });

  it('deletes a credential successfully', async () => {
    const { CredentialAPI } = await import('@/services/api/credential');
    vi.mocked(CredentialAPI.delete).mockResolvedValueOnce(undefined);

    const { result } = renderHook(() => useCredentialContext(), { wrapper });

    await act(async () => {
      await result.current.deleteCredential('1');
    });

    expect(CredentialAPI.delete).toHaveBeenCalledWith('123', '1');
    expect(result.current.loading).toBe(false);
  });

  it('handles deleteCredential error', async () => {
    const { CredentialAPI } = await import('@/services/api/credential');
    vi.mocked(CredentialAPI.delete).mockRejectedValueOnce(new Error('Failed to delete credential'));
    const notificationsSpy = vi.spyOn(notifications, 'show').mockImplementation(() => '');

    const { result } = renderHook(() => useCredentialContext(), { wrapper });

    await act(async () => {
      await result.current.deleteCredential('1', true);
    });

    expect(CredentialAPI.delete).toHaveBeenCalledWith('123', '1');
    expect(result.current.loading).toBe(false);
    expect(notificationsSpy).toHaveBeenCalled();

    notificationsSpy.mockRestore();
  });

  it('tests a credential with ID successfully', async () => {
    const { CredentialAPI } = await import('@/services/api/credential');
    vi.mocked(CredentialAPI.verifyWithId).mockResolvedValueOnce({ valid: true } as any);

    const { result } = renderHook(() => useCredentialContext(), { wrapper });

    await act(async () => {
      await result.current.testCredential(mockCredentialPayloadWithId);
    });

    expect(CredentialAPI.verifyWithId).toHaveBeenCalledWith('123', '1');
    expect(result.current.loading).toBe(false);
  });

  it('tests a credential with payload successfully', async () => {
    const { CredentialAPI } = await import('@/services/api/credential');
    vi.mocked(CredentialAPI.verifyWithPayload).mockResolvedValueOnce({ valid: true } as any);
    const { result } = renderHook(() => useCredentialContext(), { wrapper });

    await act(async () => {
      await result.current.testCredential(mockCredentialPayload);
    });

    expect(CredentialAPI.verifyWithPayload).toHaveBeenCalledWith('123', mockCredentialPayload);
    expect(result.current.loading).toBe(false);
  });

  it('handles testCredential error response', async () => {
    const { CredentialAPI } = await import('@/services/api/credential');
    vi.mocked(CredentialAPI.verifyWithId).mockResolvedValueOnce({ error: 'testFailed' });

    const { result } = renderHook(() => useCredentialContext(), { wrapper });

    await expect(async () => {
      await act(async () => {
        await result.current.testCredential(mockCredentialPayloadWithId);
      });
    }).rejects.toThrow('testFailed');

    expect(CredentialAPI.verifyWithId).toHaveBeenCalledWith('123', '1');
    expect(result.current.loading).toBe(false);
  });

  it('handles testCredential API error', async () => {
    const { CredentialAPI } = await import('@/services/api/credential');
    vi.mocked(CredentialAPI.verifyWithId).mockRejectedValueOnce(
      new Error('Failed to test credential')
    );

    const { result } = renderHook(() => useCredentialContext(), { wrapper });

    await expect(async () => {
      await act(async () => {
        await result.current.testCredential(mockCredentialPayloadWithId);
      });
    }).rejects.toThrow('Failed to test credential');

    expect(CredentialAPI.verifyWithId).toHaveBeenCalledWith('123', '1');
    expect(result.current.loading).toBe(false);
  });

  it('handles testCredential with error handling enabled', async () => {
    const { CredentialAPI } = await import('@/services/api/credential');
    vi.mocked(CredentialAPI.verifyWithId).mockRejectedValueOnce(
      new Error('Failed to test credential')
    );
    const notificationsSpy = vi.spyOn(notifications, 'show');

    const { result } = renderHook(() => useCredentialContext(), { wrapper });

    await act(async () => {
      await result.current.testCredential(mockCredentialPayloadWithId, true);
    });

    expect(CredentialAPI.verifyWithId).toHaveBeenCalledWith('123', '1');
    expect(result.current.loading).toBe(false);

    notificationsSpy.mockRestore();
  });

  it('handles testCredential with payload error handling enabled', async () => {
    const { CredentialAPI } = await import('@/services/api/credential');
    vi.mocked(CredentialAPI.verifyWithPayload).mockRejectedValueOnce(
      new Error('Failed to test credential')
    );
    const notificationsSpy = vi.spyOn(notifications, 'show');

    const { result } = renderHook(() => useCredentialContext(), { wrapper });

    await act(async () => {
      await result.current.testCredential(mockCredentialPayload, true);
    });

    expect(CredentialAPI.verifyWithPayload).toHaveBeenCalledWith('123', mockCredentialPayload);
    expect(result.current.loading).toBe(false);

    notificationsSpy.mockRestore();
  });

  it('handles testCredential error response with error handling enabled', async () => {
    const { CredentialAPI } = await import('@/services/api/credential');
    vi.mocked(CredentialAPI.verifyWithId).mockResolvedValueOnce({ error: 'testFailed' });
    const notificationsSpy = vi.spyOn(notifications, 'show');

    const { result } = renderHook(() => useCredentialContext(), { wrapper });

    await act(async () => {
      await result.current.testCredential(mockCredentialPayloadWithId, true);
    });

    expect(CredentialAPI.verifyWithId).toHaveBeenCalledWith('123', '1');
    expect(result.current.loading).toBe(false);

    notificationsSpy.mockRestore();
  });

  it('handles testCredential when data is not valid', async () => {
    const { CredentialAPI } = await import('@/services/api/credential');
    vi.mocked(CredentialAPI.verifyWithId).mockResolvedValueOnce({
      valid: false,
      message: 'Invalid credential',
    });

    const { result } = renderHook(() => useCredentialContext(), { wrapper });

    await expect(async () => {
      await act(async () => {
        await result.current.testCredential(mockCredentialPayloadWithId);
      });
    }).rejects.toThrow('Invalid credential');

    expect(CredentialAPI.verifyWithId).toHaveBeenCalledWith('123', '1');
    expect(result.current.loading).toBe(false);
  });

  it('handles testCredential API error without error handling', async () => {
    const { CredentialAPI } = await import('@/services/api/credential');
    vi.mocked(CredentialAPI.verifyWithId).mockRejectedValueOnce(new Error('API error'));

    const { result } = renderHook(() => useCredentialContext(), { wrapper });

    await expect(async () => {
      await act(async () => {
        await result.current.testCredential(mockCredentialPayloadWithId);
      });
    }).rejects.toThrow('API error');

    expect(CredentialAPI.verifyWithId).toHaveBeenCalledWith('123', '1');
    expect(result.current.loading).toBe(false);
  });

  it('reconnects a credential successfully', async () => {
    const { CredentialAPI } = await import('@/services/api/credential');
    vi.mocked(CredentialAPI.reconnect).mockResolvedValueOnce(undefined);

    const { result } = renderHook(() => useCredentialContext(), { wrapper });

    await act(async () => {
      await result.current.reconnectCredential('1');
    });

    expect(CredentialAPI.reconnect).toHaveBeenCalledWith('123', '1');
    expect(result.current.loading).toBe(false);
    expect(result.current.selectedCredential).toBeUndefined();
  });

  it('gets a credential successfully', async () => {
    const { CredentialAPI } = await import('@/services/api/credential');
    vi.mocked(CredentialAPI.get).mockResolvedValueOnce(mockCredential);

    const { result } = renderHook(() => useCredentialContext(), { wrapper });

    await act(async () => {
      await result.current.fetchCredential('1');
    });

    expect(CredentialAPI.get).toHaveBeenCalledWith('123', '1');
    expect(result.current.loading).toBe(false);
    expect(result.current.selectedCredential).toEqual(mockCredential);
  });

  it('handles fetchCredential error', async () => {
    const { CredentialAPI } = await import('@/services/api/credential');
    vi.mocked(CredentialAPI.get).mockRejectedValueOnce(new Error('Failed to fetch credential'));

    const { result } = renderHook(() => useCredentialContext(), { wrapper });

    await act(async () => {
      await result.current.fetchCredential('1');
    });

    expect(CredentialAPI.get).toHaveBeenCalledWith('123', '1');
    expect(result.current.loading).toBe(false);
    expect(result.current.selectedCredential).toBeUndefined();
  });

  it('handles reconnectCredential error', async () => {
    const { CredentialAPI } = await import('@/services/api/credential');
    vi.mocked(CredentialAPI.reconnect).mockRejectedValueOnce(
      new Error('Failed to reconnect credential')
    );
    const notificationsSpy = vi.spyOn(notifications, 'show').mockImplementation(() => '');

    const { result } = renderHook(() => useCredentialContext(), { wrapper });
    await act(async () => {
      await result.current.reconnectCredential('1', true);
    });

    expect(CredentialAPI.reconnect).toHaveBeenCalledWith('123', '1');
    expect(result.current.loading).toBe(false);
    expect(result.current.selectedCredential).toBeUndefined();
    expect(notificationsSpy).toHaveBeenCalled();

    notificationsSpy.mockRestore();
  });

  it('handles reconnectCredential error without error handling', async () => {
    const { CredentialAPI } = await import('@/services/api/credential');
    vi.mocked(CredentialAPI.reconnect).mockRejectedValueOnce(
      new Error('Failed to reconnect credential')
    );

    const { result } = renderHook(() => useCredentialContext(), { wrapper });

    await expect(async () => {
      await act(async () => {
        await result.current.reconnectCredential('1');
      });
    }).rejects.toThrow('Failed to reconnect credential');

    expect(CredentialAPI.reconnect).toHaveBeenCalledWith('123', '1');
    expect(result.current.loading).toBe(false);
  });

  it('handles error with response data structure', async () => {
    const { CredentialAPI } = await import('@/services/api/credential');
    const errorWithResponse = {
      response: {
        data: {
          error: {
            errorCode: 'TEST_ERROR',
            details: 'Test error details',
            message: 'Test error message',
          },
        },
      },
      message: 'Network error',
    };
    vi.mocked(CredentialAPI.create).mockRejectedValueOnce(errorWithResponse);

    const { result } = renderHook(() => useCredentialContext(), { wrapper });

    await expect(async () => {
      await act(async () => {
        await result.current.createCredential(mockCredentialPayload);
      });
    }).rejects.toThrow('Test error details');

    expect(result.current.loading).toBe(false);
  });

  it('handles error with only message', async () => {
    const { CredentialAPI } = await import('@/services/api/credential');
    const errorWithMessage = {
      message: 'Simple error message',
    };
    vi.mocked(CredentialAPI.create).mockRejectedValueOnce(errorWithMessage);

    const { result } = renderHook(() => useCredentialContext(), { wrapper });

    await expect(async () => {
      await act(async () => {
        await result.current.createCredential(mockCredentialPayload);
      });
    }).rejects.toThrow('Simple error message');

    expect(result.current.loading).toBe(false);
  });

  it('handles error with default message', async () => {
    const { CredentialAPI } = await import('@/services/api/credential');
    const errorWithoutMessage = {};
    vi.mocked(CredentialAPI.create).mockRejectedValueOnce(errorWithoutMessage);

    const { result } = renderHook(() => useCredentialContext(), { wrapper });

    await expect(async () => {
      await act(async () => {
        await result.current.createCredential(mockCredentialPayload);
      });
    }).rejects.toThrow(''); // Expect empty string, not 'undefined'

    expect(result.current.loading).toBe(false);
  });

  it('handles error with defaultMessage option', async () => {
    const { CredentialAPI } = await import('@/services/api/credential');
    const errorWithoutMessage = {};
    vi.mocked(CredentialAPI.create).mockRejectedValueOnce(errorWithoutMessage);

    const { result } = renderHook(() => useCredentialContext(), { wrapper });

    // Test the getErrorDetails function directly by creating a scenario where it would use defaultMessage
    // This tests the getErrorDetails function's defaultMessage fallback
    await expect(async () => {
      await act(async () => {
        await result.current.createCredential(mockCredentialPayload);
      });
    }).rejects.toThrow(''); // This tests the getErrorDetails function without defaultMessage

    expect(result.current.loading).toBe(false);
  });

  it('handles error with all message properties undefined', async () => {
    const { CredentialAPI } = await import('@/services/api/credential');
    const errorWithUndefinedMessages = {
      response: {
        data: {
          error: {
            errorCode: 'TEST_ERROR',
            details: undefined,
            message: undefined,
          },
        },
      },
      message: undefined,
    };
    vi.mocked(CredentialAPI.create).mockRejectedValueOnce(errorWithUndefinedMessages);

    const { result } = renderHook(() => useCredentialContext(), { wrapper });

    await expect(async () => {
      await act(async () => {
        await result.current.createCredential(mockCredentialPayload);
      });
    }).rejects.toThrow(''); // Expect empty string when all message properties are undefined

    expect(result.current.loading).toBe(false);
  });

  describe('handleAuth0 functionality', () => {
    it('opens popup window successfully when getAuth0 returns redirectUrl', async () => {
      const { CredentialAPI } = await import('@/services/api/credential');
      const mockAuth0Response = { redirectUrl: 'https://auth0.example.com/authorize' };
      vi.mocked(CredentialAPI.getAuth0).mockResolvedValueOnce(mockAuth0Response);
      mockWindowOpen.mockReturnValue({ closed: false });

      const { result } = renderHook(() => useCredentialContext(), { wrapper });

      await act(async () => {
        // We need to access the handleAuth0 function through createCredential with OAuth2
        const oauth2Payload: ICredentialPayload = {
          ...mockCredentialPayload,
          authenticationScheme: CredentialType.OAUTH2,
        };
        vi.mocked(CredentialAPI.create).mockResolvedValueOnce({ ...mockCredential, id: '1' });
        await result.current.createCredential(oauth2Payload);
      });

      expect(CredentialAPI.getAuth0).toHaveBeenCalledWith('123', '1');
      expect(mockWindowOpen).toHaveBeenCalledWith(
        'https://auth0.example.com/authorize',
        'auth0-popup',
        'width=500,height=600,scrollbars=yes,resizable=yes,status=yes,location=yes,toolbar=no,menubar=no'
      );
    });

    it('throws error when popup is blocked', async () => {
      const { CredentialAPI } = await import('@/services/api/credential');
      const mockAuth0Response = { redirectUrl: 'https://auth0.example.com/authorize' };
      vi.mocked(CredentialAPI.getAuth0).mockResolvedValueOnce(mockAuth0Response);
      mockWindowOpen.mockReturnValue(null); // Simulate popup blocked

      const { result } = renderHook(() => useCredentialContext(), { wrapper });

      await expect(async () => {
        await act(async () => {
          const oauth2Payload: ICredentialPayload = {
            ...mockCredentialPayload,
            authenticationScheme: CredentialType.OAUTH2,
          };
          vi.mocked(CredentialAPI.create).mockResolvedValueOnce({ ...mockCredential, id: '1' });
          await result.current.createCredential(oauth2Payload);
        });
      }).rejects.toThrow('Popup blocked');
    });

    it('handles getAuth0 API error', async () => {
      const { CredentialAPI } = await import('@/services/api/credential');
      vi.mocked(CredentialAPI.getAuth0).mockRejectedValueOnce(new Error('Auth0 API error'));

      const { result } = renderHook(() => useCredentialContext(), { wrapper });

      await expect(async () => {
        await act(async () => {
          const oauth2Payload: ICredentialPayload = {
            ...mockCredentialPayload,
            authenticationScheme: CredentialType.OAUTH2,
          };
          vi.mocked(CredentialAPI.create).mockResolvedValueOnce({ ...mockCredential, id: '1' });
          await result.current.createCredential(oauth2Payload);
        });
      }).rejects.toThrow('Auth0 API error');
    });

    it('handles getAuth0 when redirectUrl is not provided', async () => {
      const { CredentialAPI } = await import('@/services/api/credential');
      vi.mocked(CredentialAPI.getAuth0).mockResolvedValueOnce({ redirectUrl: '' }); // No redirectUrl

      const { result } = renderHook(() => useCredentialContext(), { wrapper });

      await act(async () => {
        const oauth2Payload: ICredentialPayload = {
          ...mockCredentialPayload,
          authenticationScheme: CredentialType.OAUTH2,
        };
        vi.mocked(CredentialAPI.create).mockResolvedValueOnce({ ...mockCredential, id: '1' });
        await result.current.createCredential(oauth2Payload);
      });

      expect(CredentialAPI.getAuth0).toHaveBeenCalledWith('123', '1');
      expect(mockWindowOpen).not.toHaveBeenCalled();
    });

    it('handles handleAuth0 when workspaceId is undefined', async () => {
      const { CredentialAPI } = await import('@/services/api/credential');
      mockWorkspaceId = undefined;
      const { result } = renderHook(() => useCredentialContext(), { wrapper });

      // This should not throw and should not call any API
      await act(async () => {
        const oauth2Payload: ICredentialPayload = {
          ...mockCredentialPayload,
          authenticationScheme: CredentialType.OAUTH2,
        };
        await result.current.createCredential(oauth2Payload);
      });

      expect(CredentialAPI.getAuth0).not.toHaveBeenCalled();
    });

    it('handles handleAuth0 when id is empty', async () => {
      const { CredentialAPI } = await import('@/services/api/credential');

      // Set up fresh mocks for this test - clear any existing mocks first
      vi.clearAllMocks();
      const mockCreatedCredential = { ...mockCredential, id: '' };
      vi.mocked(CredentialAPI.create).mockResolvedValueOnce(mockCreatedCredential);
      vi.mocked(CredentialAPI.getAuth0).mockResolvedValueOnce({ redirectUrl: 'test' });

      const { result } = renderHook(() => useCredentialContext(), { wrapper });

      // This should not throw and should not call any API
      await act(async () => {
        const oauth2Payload: ICredentialPayload = {
          ...mockCredentialPayload,
          authenticationScheme: CredentialType.OAUTH2,
        };
        await result.current.createCredential(oauth2Payload);
      });

      // The function should not be called because empty string is falsy
      expect(CredentialAPI.getAuth0).not.toHaveBeenCalled();
    });
  });

  describe('createCredential with OAuth2', () => {
    it('calls handleAuth0 when authenticationScheme is OAuth2', async () => {
      const { CredentialAPI } = await import('@/services/api/credential');

      const oauth2Payload: ICredentialPayload = {
        ...mockCredentialPayload,
        authenticationScheme: CredentialType.OAUTH2,
      };
      const mockCreatedCredential = { ...mockCredential, id: '1' };
      const mockAuth0Response = { redirectUrl: 'https://auth0.example.com/authorize' };

      // Clear any existing mocks and set up fresh ones
      vi.clearAllMocks();
      vi.mocked(CredentialAPI.create).mockResolvedValueOnce(mockCreatedCredential);
      vi.mocked(CredentialAPI.getAuth0).mockResolvedValueOnce(mockAuth0Response);

      const { result } = renderHook(() => useCredentialContext(), { wrapper });

      await act(async () => {
        await result.current.createCredential(oauth2Payload);
      });

      expect(CredentialAPI.create).toHaveBeenCalledWith('123', oauth2Payload);
      expect(CredentialAPI.getAuth0).toHaveBeenCalledWith('123', '1');
      expect(mockWindowOpen).toHaveBeenCalled();
    });

    it('sets selectedCredential when createCredential succeeds but handleAuth0 fails', async () => {
      const { CredentialAPI } = await import('@/services/api/credential');

      const oauth2Payload: ICredentialPayload = {
        ...mockCredentialPayload,
        authenticationScheme: CredentialType.OAUTH2,
      };
      const mockCreatedCredential = { ...mockCredential, id: '1' };

      // Clear any existing mocks and set up fresh ones
      vi.clearAllMocks();
      vi.mocked(CredentialAPI.create).mockResolvedValueOnce(mockCreatedCredential);
      vi.mocked(CredentialAPI.getAuth0).mockRejectedValueOnce(new Error('Auth0 error'));

      const { result } = renderHook(() => useCredentialContext(), { wrapper });

      try {
        await act(async () => {
          await result.current.createCredential(oauth2Payload);
        });
      } catch (error) {
        // Expected to throw
      }

      // Wait for state to update
      await act(async () => {
        // Small delay to ensure state is updated
        await new Promise((resolve) => setTimeout(resolve, 0));
      });

      expect(result.current.selectedCredential).toEqual(mockCreatedCredential);
    });
  });

  describe('updateCredential with OAuth2', () => {
    it('calls handleAuth0 when authenticationScheme is OAuth2 and no accessToken', async () => {
      const { CredentialAPI } = await import('@/services/api/credential');

      const oauth2Payload: ICredentialPayload = {
        ...mockCredentialPayload,
        authenticationScheme: CredentialType.OAUTH2,
      };
      const mockUpdatedCredential = {
        ...mockCredential,
        id: '1',
        authenticationScheme: CredentialType.OAUTH2,
        settings: {}, // No accessToken
      };
      const mockAuth0Response = { redirectUrl: 'https://auth0.example.com/authorize' };

      // Clear any existing mocks and set up fresh ones
      vi.clearAllMocks();
      vi.mocked(CredentialAPI.update).mockResolvedValueOnce(mockUpdatedCredential);
      // Reset the getAuth0 mock specifically
      vi.mocked(CredentialAPI.getAuth0).mockReset();
      vi.mocked(CredentialAPI.getAuth0).mockResolvedValueOnce(mockAuth0Response);

      const { result } = renderHook(() => useCredentialContext(), { wrapper });

      try {
        await act(async () => {
          await result.current.updateCredential(oauth2Payload);
        });
      } catch (error) {
        // If there's an error, let's see what it is
        console.log('Error caught in test:', error);
        throw error;
      }

      expect(CredentialAPI.update).toHaveBeenCalledWith('123', oauth2Payload);
      expect(CredentialAPI.getAuth0).toHaveBeenCalledWith('123', '1');
      expect(mockWindowOpen).toHaveBeenCalled();
    });

    it('does not call handleAuth0 when accessToken exists', async () => {
      const { CredentialAPI } = await import('@/services/api/credential');
      const oauth2Payload: ICredentialPayload = {
        ...mockCredentialPayload,
        authenticationScheme: CredentialType.OAUTH2,
      };
      const mockUpdatedCredential = {
        ...mockCredential,
        id: '1',
        authenticationScheme: CredentialType.OAUTH2,
        settings: { accessToken: 'existing-token' },
      };

      vi.mocked(CredentialAPI.update).mockResolvedValueOnce(mockUpdatedCredential);

      const { result } = renderHook(() => useCredentialContext(), { wrapper });

      await act(async () => {
        await result.current.updateCredential(oauth2Payload);
      });

      expect(CredentialAPI.update).toHaveBeenCalledWith('123', oauth2Payload);
      expect(CredentialAPI.getAuth0).not.toHaveBeenCalled();
      expect(mockWindowOpen).not.toHaveBeenCalled();
    });
  });
});
