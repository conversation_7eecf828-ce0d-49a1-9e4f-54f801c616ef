import type React from 'react';
import { createContext, useContext, useState } from 'react';
import { useNavigate, useParams, useSearchParams } from 'react-router-dom';
import useSWR from 'swr';

import { DEFAULT_MODEL_OPTIONS } from '@/constants/agent';
import { DEFAULT_PER_PAGE } from '@/constants/common';
import { useHandleApiError } from '@/hooks/useHandleApiError';
import { useListPromptModel } from '@/hooks/useListPromptModel';
import { useRestrictLimitParam } from '@/hooks/useRestrictLimitParam';
import type {
  IPrompt,
  IPromptCreatePayload,
  IPromptTemplateCategory,
  ITemplate,
  ITemplateCreatePayload,
} from '@/models';
import type { IStudioSuccessListResponse } from '@/models/response';
import { PromptAPI } from '@/services/api';
import { TemplateAPI } from '@/services/api/template';
import type { ResourceType } from '@/types';
import { jsonExport } from '@/utils/jsonExport';
import { showErrorNotification, showSuccessNotification } from '@/utils/notification';
import { useTranslate } from '@tolgee/react';
import { useAppContext } from '../AppContext';

const usePrompt = () => {
  const { workspaceId } = useParams();
  const navigate = useNavigate();
  const [_, setSearchParams] = useSearchParams();
  const [limit, setLimit] = useState<number>(DEFAULT_PER_PAGE); // Ensure default is correctly set
  const [cursor, setCursor] = useState('');
  const [searchValue, setSearchValue] = useState('');
  const [selectedCategory, setSelectedCategory] = useState<IPromptTemplateCategory | null>();
  const [selectedTemplate, setSelectedTemplate] = useState<ITemplate | null>(null);
  const { openConfirmModal, closeConfirmModal, orgId } = useAppContext();
  const { handleApiError } = useHandleApiError();
  const { t } = useTranslate(['prompt', 'common']);

  const { data: models } = useListPromptModel();

  const { data: prompts, mutate } = useSWR<IStudioSuccessListResponse<IPrompt>>(
    workspaceId ? ['prompts', workspaceId, limit, cursor, searchValue] : null,
    () => PromptAPI.getList(workspaceId ?? '', limit, cursor, searchValue),
    {
      revalidateOnFocus: false,
    }
  );

  const createPrompt = async (prompt: Omit<IPromptCreatePayload, 'workspaceId'>) => {
    const model = models?.[0]?.name ?? DEFAULT_MODEL_OPTIONS.model;
    try {
      const newPrompt = await PromptAPI.create({
        ...prompt,
        orgId,
        workspaceId: workspaceId ?? '',
        settings: prompt?.settings || {
          model,
          modelOptions: {
            ...DEFAULT_MODEL_OPTIONS,
            model,
          },
        },
      });
      if (newPrompt.id) {
        navigate(`/studio/${workspaceId}/prompts/${newPrompt.id}`);
      }
    } catch (error) {
      handleApiError(error);
    }
  };

  const duplicatePrompt = async (prompt: IPrompt) => {
    try {
      await PromptAPI.create({
        ...prompt,
        workspaceId: workspaceId ?? '',
        name: `${prompt.name} (Copy)`,
      });
      await mutate();
      showSuccessNotification(t('duplicated'));
    } catch (error) {
      handleApiError(error);
      showErrorNotification(t('duplicateFailed'));
    }
  };

  const updatePrompt = async (prompt: IPrompt) => {
    try {
      await PromptAPI.update(prompt);
      mutate();
    } catch (error) {
      handleApiError(error);
    }
  };

  const deletePrompt = async (id: string) => {
    try {
      await PromptAPI.delete(id);
      mutate();
    } catch (error) {
      handleApiError(error);
    }
  };

  const exportPrompt = async (id: string) => {
    try {
      const data = await PromptAPI.export(workspaceId ?? '', id);
      jsonExport(data, `prompt-${data?.[0]?.name || ''}.json`);
    } catch (error) {
      handleApiError(error);
    }
  };

  const importPrompt = async (file: File) => {
    try {
      await PromptAPI.import(file);
      await mutate();
      showSuccessNotification(t('importedSuccessfully'));
    } catch (error) {
      handleApiError(error);
    }
  };

  const getSearchPrompts = (value: string) => {
    setSearchValue(value);
  };

  const createTemplate = async (template: ITemplateCreatePayload) => {
    try {
      await TemplateAPI.create(workspaceId ?? '', template);
      mutate();
    } catch (error) {
      handleApiError(error);
    }
  };

  const handleDeletePrompt = async (prompt: IPrompt, callback?: () => void) => {
    openConfirmModal({
      name: prompt.name,
      onConfirm: async () => {
        await deletePrompt(prompt.id);
        closeConfirmModal();
        callback?.();
      },
      onCancel: () => {
        closeConfirmModal();
      },
    });
  };

  const handleSaveAsTemplate = async (prompt: IPrompt) => {
    const template = {
      name: prompt?.name ?? '',
      description: prompt?.description ?? '',
      type: 'prompt',
      resource: 'prompt' as ResourceType,
      settings: prompt?.settings,
    };

    try {
      await TemplateAPI.create(workspaceId ?? '', template);
      showSuccessNotification(t('template.saved', { ns: 'common' }));
    } catch (error) {
      showErrorNotification(t('template.saveFailed', { ns: 'common' }));
      handleApiError(error);
    }
  };

  useRestrictLimitParam(limit, setLimit, setSearchParams, () => setCursor(''));

  return {
    prompts,
    promptModels: models ?? [],
    createPrompt,
    duplicatePrompt,
    updatePrompt,
    deletePrompt,
    exportPrompt,
    importPrompt,
    getSearchPrompts,
    limit,
    setLimit,
    cursor,
    setCursor,
    searchValue,
    setSearchValue,
    selectedCategory,
    setSelectedCategory,
    selectedTemplate,
    setSelectedTemplate,
    createTemplate,
    handleDeletePrompt,
    handleSaveAsTemplate,
  };
};

export type PromptContextType = ReturnType<typeof usePrompt>;

const context = createContext<PromptContextType | null>(null);

export const PromptContextProvider = ({
  children,
  value,
}: {
  children: React.ReactNode;
  value?: PromptContextType;
}) => {
  const defaultValue = usePrompt();
  const contextValue = value || defaultValue;

  return <context.Provider value={contextValue}>{children}</context.Provider>;
};

export const usePromptContext = () => {
  const value = useContext(context);

  if (!value) {
    throw new Error('usePromptContext must be used inside PromptContextProvider');
  }

  return value;
};
