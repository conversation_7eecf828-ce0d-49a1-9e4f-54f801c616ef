import { DEFAULT_PER_PAGE } from '@/constants/common';
import type {
  IPrompt,
  IPromptCreatePayload,
  IPromptTemplateCategory,
  ITemplate,
  ITemplateCreatePayload,
} from '@/models';
import { PromptAPI } from '@/services/api';
import { TemplateAPI } from '@/services/api/template';
import { MantineWrapper } from '@/utils/test';
import { act, renderHook } from '@testing-library/react';
import type { ReactNode } from 'react';
import { beforeEach, describe, expect, it, vi } from 'vitest';
import { PromptContextProvider, usePromptContext } from './index';

const mockNavigate = vi.fn();
const mockOpenConfirmModal = vi.fn();
const mockCloseConfirmModal = vi.fn();

vi.mock('react-router-dom', () => ({
  useParams: () => ({
    workspaceId: 'test-workspace',
  }),
  useNavigate: () => mockNavigate,
  useSearchParams: () => [new URLSearchParams(), vi.fn()],
}));

vi.mock('swr', () => ({
  default: vi.fn().mockReturnValue({
    data: undefined,
    mutate: vi.fn(),
  }),
}));

vi.mock('@/services/api', () => ({
  PromptAPI: {
    getList: vi.fn(),
    create: vi.fn(),
    update: vi.fn(),
    delete: vi.fn(),
    export: vi.fn(),
    import: vi.fn(),
  },
}));

vi.mock('@/services/api/template', () => ({
  TemplateAPI: {
    create: vi.fn(),
  },
}));

vi.mock('@/hooks/useListPromptModel', () => ({
  useListPromptModel: vi.fn().mockReturnValue({
    data: [
      {
        name: 'gpt-4.1',
        provider: 'openai',
        maxTokens: 128000,
        supportsStreaming: true,
        costPer1KTokens: 5,
        description: 'Test model',
        modelId: 'gpt-4.1',
      },
    ],
  }),
}));

vi.mock('@/contexts/AppContext', () => ({
  useAppContext: () => ({
    openConfirmModal: mockOpenConfirmModal,
    closeConfirmModal: mockCloseConfirmModal,
    confirmModal: { opened: false },
  }),
  AppContextProvider: ({ children }) => <>{children}</>,
}));

vi.mock('@/components/Modals/ConfirmModal', () => ({
  default: ({ children }) => <div>{children}</div>,
}));

vi.mock('@/hooks/useHandleApiError', () => ({
  useHandleApiError: () => ({ handleApiError: vi.fn() }),
}));

vi.mock('@/utils/jsonExport', () => ({
  jsonExport: vi.fn(),
}));

vi.mock('@/utils/notification', () => ({
  showErrorNotification: vi.fn(),
  showSuccessNotification: vi.fn(),
}));

describe('PromptContext', () => {
  const wrapper = ({ children }: { children: ReactNode }) => (
    <MantineWrapper>
      <PromptContextProvider>{children}</PromptContextProvider>
    </MantineWrapper>
  );

  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('provides prompt context with default values', () => {
    const { result } = renderHook(() => usePromptContext(), { wrapper });

    expect(result.current.prompts).toBeUndefined();
    expect(result.current.limit).toBe(DEFAULT_PER_PAGE);
    expect(result.current.cursor).toBe('');
    expect(result.current.searchValue).toBe('');
    expect(result.current.selectedCategory).toBeUndefined();
    expect(result.current.selectedTemplate).toBeNull();
    expect(result.current.promptModels).toEqual([
      {
        name: 'gpt-4.1',
        provider: 'openai',
        maxTokens: 128000,
        supportsStreaming: true,
        costPer1KTokens: 5,
        description: 'Test model',
        modelId: 'gpt-4.1',
      },
    ]);
  });

  it('throws error when using context outside provider', () => {
    expect(() => {
      renderHook(() => usePromptContext());
    }).toThrow('usePromptContext must be used inside PromptContextProvider');
  });

  it('handles prompt creation', async () => {
    const mockPrompt: Omit<IPromptCreatePayload, 'workspaceId'> = {
      name: 'Test Prompt',
      description: 'Test Description',
    };

    const mockCreatedPrompt: IPrompt = {
      id: 'test-prompt-id',
      name: 'Test Prompt',
      description: 'Test Description',
      workspaceId: 'test-workspace',
      settings: {
        model: 'gpt-4.1',
        messages: [{ role: 'user', content: '' }],
        modelOptions: {
          frequencyPenalty: 0,
          maxTokens: 256,
          model: 'gpt-4.1',
          presencePenalty: 0,
          temperature: 1,
          topP: 1,
        },
      },
    };

    vi.mocked(PromptAPI.create).mockResolvedValue(mockCreatedPrompt);

    const { result } = renderHook(() => usePromptContext(), { wrapper });

    await act(async () => {
      await result.current.createPrompt(mockPrompt);
    });

    expect(PromptAPI.create).toHaveBeenCalledWith({
      ...mockPrompt,
      orgId: undefined,
      workspaceId: 'test-workspace',
      settings: {
        model: 'gpt-4.1',
        modelOptions: {
          frequencyPenalty: 0,
          maxTokens: 256,
          model: 'gpt-4.1',
          presencePenalty: 0,
          temperature: 1,
          topP: 1,
        },
      },
    });
  });

  it('handles prompt duplication', async () => {
    const mockPrompt: IPrompt = {
      id: 'test-prompt-id',
      name: 'Test Prompt',
      description: 'Test Description',
      workspaceId: 'test-workspace',
      settings: {
        model: 'gpt-4.1',
        messages: [{ role: 'user', content: '' }],
        modelOptions: {
          frequencyPenalty: 0,
          maxTokens: 256,
          model: 'gpt-4.1',
          presencePenalty: 0,
          temperature: 1,
          topP: 1,
        },
      },
    };

    const mockDuplicatedPrompt: IPrompt = {
      ...mockPrompt,
      id: 'new-test-prompt-id',
      name: 'Test Prompt (Copy)',
    };

    vi.mocked(PromptAPI.create).mockResolvedValue(mockDuplicatedPrompt);

    const { result } = renderHook(() => usePromptContext(), { wrapper });

    await act(async () => {
      await result.current.duplicatePrompt(mockPrompt);
    });

    expect(PromptAPI.create).toHaveBeenCalledWith({
      ...mockPrompt,
      name: 'Test Prompt (Copy)',
    });
  });

  it('handles prompt update', async () => {
    const mockPrompt: IPrompt = {
      id: 'test-prompt-id',
      name: 'Test Prompt',
      description: 'Test Description',
      workspaceId: 'test-workspace',
      settings: {
        model: 'gpt-4.1',
        messages: [{ role: 'user', content: '' }],
        modelOptions: {
          frequencyPenalty: 0,
          maxTokens: 256,
          model: 'gpt-4.1',
          presencePenalty: 0,
          temperature: 1,
          topP: 1,
        },
      },
    };

    const mockUpdatedPrompt: IPrompt = {
      ...mockPrompt,
      updatedAt: new Date(),
    };

    vi.mocked(PromptAPI.update).mockResolvedValue(mockUpdatedPrompt);

    const { result } = renderHook(() => usePromptContext(), { wrapper });

    await act(async () => {
      await result.current.updatePrompt(mockPrompt);
    });

    expect(PromptAPI.update).toHaveBeenCalledWith(mockPrompt);
  });

  it('handles prompt deletion', async () => {
    const promptId = 'test-prompt-id';
    const mockDeletedPrompt: IPrompt = {
      id: promptId,
      name: 'Test Prompt',
      description: 'Test Description',
      workspaceId: 'test-workspace',
      settings: {
        model: 'gpt-4.1',
        messages: [{ role: 'user', content: '' }],
        modelOptions: {
          frequencyPenalty: 0,
          maxTokens: 256,
          model: 'gpt-4.1',
          presencePenalty: 0,
          temperature: 1,
          topP: 1,
        },
      },
    };

    vi.mocked(PromptAPI.delete).mockResolvedValue(mockDeletedPrompt);

    const { result } = renderHook(() => usePromptContext(), { wrapper });

    await act(async () => {
      await result.current.deletePrompt(promptId);
    });

    expect(PromptAPI.delete).toHaveBeenCalledWith(promptId);
  });

  it('handles template creation', async () => {
    const mockTemplate: ITemplateCreatePayload = {
      name: 'Test Template',
      description: 'Test Description',
      type: 'prompt',
      resource: 'prompt',
      settings: {
        model: 'gpt-4.1',
        messages: [{ role: 'user', content: '' }],
        modelOptions: {
          frequencyPenalty: 0,
          maxTokens: 256,
          model: 'gpt-4.1',
          presencePenalty: 0,
          temperature: 1,
          topP: 1,
        },
      },
    };

    const mockCreatedTemplate: ITemplate = {
      id: 'test-template-id',
      name: 'Test Template',
      description: 'Test Description',
      workspaceId: 'test-workspace',
      type: 'prompt',
      resource: 'prompt',
      settings: {
        model: 'gpt-4.1',
        messages: [{ role: 'user', content: '' }],
        modelOptions: {
          frequencyPenalty: 0,
          maxTokens: 256,
          model: 'gpt-4.1',
          presencePenalty: 0,
          temperature: 1,
          topP: 1,
        },
      },
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    };

    vi.mocked(TemplateAPI.create).mockResolvedValue(mockCreatedTemplate);

    const { result } = renderHook(() => usePromptContext(), { wrapper });

    await act(async () => {
      await result.current.createTemplate(mockTemplate);
    });

    expect(TemplateAPI.create).toHaveBeenCalledWith('test-workspace', mockTemplate);
  });

  it('handles search value update', () => {
    const { result } = renderHook(() => usePromptContext(), { wrapper });

    act(() => {
      result.current.getSearchPrompts('test search');
    });

    expect(result.current.searchValue).toBe('test search');
  });

  it('handles limit update', () => {
    const { result } = renderHook(() => usePromptContext(), { wrapper });

    act(() => {
      result.current.setLimit(24);
    });

    expect(result.current.limit).toBe(24);
  });

  it('handles cursor update', () => {
    const { result } = renderHook(() => usePromptContext(), { wrapper });

    act(() => {
      result.current.setCursor('next-cursor');
    });

    expect(result.current.cursor).toBe('next-cursor');
  });

  it('handles save as template', async () => {
    const mockPrompt: IPrompt = {
      id: 'test-prompt-id',
      name: 'Test Prompt',
      description: 'Test Description',
      workspaceId: 'test-workspace',
      settings: {
        model: 'gpt-4.1',
        messages: [{ role: 'user', content: '' }],
        modelOptions: {
          frequencyPenalty: 0,
          maxTokens: 256,
          model: 'gpt-4.1',
          presencePenalty: 0,
          temperature: 1,
          topP: 1,
        },
      },
    };

    const mockCreatedTemplate: ITemplate = {
      id: 'test-template-id',
      name: mockPrompt.name,
      description: mockPrompt.description,
      workspaceId: 'test-workspace',
      type: 'prompt',
      resource: 'prompt',
      settings: mockPrompt.settings,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    };

    vi.mocked(TemplateAPI.create).mockResolvedValue(mockCreatedTemplate);

    const { result } = renderHook(() => usePromptContext(), { wrapper });

    await act(async () => {
      await result.current.handleSaveAsTemplate(mockPrompt);
    });

    expect(TemplateAPI.create).toHaveBeenCalledWith('test-workspace', {
      name: mockPrompt.name,
      description: mockPrompt.description,
      type: 'prompt',
      resource: 'prompt',
      settings: mockPrompt.settings,
    });
  });

  it('handles error during prompt creation', async () => {
    const mockPrompt: Omit<IPromptCreatePayload, 'workspaceId'> = {
      name: 'Test Prompt',
      description: 'Test Description',
    };

    const mockError = new Error('Failed to create prompt');
    vi.mocked(PromptAPI.create).mockRejectedValue(mockError);

    const { result } = renderHook(() => usePromptContext(), { wrapper });

    await act(async () => {
      await result.current.createPrompt(mockPrompt);
    });

    expect(PromptAPI.create).toHaveBeenCalledWith({
      ...mockPrompt,
      orgId: undefined,
      workspaceId: 'test-workspace',
      settings: {
        model: 'gpt-4.1',
        modelOptions: {
          frequencyPenalty: 0,
          maxTokens: 256,
          model: 'gpt-4.1',
          presencePenalty: 0,
          temperature: 1,
          topP: 1,
        },
      },
    });
  });

  it('handles category selection', () => {
    const mockCategory: IPromptTemplateCategory = {
      id: 'test-category',
      name: 'Test Category',
    };

    const { result } = renderHook(() => usePromptContext(), { wrapper });

    act(() => {
      result.current.setSelectedCategory(mockCategory);
    });

    expect(result.current.selectedCategory).toEqual(mockCategory);
  });

  it('handles template selection', () => {
    const mockTemplate: ITemplate = {
      id: 'test-template',
      name: 'Test Template',
      description: 'Test Description',
      workspaceId: 'test-workspace',
      type: 'prompt',
      resource: 'prompt',
      settings: {
        model: 'gpt-4.1',
        messages: [{ role: 'user', content: '' }],
        modelOptions: {
          frequencyPenalty: 0,
          maxTokens: 256,
          model: 'gpt-4.1',
          presencePenalty: 0,
          temperature: 1,
          topP: 1,
        },
      },
    };

    const { result } = renderHook(() => usePromptContext(), { wrapper });

    act(() => {
      result.current.setSelectedTemplate(mockTemplate);
    });

    expect(result.current.selectedTemplate).toEqual(mockTemplate);
  });

  it('handles export prompt', async () => {
    const promptId = 'test-prompt-id';
    const mockExportData: IPrompt[] = [
      {
        id: promptId,
        name: 'Test Prompt',
        description: 'Test Description',
        workspaceId: 'test-workspace',
        settings: {
          model: 'gpt-4.1',
          messages: [{ role: 'user', content: '' }],
          modelOptions: {
            frequencyPenalty: 0,
            maxTokens: 256,
            model: 'gpt-4.1',
            presencePenalty: 0,
            temperature: 1,
            topP: 1,
          },
        },
      },
    ];

    const { jsonExport } = await import('@/utils/jsonExport');
    vi.mocked(PromptAPI.export).mockResolvedValue(mockExportData);

    const { result } = renderHook(() => usePromptContext(), { wrapper });

    await act(async () => {
      await result.current.exportPrompt(promptId);
    });

    expect(PromptAPI.export).toHaveBeenCalledWith('test-workspace', promptId);
    expect(jsonExport).toHaveBeenCalledWith(
      mockExportData,
      `prompt-${mockExportData[0].name}.json`
    );
  });

  it('handles error during prompt update', async () => {
    const mockPrompt: IPrompt = {
      id: 'test-prompt-id',
      name: 'Test Prompt',
      description: 'Test Description',
      workspaceId: 'test-workspace',
      settings: {
        model: 'gpt-4.1',
        messages: [{ role: 'user', content: '' }],
        modelOptions: {
          frequencyPenalty: 0,
          maxTokens: 256,
          model: 'gpt-4.1',
          presencePenalty: 0,
          temperature: 1,
          topP: 1,
        },
      },
    };

    const mockError = new Error('Failed to update prompt');
    vi.mocked(PromptAPI.update).mockRejectedValue(mockError);

    const { result } = renderHook(() => usePromptContext(), { wrapper });

    await act(async () => {
      await result.current.updatePrompt(mockPrompt);
    });

    expect(PromptAPI.update).toHaveBeenCalledWith(mockPrompt);
  });

  it('handles error during prompt deletion', async () => {
    const promptId = 'test-prompt-id';
    const mockError = new Error('Failed to delete prompt');
    vi.mocked(PromptAPI.delete).mockRejectedValue(mockError);

    const { result } = renderHook(() => usePromptContext(), { wrapper });

    await act(async () => {
      await result.current.deletePrompt(promptId);
    });

    expect(PromptAPI.delete).toHaveBeenCalledWith(promptId);
  });

  it('handles error during template creation', async () => {
    const mockTemplate: ITemplateCreatePayload = {
      name: 'Test Template',
      description: 'Test Description',
      type: 'prompt',
      resource: 'prompt',
      settings: {
        model: 'gpt-4.1',
        messages: [{ role: 'user', content: '' }],
        modelOptions: {
          frequencyPenalty: 0,
          maxTokens: 256,
          model: 'gpt-4.1',
          presencePenalty: 0,
          temperature: 1,
          topP: 1,
        },
      },
    };

    const mockError = new Error('Failed to create template');
    vi.mocked(TemplateAPI.create).mockRejectedValue(mockError);

    const { result } = renderHook(() => usePromptContext(), { wrapper });

    await act(async () => {
      await result.current.createTemplate(mockTemplate);
    });

    expect(TemplateAPI.create).toHaveBeenCalledWith('test-workspace', mockTemplate);
  });

  it('handles search value reset', () => {
    const { result } = renderHook(() => usePromptContext(), { wrapper });

    act(() => {
      result.current.getSearchPrompts('test search');
    });
    expect(result.current.searchValue).toBe('test search');

    act(() => {
      result.current.getSearchPrompts('');
    });
    expect(result.current.searchValue).toBe('');
  });

  it('handles category reset', () => {
    const mockCategory: IPromptTemplateCategory = {
      id: 'test-category',
      name: 'Test Category',
    };

    const { result } = renderHook(() => usePromptContext(), { wrapper });

    act(() => {
      result.current.setSelectedCategory(mockCategory);
    });
    expect(result.current.selectedCategory).toEqual(mockCategory);

    act(() => {
      result.current.setSelectedCategory(null);
    });
    expect(result.current.selectedCategory).toBeNull();
  });

  it('handles template reset', () => {
    const mockTemplate: ITemplate = {
      id: 'test-template',
      name: 'Test Template',
      description: 'Test Description',
      workspaceId: 'test-workspace',
      type: 'prompt',
      resource: 'prompt',
      settings: {
        model: 'gpt-4.1',
        messages: [{ role: 'user', content: '' }],
        modelOptions: {
          frequencyPenalty: 0,
          maxTokens: 256,
          model: 'gpt-4.1',
          presencePenalty: 0,
          temperature: 1,
          topP: 1,
        },
      },
    };

    const { result } = renderHook(() => usePromptContext(), { wrapper });

    act(() => {
      result.current.setSelectedTemplate(mockTemplate);
    });
    expect(result.current.selectedTemplate).toEqual(mockTemplate);

    act(() => {
      result.current.setSelectedTemplate(null);
    });
    expect(result.current.selectedTemplate).toBeNull();
  });

  it('handles duplicatePrompt with isNavigate=true', async () => {
    const mockPrompt: IPrompt = {
      id: 'test-prompt-id',
      name: 'Test Prompt',
      description: 'Test Description',
      workspaceId: 'test-workspace',
      settings: {
        model: 'gpt-4.1',
        messages: [{ role: 'user', content: '' }],
        modelOptions: {
          frequencyPenalty: 0,
          maxTokens: 256,
          model: 'gpt-4.1',
          presencePenalty: 0,
          temperature: 1,
          topP: 1,
        },
      },
    };
    const mockDuplicatedPrompt: IPrompt = {
      ...mockPrompt,
      id: 'new-test-prompt-id',
      name: 'Test Prompt (Copy)',
    };
    vi.mocked(PromptAPI.create).mockResolvedValue(mockDuplicatedPrompt);
    const { result } = renderHook(() => usePromptContext(), { wrapper });
    await act(async () => {
      await result.current.duplicatePrompt(mockPrompt);
    });
    expect(PromptAPI.create).toHaveBeenCalledWith({
      ...mockPrompt,
      workspaceId: 'test-workspace',
      name: 'Test Prompt (Copy)',
    });
  });

  it('createPrompt uses default model if models is empty', async () => {
    const mockPrompt: Omit<IPromptCreatePayload, 'workspaceId'> = {
      name: 'Test Prompt',
      description: 'Test Description',
    };
    const mockCreatedPrompt: IPrompt = {
      id: 'test-prompt-id',
      name: 'Test Prompt',
      description: 'Test Description',
      workspaceId: 'test-workspace',
      settings: {
        model: 'gpt-4.1', // default
        messages: [{ role: 'user', content: '' }],
        modelOptions: {
          frequencyPenalty: 0,
          maxTokens: 256,
          model: 'gpt-4.1',
          presencePenalty: 0,
          temperature: 1,
          topP: 1,
        },
      },
    };
    vi.mocked(PromptAPI.create).mockResolvedValue(mockCreatedPrompt);
    const { result } = renderHook(() => usePromptContext(), { wrapper });
    await act(async () => {
      await result.current.createPrompt(mockPrompt);
    });
    expect(PromptAPI.create).toHaveBeenCalledWith(
      expect.objectContaining({
        settings: expect.objectContaining({ model: 'gpt-4.1' }),
      })
    );
  });

  it('handleSaveAsTemplate calls createTemplate with correct payload', async () => {
    const mockPrompt: IPrompt = {
      id: 'test-prompt-id',
      name: 'Test Prompt',
      description: 'Test Description',
      workspaceId: 'test-workspace',
      settings: {
        model: 'gpt-4.1',
        messages: [{ role: 'user', content: '' }],
        modelOptions: {
          frequencyPenalty: 0,
          maxTokens: 256,
          model: 'gpt-4.1',
          presencePenalty: 0,
          temperature: 1,
          topP: 1,
        },
      },
    };

    vi.mocked(TemplateAPI.create).mockResolvedValue({
      id: 'test-template-id',
      name: mockPrompt.name,
      description: mockPrompt.description,
      workspaceId: 'test-workspace',
      type: 'prompt',
      resource: 'prompt',
      settings: mockPrompt.settings,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    });

    const { result } = renderHook(() => usePromptContext(), { wrapper });

    await act(async () => {
      await result.current.handleSaveAsTemplate(mockPrompt);
    });

    expect(TemplateAPI.create).toHaveBeenCalledWith('test-workspace', {
      name: mockPrompt.name,
      description: mockPrompt.description,
      type: 'prompt',
      resource: 'prompt',
      settings: mockPrompt.settings,
    });
  });

  it('setSelectedCategory handles undefined and null', () => {
    const { result } = renderHook(() => usePromptContext(), { wrapper });
    act(() => {
      result.current.setSelectedCategory(undefined);
    });
    expect(result.current.selectedCategory).toBeUndefined();
    act(() => {
      result.current.setSelectedCategory(null);
    });
    expect(result.current.selectedCategory).toBeNull();
  });

  it('setSelectedTemplate handles null', () => {
    const { result } = renderHook(() => usePromptContext(), { wrapper });
    act(() => {
      result.current.setSelectedTemplate(null);
    });
    expect(result.current.selectedTemplate).toBeNull();
  });

  it('getSearchPrompts handles undefined', () => {
    const { result } = renderHook(() => usePromptContext(), { wrapper });
    act(() => {
      result.current.getSearchPrompts(undefined as any);
    });
    expect(result.current.searchValue).toBeUndefined();
  });

  it('createPrompt with existing settings uses provided settings', async () => {
    const mockPrompt: Omit<IPromptCreatePayload, 'workspaceId'> = {
      name: 'Test Prompt',
      description: 'Test Description',
      settings: {
        model: 'custom-model',
        messages: [{ role: 'assistant', content: 'Hello' }],
        modelOptions: {
          temperature: 0.5,
          maxTokens: 512,
          model: 'custom-model',
          presencePenalty: 0.1,
          frequencyPenalty: 0.2,
          topP: 0.9,
        },
      },
    };

    const mockCreatedPrompt: IPrompt = {
      id: 'test-prompt-id',
      name: 'Test Prompt',
      description: 'Test Description',
      workspaceId: 'test-workspace',
      settings: mockPrompt.settings,
    };

    vi.mocked(PromptAPI.create).mockResolvedValue(mockCreatedPrompt);

    const { result } = renderHook(() => usePromptContext(), { wrapper });

    await act(async () => {
      await result.current.createPrompt(mockPrompt);
    });

    expect(PromptAPI.create).toHaveBeenCalledWith({
      ...mockPrompt,
      workspaceId: 'test-workspace',
      settings: mockPrompt.settings,
    });
  });

  it('handleDeletePrompt with callback calls callback only when user confirms', async () => {
    const mockPrompt: IPrompt = {
      id: 'test-prompt-id',
      name: 'Test Prompt',
      description: 'Test Description',
      workspaceId: 'test-workspace',
      settings: {
        model: 'gpt-4.1',
        messages: [{ role: 'user', content: '' }],
        modelOptions: {
          frequencyPenalty: 0,
          maxTokens: 256,
          model: 'gpt-4.1',
          presencePenalty: 0,
          temperature: 1,
          topP: 1,
        },
      },
    };

    const callback = vi.fn();
    vi.mocked(PromptAPI.delete).mockResolvedValue(mockPrompt);

    const { result } = renderHook(() => usePromptContext(), { wrapper });

    act(() => {
      result.current.handleDeletePrompt(mockPrompt, callback);
    });

    // Get the onConfirm function that was passed to openConfirmModal
    const onConfirmCall = mockOpenConfirmModal.mock.calls[0][0];
    const onConfirm = onConfirmCall.onConfirm;

    // Call the onConfirm function to simulate user confirmation
    await act(async () => {
      await onConfirm();
    });

    // Verify that callback was called after successful deletion
    expect(callback).toHaveBeenCalled();
    expect(PromptAPI.delete).toHaveBeenCalledWith(mockPrompt.id);
    expect(mockCloseConfirmModal).toHaveBeenCalled();
  });

  it('handleDeletePrompt calls onCancel when user cancels deletion', () => {
    const mockPrompt: IPrompt = {
      id: 'test-prompt-id',
      name: 'Test Prompt',
      description: 'Test Description',
      workspaceId: 'test-workspace',
      settings: {
        model: 'gpt-4.1',
        messages: [{ role: 'user', content: '' }],
        modelOptions: {
          frequencyPenalty: 0,
          maxTokens: 256,
          model: 'gpt-4.1',
          presencePenalty: 0,
          temperature: 1,
          topP: 1,
        },
      },
    };

    const { result } = renderHook(() => usePromptContext(), { wrapper });

    act(() => {
      result.current.handleDeletePrompt(mockPrompt);
    });

    // Verify that openConfirmModal was called with correct parameters
    expect(mockOpenConfirmModal).toHaveBeenCalledWith({
      name: mockPrompt.name,
      onConfirm: expect.any(Function),
      onCancel: expect.any(Function),
    });

    // Get the onCancel function that was passed to openConfirmModal
    const onCancelCall = mockOpenConfirmModal.mock.calls[0][0];
    const onCancel = onCancelCall.onCancel;

    // Call the onCancel function to simulate user cancellation
    act(() => {
      onCancel();
    });

    // Verify that closeConfirmModal was called but deletePrompt was NOT called
    expect(mockCloseConfirmModal).toHaveBeenCalled();
    expect(PromptAPI.delete).not.toHaveBeenCalled();
  });

  it('handleDeletePrompt with callback does not call callback when user cancels', () => {
    const mockPrompt: IPrompt = {
      id: 'test-prompt-id',
      name: 'Test Prompt',
      description: 'Test Description',
      workspaceId: 'test-workspace',
      settings: {
        model: 'gpt-4.1',
        messages: [{ role: 'user', content: '' }],
        modelOptions: {
          frequencyPenalty: 0,
          maxTokens: 256,
          model: 'gpt-4.1',
          presencePenalty: 0,
          temperature: 1,
          topP: 1,
        },
      },
    };

    const callback = vi.fn();

    const { result } = renderHook(() => usePromptContext(), { wrapper });

    act(() => {
      result.current.handleDeletePrompt(mockPrompt, callback);
    });

    // Get the onCancel function that was passed to openConfirmModal
    const onCancelCall = mockOpenConfirmModal.mock.calls[0][0];
    const onCancel = onCancelCall.onCancel;

    // Call the onCancel function to simulate user cancellation
    act(() => {
      onCancel();
    });

    // Verify that callback was NOT called when user cancels
    expect(callback).not.toHaveBeenCalled();
    expect(PromptAPI.delete).not.toHaveBeenCalled();
    expect(mockCloseConfirmModal).toHaveBeenCalled();
  });

  it('calls closeConfirmModal when handleDeletePrompt onCancel is triggered', () => {
    const mockPrompt: IPrompt = {
      id: 'test-prompt-id',
      name: 'Test Prompt',
      description: 'Test Description',
      workspaceId: 'test-workspace',
      settings: {
        model: 'gpt-4.1',
        messages: [{ role: 'user', content: '' }],
        modelOptions: {
          frequencyPenalty: 0,
          maxTokens: 256,
          model: 'gpt-4.1',
          presencePenalty: 0,
          temperature: 1,
          topP: 1,
        },
      },
    };

    const { result } = renderHook(() => usePromptContext(), { wrapper });

    act(() => {
      result.current.handleDeletePrompt(mockPrompt);
    });

    // The mockOpenConfirmModal automatically calls onConfirm, so we need to test onCancel separately
    // We can verify that openConfirmModal was called with the correct structure
    expect(mockOpenConfirmModal).toHaveBeenCalledWith({
      name: mockPrompt.name,
      onConfirm: expect.any(Function),
      onCancel: expect.any(Function),
    });

    // Get the onCancel function that was passed to openConfirmModal
    const onCancelCall = mockOpenConfirmModal.mock.calls[0][0];
    const onCancel = onCancelCall.onCancel;

    // Call the onCancel function to simulate user cancellation
    act(() => {
      onCancel();
    });

    // Verify that closeConfirmModal was called
    expect(mockCloseConfirmModal).toHaveBeenCalled();
  });

  it('handleDeletePrompt calls onConfirm when user confirms deletion', async () => {
    const mockPrompt: IPrompt = {
      id: 'test-prompt-id',
      name: 'Test Prompt',
      description: 'Test Description',
      workspaceId: 'test-workspace',
      settings: {
        model: 'gpt-4.1',
        messages: [{ role: 'user', content: '' }],
        modelOptions: {
          frequencyPenalty: 0,
          maxTokens: 256,
          model: 'gpt-4.1',
          presencePenalty: 0,
          temperature: 1,
          topP: 1,
        },
      },
    };

    vi.mocked(PromptAPI.delete).mockResolvedValue(mockPrompt);

    const { result } = renderHook(() => usePromptContext(), { wrapper });

    act(() => {
      result.current.handleDeletePrompt(mockPrompt);
    });

    // Verify that openConfirmModal was called with correct parameters
    expect(mockOpenConfirmModal).toHaveBeenCalledWith({
      name: mockPrompt.name,
      onConfirm: expect.any(Function),
      onCancel: expect.any(Function),
    });

    // Get the onConfirm function that was passed to openConfirmModal
    const onConfirmCall = mockOpenConfirmModal.mock.calls[0][0];
    const onConfirm = onConfirmCall.onConfirm;

    // Call the onConfirm function to simulate user confirmation
    await act(async () => {
      await onConfirm();
    });

    // Verify that deletePrompt was called and closeConfirmModal was called
    expect(PromptAPI.delete).toHaveBeenCalledWith(mockPrompt.id);
    expect(mockCloseConfirmModal).toHaveBeenCalled();
  });

  it('handleDeletePrompt calls onCancel when user cancels deletion', () => {
    const mockPrompt: IPrompt = {
      id: 'test-prompt-id',
      name: 'Test Prompt',
      description: 'Test Description',
      workspaceId: 'test-workspace',
      settings: {
        model: 'gpt-4.1',
        messages: [{ role: 'user', content: '' }],
        modelOptions: {
          frequencyPenalty: 0,
          maxTokens: 256,
          model: 'gpt-4.1',
          presencePenalty: 0,
          temperature: 1,
          topP: 1,
        },
      },
    };

    const { result } = renderHook(() => usePromptContext(), { wrapper });

    act(() => {
      result.current.handleDeletePrompt(mockPrompt);
    });

    // Verify that openConfirmModal was called with correct parameters
    expect(mockOpenConfirmModal).toHaveBeenCalledWith({
      name: mockPrompt.name,
      onConfirm: expect.any(Function),
      onCancel: expect.any(Function),
    });

    // Get the onCancel function that was passed to openConfirmModal
    const onCancelCall = mockOpenConfirmModal.mock.calls[0][0];
    const onCancel = onCancelCall.onCancel;

    // Call the onCancel function to simulate user cancellation
    act(() => {
      onCancel();
    });

    // Verify that closeConfirmModal was called but deletePrompt was NOT called
    expect(mockCloseConfirmModal).toHaveBeenCalled();
    expect(PromptAPI.delete).not.toHaveBeenCalled();
  });

  it('handleDeletePrompt with callback calls callback only when user confirms', async () => {
    const mockPrompt: IPrompt = {
      id: 'test-prompt-id',
      name: 'Test Prompt',
      description: 'Test Description',
      workspaceId: 'test-workspace',
      settings: {
        model: 'gpt-4.1',
        messages: [{ role: 'user', content: '' }],
        modelOptions: {
          frequencyPenalty: 0,
          maxTokens: 256,
          model: 'gpt-4.1',
          presencePenalty: 0,
          temperature: 1,
          topP: 1,
        },
      },
    };

    const callback = vi.fn();
    vi.mocked(PromptAPI.delete).mockResolvedValue(mockPrompt);

    const { result } = renderHook(() => usePromptContext(), { wrapper });

    act(() => {
      result.current.handleDeletePrompt(mockPrompt, callback);
    });

    // Get the onConfirm function that was passed to openConfirmModal
    const onConfirmCall = mockOpenConfirmModal.mock.calls[0][0];
    const onConfirm = onConfirmCall.onConfirm;

    // Call the onConfirm function to simulate user confirmation
    await act(async () => {
      await onConfirm();
    });

    // Verify that callback was called after successful deletion
    expect(callback).toHaveBeenCalled();
    expect(PromptAPI.delete).toHaveBeenCalledWith(mockPrompt.id);
    expect(mockCloseConfirmModal).toHaveBeenCalled();
  });

  it('handleDeletePrompt with callback does not call callback when user cancels', () => {
    const mockPrompt: IPrompt = {
      id: 'test-prompt-id',
      name: 'Test Prompt',
      description: 'Test Description',
      workspaceId: 'test-workspace',
      settings: {
        model: 'gpt-4.1',
        messages: [{ role: 'user', content: '' }],
        modelOptions: {
          frequencyPenalty: 0,
          maxTokens: 256,
          model: 'gpt-4.1',
          presencePenalty: 0,
          temperature: 1,
          topP: 1,
        },
      },
    };

    const callback = vi.fn();

    const { result } = renderHook(() => usePromptContext(), { wrapper });

    act(() => {
      result.current.handleDeletePrompt(mockPrompt, callback);
    });

    // Get the onCancel function that was passed to openConfirmModal
    const onCancelCall = mockOpenConfirmModal.mock.calls[0][0];
    const onCancel = onCancelCall.onCancel;

    // Call the onCancel function to simulate user cancellation
    act(() => {
      onCancel();
    });

    // Verify that callback was NOT called when user cancels
    expect(callback).not.toHaveBeenCalled();
    expect(PromptAPI.delete).not.toHaveBeenCalled();
    expect(mockCloseConfirmModal).toHaveBeenCalled();
  });

  it('handles workspaceId being undefined', () => {
    const { result } = renderHook(() => usePromptContext(), { wrapper });

    expect(result.current.prompts).toBeUndefined();
    expect(result.current.promptModels).toEqual([
      {
        name: 'gpt-4.1',
        provider: 'openai',
        maxTokens: 128000,
        supportsStreaming: true,
        costPer1KTokens: 5,
        description: 'Test model',
        modelId: 'gpt-4.1',
      },
    ]);
  });

  it('calls handleApiError when createPrompt throws', async () => {
    const handleApiError = vi.fn();
    const mod = await import('@/hooks/useHandleApiError');
    vi.spyOn(mod, 'useHandleApiError').mockReturnValue({ handleApiError });
    const error = new Error('API error');
    vi.mocked(PromptAPI.create).mockRejectedValue(error);
    const mockPrompt: Omit<IPromptCreatePayload, 'workspaceId'> = {
      name: 'Test Prompt',
      description: 'Test Description',
    };
    const { result } = renderHook(() => usePromptContext(), { wrapper });
    await act(async () => {
      await result.current.createPrompt(mockPrompt);
    });
    expect(handleApiError).toHaveBeenCalledWith(error);
  });

  it('handleDeletePrompt calls handleApiError when delete operation fails', async () => {
    const handleApiError = vi.fn();
    const mod = await import('@/hooks/useHandleApiError');
    vi.spyOn(mod, 'useHandleApiError').mockReturnValue({ handleApiError });

    const mockPrompt: IPrompt = {
      id: 'test-prompt-id',
      name: 'Test Prompt',
      description: 'Test Description',
      workspaceId: 'test-workspace',
      settings: {
        model: 'gpt-4.1',
        messages: [{ role: 'user', content: '' }],
        modelOptions: {
          frequencyPenalty: 0,
          maxTokens: 256,
          model: 'gpt-4.1',
          presencePenalty: 0,
          temperature: 1,
          topP: 1,
        },
      },
    };

    const error = new Error('Failed to delete prompt');
    vi.mocked(PromptAPI.delete).mockRejectedValue(error);

    const { result } = renderHook(() => usePromptContext(), { wrapper });

    act(() => {
      result.current.handleDeletePrompt(mockPrompt);
    });

    // Get the onConfirm function that was passed to openConfirmModal
    const onConfirmCall = mockOpenConfirmModal.mock.calls[0][0];
    const onConfirm = onConfirmCall.onConfirm;

    // Call the onConfirm function to simulate user confirmation
    await act(async () => {
      await onConfirm();
    });

    // Verify that handleApiError was called with the error
    expect(handleApiError).toHaveBeenCalledWith(error);
    expect(PromptAPI.delete).toHaveBeenCalledWith(mockPrompt.id);
    expect(mockCloseConfirmModal).toHaveBeenCalled();
  });

  it('handles import prompt success', async () => {
    const mockFile = new File(['test content'], 'test.json', { type: 'application/json' });
    const mockImportedPrompt: IPrompt = {
      id: 'imported-prompt-id',
      name: 'Imported Prompt',
      description: 'Imported Description',
      workspaceId: 'test-workspace',
      settings: {
        model: 'gpt-4.1',
        messages: [{ role: 'user', content: '' }],
        modelOptions: {
          frequencyPenalty: 0,
          maxTokens: 256,
          model: 'gpt-4.1',
          presencePenalty: 0,
          temperature: 1,
          topP: 1,
        },
      },
    };

    const { showSuccessNotification } = await import('@/utils/notification');
    vi.mocked(PromptAPI.import).mockResolvedValue(mockImportedPrompt);

    const { result } = renderHook(() => usePromptContext(), { wrapper });

    await act(async () => {
      await result.current.importPrompt(mockFile);
    });

    expect(PromptAPI.import).toHaveBeenCalledWith(mockFile);
    expect(showSuccessNotification).toHaveBeenCalledWith(expect.any(String));
  });

  it('handles import prompt error', async () => {
    const mockFile = new File(['test content'], 'test.json', { type: 'application/json' });
    const error = new Error('Failed to import prompt');
    const handleApiError = vi.fn();
    const mod = await import('@/hooks/useHandleApiError');
    vi.spyOn(mod, 'useHandleApiError').mockReturnValue({ handleApiError });

    vi.mocked(PromptAPI.import).mockRejectedValue(error);

    const { result } = renderHook(() => usePromptContext(), { wrapper });

    await act(async () => {
      await result.current.importPrompt(mockFile);
    });

    expect(PromptAPI.import).toHaveBeenCalledWith(mockFile);
    expect(handleApiError).toHaveBeenCalledWith(error);
  });

  it('handles export prompt error', async () => {
    const promptId = 'test-prompt-id';
    const error = new Error('Failed to export prompt');
    const handleApiError = vi.fn();
    const mod = await import('@/hooks/useHandleApiError');
    vi.spyOn(mod, 'useHandleApiError').mockReturnValue({ handleApiError });

    vi.mocked(PromptAPI.export).mockRejectedValue(error);

    const { result } = renderHook(() => usePromptContext(), { wrapper });

    await act(async () => {
      await result.current.exportPrompt(promptId);
    });

    expect(PromptAPI.export).toHaveBeenCalledWith('test-workspace', promptId);
    expect(handleApiError).toHaveBeenCalledWith(error);
  });
});
