import type { MessagePayload, ObjectColumn, Record, RecordData, RecordsCount, View, WSObject } from '@/models';
import type { ISuccessResponse } from '@resola-ai/models';
import { axiosService, logger } from '@resola-ai/services-shared';
import { beforeEach, describe, expect, it, vi } from 'vitest';
import { RecordAPI } from './record';

// Mock dependencies
vi.mock('@resola-ai/services-shared', () => ({
  axiosService: {
    instance: {
      get: vi.fn(),
      post: vi.fn(),
      put: vi.fn(),
      delete: vi.fn(),
    },
  },
  logger: {
    error: vi.fn(),
  },
}));

describe('RecordAPI', () => {
  const mockWSId = 'workspace-123';
  const mockObjId = 'object-456';
  const mockViewId = 'view-789';
  const mockRecordId = 'record-101';

  const mockRecord: Record = {
    id: mockRecordId,
    fields: {
      name: '<PERSON>',
      email: '<EMAIL>',
    },
  } as Record;

  const mockWSObject: WSObject = {
    id: mockObjId,
    name: {
      singular: 'Contact',
      plural: 'Contacts',
    },
  } as WSObject;

  const mockRecordData: RecordData = {
    records: [mockRecord],
    views: { id: mockViewId, name: 'Contact', workspaceId: mockWSId, type: 'list', icon: 'contact', fields: [] } as View,
    object: { ...mockWSObject, type: 'text', header: 'Contact', name: 'Contact' } as ObjectColumn,
    totalRecordsCount: 1,
  };

  const mockKanbanResponse = {
    type: 'kanban' as const,
    kanbanRecords: {
      'stage-1': [mockRecord],
      'stage-2': [],
    },
    view: { id: mockViewId },
    object: mockWSObject,
    swimlaneFieldId: 'status',
    swimlaneOrder: ['stage-1', 'stage-2'],
  };

  const mockRecordsCount: RecordsCount = {
    datetime: '2021-01-01',
    values: [
      {
        '2021-01-01': 42,
      },
    ],
  };

  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe('get', () => {
    it('should fetch records successfully with minimal parameters', async () => {
      const mockResponse: ISuccessResponse<RecordData> = {
        data: mockRecordData,
        status: '200',
      };

      vi.mocked(axiosService.instance.get).mockResolvedValue({
        data: mockResponse,
      });

      const result = await RecordAPI.get(mockWSId, mockViewId);

      expect(axiosService.instance.get).toHaveBeenCalledWith(
        `data/${mockWSId}/${mockViewId}`,
        {
          params: {
            textSearch: '',
            offset: undefined,
            limit: undefined,
          },
        }
      );
      expect(result).toEqual(mockRecordData);
    });

    it('should fetch records successfully with all parameters', async () => {
      const mockResponse: ISuccessResponse<RecordData> = {
        data: mockRecordData,
        status: '200',
      };

      vi.mocked(axiosService.instance.get).mockResolvedValue({
        data: mockResponse,
      });

      const result = await RecordAPI.get(mockWSId, mockViewId, 'search term', 10, 20);

      expect(axiosService.instance.get).toHaveBeenCalledWith(
        `data/${mockWSId}/${mockViewId}`,
        {
          params: {
            textSearch: 'search term',
            offset: 10,
            limit: 20,
          },
        }
      );
      expect(result).toEqual(mockRecordData);
    });

    it('should handle empty textSearch parameter', async () => {
      const mockResponse: ISuccessResponse<RecordData> = {
        data: mockRecordData,
        status: '200',
      };

      vi.mocked(axiosService.instance.get).mockResolvedValue({
        data: mockResponse,
      });

      const result = await RecordAPI.get(mockWSId, mockViewId, '', 0, 50);

      expect(axiosService.instance.get).toHaveBeenCalledWith(
        `data/${mockWSId}/${mockViewId}`,
        {
          params: {
            textSearch: '',
            offset: 0,
            limit: 50,
          },
        }
      );
      expect(result).toEqual(mockRecordData);
    });

    it('should handle errors and return undefined', async () => {
      const mockError = new Error('Network error');
      vi.mocked(axiosService.instance.get).mockRejectedValue(mockError);

      const result = await RecordAPI.get(mockWSId, mockViewId);

      expect(result).toBeUndefined();
      expect(logger.error).toHaveBeenCalledWith(mockError);
    });
  });

  describe('getKanbanRecords', () => {
    it('should fetch kanban records successfully with minimal parameters', async () => {
      const mockResponse: ISuccessResponse<typeof mockKanbanResponse> = {
        data: mockKanbanResponse,
        status: '200',
      };

      vi.mocked(axiosService.instance.get).mockResolvedValue({
        data: mockResponse,
      });

      const result = await RecordAPI.getKanbanRecords(mockWSId, mockViewId);

      expect(axiosService.instance.get).toHaveBeenCalledWith(
        `data/${mockWSId}/${mockViewId}`,
        {
          params: {
            type: 'kanban',
            textSearch: '',
            offset: undefined,
            limit: undefined,
          },
        }
      );
      expect(result).toEqual(mockKanbanResponse);
    });

    it('should fetch kanban records successfully with all parameters', async () => {
      const mockResponse: ISuccessResponse<typeof mockKanbanResponse> = {
        data: mockKanbanResponse,
        status: '200',
      };

      vi.mocked(axiosService.instance.get).mockResolvedValue({
        data: mockResponse,
      });

      const result = await RecordAPI.getKanbanRecords(mockWSId, mockViewId, 'kanban search', 5, 15);

      expect(axiosService.instance.get).toHaveBeenCalledWith(
        `data/${mockWSId}/${mockViewId}`,
        {
          params: {
            type: 'kanban',
            textSearch: 'kanban search',
            offset: 5,
            limit: 15,
          },
        }
      );
      expect(result).toEqual(mockKanbanResponse);
    });

    it('should handle errors and return undefined', async () => {
      const mockError = new Error('Kanban error');
      vi.mocked(axiosService.instance.get).mockRejectedValue(mockError);

      const result = await RecordAPI.getKanbanRecords(mockWSId, mockViewId);

      expect(result).toBeUndefined();
      expect(logger.error).toHaveBeenCalledWith(mockError);
    });
  });

  describe('getRecords', () => {
    it('should fetch records without filters', async () => {
      const mockResponse: ISuccessResponse<Record[]> = {
        data: [mockRecord],
        status: '200',
      };

      vi.mocked(axiosService.instance.get).mockResolvedValue({
        data: mockResponse,
      });

      const result = await RecordAPI.getRecords(mockWSId, mockObjId);

      expect(axiosService.instance.get).toHaveBeenCalledWith(
        `data/${mockWSId}/${mockObjId}/records`,
        undefined
      );
      expect(result).toEqual([mockRecord]);
    });

    it('should fetch records with filters', async () => {
      const filters = '{"status": "active"}';
      const mockResponse: ISuccessResponse<Record[]> = {
        data: [mockRecord],
        status: '200',
      };

      vi.mocked(axiosService.instance.get).mockResolvedValue({
        data: mockResponse,
      });

      const result = await RecordAPI.getRecords(mockWSId, mockObjId, filters);

      expect(axiosService.instance.get).toHaveBeenCalledWith(
        `data/${mockWSId}/${mockObjId}/records`,
        { params: { filters } }
      );
      expect(result).toEqual([mockRecord]);
    });

    it('should handle errors and return undefined', async () => {
      const mockError = new Error('Get records error');
      vi.mocked(axiosService.instance.get).mockRejectedValue(mockError);

      const result = await RecordAPI.getRecords(mockWSId, mockObjId);

      expect(result).toBeUndefined();
      expect(logger.error).toHaveBeenCalledWith(mockError);
    });
  });

  describe('getRecordById', () => {
    it('should fetch record by ID successfully', async () => {
      const mockResponse: ISuccessResponse<Record[]> = {
        data: [mockRecord],
        status: '200',
      };

      vi.mocked(axiosService.instance.get).mockResolvedValue({
        data: mockResponse,
      });

      const result = await RecordAPI.getRecordById(mockWSId, mockObjId, mockRecordId);

      expect(axiosService.instance.get).toHaveBeenCalledWith(
        `data/${mockWSId}/${mockObjId}/${mockRecordId}`
      );
      expect(result).toEqual(mockRecord);
    });

    it('should handle empty response array', async () => {
      const mockResponse: ISuccessResponse<Record[]> = {
        data: [],
        status: '200',
      };

      vi.mocked(axiosService.instance.get).mockResolvedValue({
        data: mockResponse,
      });

      const result = await RecordAPI.getRecordById(mockWSId, mockObjId, mockRecordId);

      expect(result).toBeUndefined();
    });

    it('should handle errors and re-throw them', async () => {
      const mockError = new Error('Record not found');
      vi.mocked(axiosService.instance.get).mockRejectedValue(mockError);

      await expect(RecordAPI.getRecordById(mockWSId, mockObjId, mockRecordId)).rejects.toThrow('Record not found');
      expect(logger.error).toHaveBeenCalledWith(mockError);
    });
  });

  describe('save', () => {
    it('should save record successfully', async () => {
      const payload = {
        fields: {
          name: 'Jane Doe',
          email: '<EMAIL>',
        },
      };

      const mockResponse: ISuccessResponse<Record> = {
        data: { ...mockRecord, ...payload },
        status: '200',
      };

      vi.mocked(axiosService.instance.post).mockResolvedValue({
        data: mockResponse,
      });

      const result = await RecordAPI.save(mockWSId, mockObjId, payload);

      expect(axiosService.instance.post).toHaveBeenCalledWith(
        `data/${mockWSId}/${mockObjId}`,
        payload,
        {
          params: {
            position: undefined,
          },
        }
      );
      expect(result).toEqual({ ...mockRecord, ...payload });
    });

    it('should handle errors and re-throw them', async () => {
      const payload = { fields: { name: 'Test' } };
      const mockError = new Error('Save failed');
      vi.mocked(axiosService.instance.post).mockRejectedValue(mockError);

      await expect(RecordAPI.save(mockWSId, mockObjId, payload)).rejects.toThrow('Save failed');
      expect(logger.error).toHaveBeenCalledWith(mockError);
    });
  });

  describe('update', () => {
    it('should update record successfully', async () => {
      const payload = {
        fields: {
          name: 'Updated Name',
          email: '<EMAIL>',
        },
      };

      const mockResponse: ISuccessResponse<WSObject> = {
        data: mockWSObject,
        status: '200',
      };

      vi.mocked(axiosService.instance.put).mockResolvedValue({
        data: mockResponse,
      });

      const result = await RecordAPI.update(mockWSId, mockObjId, mockRecordId, payload);

      expect(axiosService.instance.put).toHaveBeenCalledWith(
        `data/${mockWSId}/${mockObjId}/${mockRecordId}`,
        payload
      );
      expect(result).toEqual(mockWSObject);
    });

    it('should handle errors and re-throw them', async () => {
      const payload = { fields: { name: 'Test' } };
      const mockError = new Error('Update failed');
      vi.mocked(axiosService.instance.put).mockRejectedValue(mockError);

      await expect(RecordAPI.update(mockWSId, mockObjId, mockRecordId, payload)).rejects.toThrow('Update failed');
      expect(logger.error).toHaveBeenCalledWith(mockError);
    });
  });

  describe('mergeRecord', () => {
    it('should merge record successfully', async () => {
      const payload = {
        sourceRecordId: 'source-record-123',
        mergeData: {
          fields: {
            name: 'Merged Name',
          },
        },
      };

      const mockResponse: ISuccessResponse<WSObject> = {
        data: mockWSObject,
        status: '200',
      };

      vi.mocked(axiosService.instance.put).mockResolvedValue({
        data: mockResponse,
      });

      const result = await RecordAPI.mergeRecord(mockWSId, mockObjId, mockRecordId, payload);

      expect(axiosService.instance.put).toHaveBeenCalledWith(
        `data/${mockWSId}/${mockObjId}/${mockRecordId}/merge`,
        payload
      );
      expect(result).toEqual(mockWSObject);
    });

    it('should handle errors and re-throw them', async () => {
      const payload = { sourceRecordId: 'source' };
      const mockError = new Error('Merge failed');
      vi.mocked(axiosService.instance.put).mockRejectedValue(mockError);

      await expect(RecordAPI.mergeRecord(mockWSId, mockObjId, mockRecordId, payload)).rejects.toThrow('Merge failed');
      expect(logger.error).toHaveBeenCalledWith(mockError);
    });
  });

  describe('updateByField', () => {
    it('should update record field successfully', async () => {
      const fieldId = 'field-123';
      const payload = {
        value: 'New field value',
      };

      const mockResponse = {
        data: { data: { updated: true } },
      };

      vi.mocked(axiosService.instance.put).mockResolvedValue(mockResponse);

      const result = await RecordAPI.updateByField(mockWSId, mockObjId, mockRecordId, fieldId, payload);

      expect(axiosService.instance.put).toHaveBeenCalledWith(
        `data/${mockWSId}/${mockObjId}/records/${mockRecordId}/fields/${fieldId}`,
        payload
      );
      expect(result).toEqual({ updated: true });
    });

    it('should handle errors and re-throw them', async () => {
      const fieldId = 'field-123';
      const payload = { value: 'test' };
      const mockError = new Error('Field update failed');
      vi.mocked(axiosService.instance.put).mockRejectedValue(mockError);

      await expect(RecordAPI.updateByField(mockWSId, mockObjId, mockRecordId, fieldId, payload)).rejects.toThrow('Field update failed');
      expect(logger.error).toHaveBeenCalledWith(mockError);
    });
  });

  describe('delete', () => {
    it('should delete record successfully', async () => {
      const mockResponse = {
        data: { data: { deleted: true } },
      };

      vi.mocked(axiosService.instance.delete).mockResolvedValue(mockResponse);

      const result = await RecordAPI.delete(mockWSId, mockObjId, mockRecordId);

      expect(axiosService.instance.delete).toHaveBeenCalledWith(
        `data/${mockWSId}/${mockObjId}/${mockRecordId}`
      );
      expect(result).toEqual({ deleted: true });
    });

    it('should handle errors and re-throw them', async () => {
      const mockError = new Error('Delete failed');
      vi.mocked(axiosService.instance.delete).mockRejectedValue(mockError);

      await expect(RecordAPI.delete(mockWSId, mockObjId, mockRecordId)).rejects.toThrow('Delete failed');
      expect(logger.error).toHaveBeenCalledWith(mockError);
    });
  });

  describe('sendMessage', () => {
    it('should send message successfully', async () => {
      const payload: MessagePayload = {
        to: ['user1', 'user2'],
        channel: 'mail',
      };

      const mockResponse = {
        data: { data: { messageSent: true, messageId: 'msg-123' } },
      };

      vi.mocked(axiosService.instance.post).mockResolvedValue(mockResponse);

      const result = await RecordAPI.sendMessage(mockWSId, mockObjId, mockRecordId, payload);

      expect(axiosService.instance.post).toHaveBeenCalledWith(
        `data/${mockWSId}/${mockObjId}/${mockRecordId}/message`,
        payload
      );
      expect(result).toEqual({ messageSent: true, messageId: 'msg-123' });
    });

    it('should handle errors and re-throw them', async () => {
      const payload: MessagePayload = {
        to: ['<EMAIL>', '<EMAIL>'],
        channel: 'mail',
      };
      const mockError = new Error('Message send failed');
      vi.mocked(axiosService.instance.post).mockRejectedValue(mockError);

      await expect(RecordAPI.sendMessage(mockWSId, mockObjId, mockRecordId, payload)).rejects.toThrow('Message send failed');
      expect(logger.error).toHaveBeenCalledWith(mockError);
    });
  });

  describe('getLiveChatUrl', () => {
    it('should get live chat URL successfully', async () => {
      const payload = {
        enduserId: 'user-123',
        integrationId: 'integration-456',
      };

      const mockResponse = {
        data: { data: { url: 'https://chat.example.com/room-123' } },
      };

      vi.mocked(axiosService.instance.post).mockResolvedValue(mockResponse);

      const result = await RecordAPI.getLiveChatUrl(payload);

      expect(axiosService.instance.post).toHaveBeenCalledWith(
        'integrations/lc/getLink',
        payload
      );
      expect(result).toEqual({ url: 'https://chat.example.com/room-123' });
    });

    it('should handle errors and re-throw them', async () => {
      const payload = {
        enduserId: 'user-123',
        integrationId: 'integration-456',
      };
      const mockError = new Error('Chat URL generation failed');
      vi.mocked(axiosService.instance.post).mockRejectedValue(mockError);

      await expect(RecordAPI.getLiveChatUrl(payload)).rejects.toThrow('Chat URL generation failed');
      expect(logger.error).toHaveBeenCalledWith(mockError);
    });
  });

  describe('recordsCount', () => {
    it('should get records count successfully', async () => {
      const mockResponse: ISuccessResponse<RecordsCount> = {
        data: mockRecordsCount,
        status: '200',
      };

      vi.mocked(axiosService.instance.get).mockResolvedValue({
        data: mockResponse,
      });

      const result = await RecordAPI.recordsCount(mockWSId, mockObjId);

      expect(axiosService.instance.get).toHaveBeenCalledWith(
        `data/${mockWSId}/${mockObjId}/recordsCount`
      );
      expect(result).toEqual(mockRecordsCount);
    });

    it('should handle errors and re-throw them', async () => {
      const mockError = new Error('Count failed');
      vi.mocked(axiosService.instance.get).mockRejectedValue(mockError);

      await expect(RecordAPI.recordsCount(mockWSId, mockObjId)).rejects.toThrow('Count failed');
      expect(logger.error).toHaveBeenCalledWith(mockError);
    });
  });

  describe('error handling patterns', () => {
    it('should handle errors consistently across methods that return undefined', async () => {
      const mockError = new Error('Consistent error');
      vi.mocked(axiosService.instance.get).mockRejectedValue(mockError);

      // Methods that return undefined on error
      const result1 = await RecordAPI.get(mockWSId, mockViewId);
      const result2 = await RecordAPI.getKanbanRecords(mockWSId, mockViewId);
      const result3 = await RecordAPI.getRecords(mockWSId, mockObjId);

      expect(result1).toBeUndefined();
      expect(result2).toBeUndefined();
      expect(result3).toBeUndefined();
      expect(logger.error).toHaveBeenCalledTimes(3);
      expect(logger.error).toHaveBeenCalledWith(mockError);
    });

    it('should handle errors consistently across methods that re-throw', async () => {
      const mockError = new Error('Consistent error');
      vi.mocked(axiosService.instance.get).mockRejectedValue(mockError);
      vi.mocked(axiosService.instance.post).mockRejectedValue(mockError);
      vi.mocked(axiosService.instance.put).mockRejectedValue(mockError);
      vi.mocked(axiosService.instance.delete).mockRejectedValue(mockError);

      // Methods that re-throw errors
      await expect(RecordAPI.getRecordById(mockWSId, mockObjId, mockRecordId)).rejects.toThrow();
      await expect(RecordAPI.save(mockWSId, mockObjId, {})).rejects.toThrow();
      await expect(RecordAPI.update(mockWSId, mockObjId, mockRecordId, {})).rejects.toThrow();
      await expect(RecordAPI.mergeRecord(mockWSId, mockObjId, mockRecordId, {})).rejects.toThrow();
      await expect(RecordAPI.updateByField(mockWSId, mockObjId, mockRecordId, 'field', {})).rejects.toThrow();
      await expect(RecordAPI.delete(mockWSId, mockObjId, mockRecordId)).rejects.toThrow();
      await expect(RecordAPI.sendMessage(mockWSId, mockObjId, mockRecordId, {} as MessagePayload)).rejects.toThrow();
      await expect(RecordAPI.getLiveChatUrl({ enduserId: 'e', integrationId: 'i' })).rejects.toThrow();
      await expect(RecordAPI.recordsCount(mockWSId, mockObjId)).rejects.toThrow();

      expect(logger.error).toHaveBeenCalledTimes(9);
    });
  });

  describe('complex payload handling', () => {
    it('should handle complex save payload', async () => {
      const complexPayload = {
        fields: {
          name: 'Complex Record',
          email: '<EMAIL>',
          metadata: {
            source: 'import',
            tags: ['important', 'client'],
          },
        },
        relationships: {
          parentId: 'parent-123',
          children: ['child-1', 'child-2'],
        },
      };

      const mockResponse: ISuccessResponse<Record> = {
        data: mockRecord,
        status: '200',
      };

      vi.mocked(axiosService.instance.post).mockResolvedValue({
        data: mockResponse,
      });

      await RecordAPI.save(mockWSId, mockObjId, complexPayload);

      expect(axiosService.instance.post).toHaveBeenCalledWith(
        `data/${mockWSId}/${mockObjId}`,
        complexPayload,
        {
          params: {
            position: undefined,
          },
        }
      );
    });

    it('should handle complex message payload', async () => {
      const complexMessagePayload: MessagePayload = {
        to: ['<EMAIL>', '<EMAIL>'],
        channel: 'mail',
        attachments: [
          { path: 'https://example.com/file1.pdf', type: 'pdf', filename: 'file1.pdf', url: 'https://example.com/file1.pdf', disposition: 'attachment' },
          { path: 'https://example.com/file2.jpg', type: 'image', filename: 'file2.jpg', url: 'https://example.com/file2.jpg', disposition: 'attachment' },
        ],
      };

      const mockResponse = {
        data: { data: { messageSent: true } },
      };

      vi.mocked(axiosService.instance.post).mockResolvedValue(mockResponse);

      await RecordAPI.sendMessage(mockWSId, mockObjId, mockRecordId, complexMessagePayload);

      expect(axiosService.instance.post).toHaveBeenCalledWith(
        `data/${mockWSId}/${mockObjId}/${mockRecordId}/message`,
        complexMessagePayload
      );
    });
  });
});
