import type { MessagePayload, Record, RecordData, RecordsCount, WSObject } from '@/models';
import type { ISuccessResponse } from '@resola-ai/models';
import { axiosService, logger } from '@resola-ai/services-shared';
interface KanbanResponse {
  type: 'kanban';
  kanbanRecords: { [key: string]: Record[]; other: Record[] };
  view: any;
  object: WSObject;
  swimlaneFieldId: string;
  swimlaneOrder: { id: string; active: boolean }[];
}

export const RecordAPI = {
  get: async (
    wsId: string,
    viewId: string,
    textSearch?: string,
    offset?: number,
    limit?: number
  ) => {
    try {
      const response = await axiosService.instance.get<ISuccessResponse<RecordData>>(
        `data/${wsId}/${viewId}`,
        {
          params: {
            textSearch: textSearch || '',
            offset,
            limit,
          },
        }
      );
      return response.data.data;
    } catch (error) {
      logger.error(error);
      return undefined;
    }
  },
  getKanbanRecords: async (
    wsId: string,
    viewId: string,
    textSearch?: string,
    offset?: number,
    limit?: number
  ) => {
    try {
      const response = await axiosService.instance.get<ISuccessResponse<KanbanResponse>>(
        `data/${wsId}/${viewId}`,
        {
          params: {
            type: 'kanban',
            textSearch: textSearch || '',
            offset,
            limit,
          },
        }
      );
      return response.data.data;
    } catch (error) {
      logger.error(error);
      return undefined;
    }
  },
  getRecords: async (wsId: string, objId: string, filters?: string) => {
    try {
      const response = await axiosService.instance.get<ISuccessResponse<Record[]>>(
        `data/${wsId}/${objId}/records`,
        filters ? { params: { filters } } : undefined
      );
      return response.data.data;
    } catch (error) {
      logger.error(error);
      return undefined;
    }
  },
  getRecordById: async (wsId: string, objId: string, recordId: string) => {
    try {
      const response = await axiosService.instance.get<ISuccessResponse<Record>>(
        `data/${wsId}/${objId}/${recordId}`
      );
      return response.data.data?.[0];
    } catch (error) {
      logger.error(error);
      throw error;
    }
  },
  save: async (wsId: string, objId: string, payload, position?: number) => {
    try {
      const response = await axiosService.instance.post<ISuccessResponse<Record>>(
        `data/${wsId}/${objId}`,
        {
          ...payload,
        },
        {
          params: {
            position,
          },
        }
      );
      return response.data.data;
    } catch (error) {
      logger.error(error);
      throw error;
    }
  },
  update: async (wsId: string, objId: string, recordId: string, payload) => {
    try {
      const response = await axiosService.instance.put<ISuccessResponse<WSObject>>(
        `data/${wsId}/${objId}/${recordId}`,
        {
          ...payload,
        }
      );
      return response.data.data;
    } catch (error) {
      logger.error(error);
      throw error;
    }
  },
  mergeRecord: async (wsId: string, objId: string, recordId: string, payload) => {
    try {
      const response = await axiosService.instance.put<ISuccessResponse<WSObject>>(
        `data/${wsId}/${objId}/${recordId}/merge`,
        {
          ...payload,
        }
      );
      return response.data.data;
    } catch (error) {
      logger.error(error);
      throw error;
    }
  },
  updateByField: async (
    wsId: string,
    objId: string,
    recordId: string,
    fieldId: string,
    payload
  ) => {
    try {
      const response = await axiosService.instance.put(
        `data/${wsId}/${objId}/records/${recordId}/fields/${fieldId}`,
        {
          ...payload,
        }
      );
      return response.data.data;
    } catch (error) {
      logger.error(error);
      throw error;
    }
  },
  delete: async (wsId: string, objId: string, recordId: string) => {
    try {
      const response = await axiosService.instance.delete(`data/${wsId}/${objId}/${recordId}`);
      return response.data.data;
    } catch (error) {
      logger.error(error);
      throw error;
    }
  },
  sendMessage: async (wsId: string, objId: string, recordId: string, payload: MessagePayload) => {
    try {
      const response = await axiosService.instance.post(
        `data/${wsId}/${objId}/${recordId}/message`,
        payload
      );
      return response.data.data;
    } catch (error) {
      logger.error(error);
      throw error;
    }
  },
  getLiveChatUrl: async (payload: { enduserId: string; integrationId: string }) => {
    try {
      const response = await axiosService.instance.post('integrations/lc/getLink', payload);
      return response.data.data;
    } catch (error) {
      logger.error(error);
      throw error;
    }
  },
  recordsCount: async (wsId: string, objId: string) => {
    try {
      const response = await axiosService.instance.get<ISuccessResponse<RecordsCount>>(
        `data/${wsId}/${objId}/recordsCount`
      );
      return response.data.data;
    } catch (error) {
      logger.error(error);
      throw error;
    }
  },
  reorder: async (
    wsId: string,
    objId: string,
    recordId: string,
    payload: {
      previousRecordId: string;
      nextRecordId: string;
      swimlaneId: string;
      swimlaneOptId: string;
    }
  ) => {
    try {
      const response = await axiosService.instance.put(
        `data/${wsId}/${objId}/records/${recordId}/order`,
        payload
      );
      return response.data.data;
    } catch (error) {
      logger.error(error);
      throw error;
    }
  },
  duplicate: async (wsId: string, objId: string, recordId: string) => {
    try {
      const response = await axiosService.instance.post(
        `data/${wsId}/${objId}/${recordId}/duplicate`
      );
      return response.data.data;
    } catch (error) {
      logger.error(error);
      throw error;
    }
  },
};
