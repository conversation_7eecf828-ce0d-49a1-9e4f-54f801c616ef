/* eslint-disable no-unused-vars */
/**
 * Page Navigation Logic Types
 *
 * These types define the structure for conditional page navigation in multi-page forms.
 * The logic follows a prioritized fallback chain:
 * 1. Conditional redirects (based on form data)
 * 2. Normal redirects (unconditional)
 * 3. Sequential flow (index-based)
 */

export const __ENDING_PAGE_ID__ = '__ending_configuration__';

/**
 * Field-level condition operators
 */
/**
 * Supported MongoDB-like operators for page logic conditions.
 * Use string literal union for type safety and IDE autocompletion.
 */
export type PageConditionOperator =
  | '$ne'
  | '$in'
  | '$nin'
  | '$regex'
  | '$exists'
  | '$gt'
  | '$gte'
  | '$lt'
  | '$lte'
  | '$not';

/**
 * Field condition - maps field ID to operator and value, or direct value
 * Examples:
 * - { "01k6fgpecg...": { $in: ["option1", "option2"] } }
 * - { "01k6fgpecg...": "direct_value" }
 * - { "01k6fgpecg...": null }
 */
export type FieldCondition = {
  [fieldId: string]:
    | {
        [operator in PageConditionOperator]?: any;
      }
    | any; // Allow direct values (string, number, boolean, null)
};

/**
 * Condition group - supports nested AND/OR logic
 * Can contain field conditions or nested condition groups
 */
export interface ConditionGroup {
  $and?: (FieldCondition | ConditionGroup)[];
  $or?: (FieldCondition | ConditionGroup)[];
}

/**
 * Conditional rule for page redirection
 */
export interface ConditionalRule {
  id: string;
  action: 'redirect_to_page';
  targetPageId: string;
  conditions: ConditionGroup;
}

/**
 * Normal redirect configuration
 */
export interface NormalRedirect {
  targetPageId: string;
}

/**
 * Page logic configuration
 */
export interface PageLogic {
  conditionalRules?: ConditionalRule[];
  normalRedirect?: NormalRedirect;
}

/**
 * Extended FormSection with index for navigation
 * Note: FormSection is imported from form-builder.ts
 * Index may be derived from array position or included in data
 */
export interface PageWithIndex {
  id: string;
  index: number;
  logic?: PageLogic;
}
