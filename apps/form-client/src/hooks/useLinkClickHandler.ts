import { rem } from '@mantine/core';
import { createStyles } from '@mantine/emotion';
import { useCallback, useEffect, useRef, useState } from 'react';

export interface LinkClickHandlerConfig {
  fieldId: string;
  globalHandlerName: string;
  popoverClasses: {
    errorPopover: string;
    popoverArrow: string;
    popoverContent: string;
    popoverText: string;
  };
  activePageIndex?: number; // Add page index to detect page changes
}

export interface LinkInfo {
  url: string;
  text: string;
  id: string;
}

export const useLinkClickStyles = createStyles((theme) => ({
  errorPopover: {
    backgroundColor: `${theme.colors.decaRed[1]} !important`,
    border: `1px solid ${theme.colors.decaRed[7]} !important`,
    borderRadius: rem(6),
    padding: rem(8),
    boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)',
    position: 'relative',
  },
  popoverContent: {
    display: 'flex',
    alignItems: 'flex-start',
    gap: rem(8),
    maxWidth: rem(300),
  },
  popoverText: {
    flex: 1,
    overflowWrap: 'break-word',
    width: '90%',
  },
  popoverArrow: {
    borderColor: `${theme.colors.decaRed[7]} !important`,
  },
}));

export const useLinkClickHandler = (config: LinkClickHandlerConfig) => {
  const [clickedLinks, setClickedLinks] = useState<Set<string>>(new Set());
  const [showHint, setShowHint] = useState(false);
  const [showPopoverForLinks, setShowPopoverForLinks] = useState<Set<string>>(new Set());
  const [attemptedCheck, setAttemptedCheck] = useState(false);
  const attemptedCheckTimerRef = useRef<NodeJS.Timeout | null>(null);
  const autoHideTimerRef = useRef<NodeJS.Timeout | null>(null);
  const previousPageIndexRef = useRef<number | undefined>(config.activePageIndex);

  const extractLinks = useCallback(
    (text: string): LinkInfo[] => {
      const linkRegex = /<a[^>]+href=["']([^"']+)["'][^>]*>([^<]+)<\/a>/gi;
      const links: LinkInfo[] = [];
      let match: RegExpExecArray | null = linkRegex.exec(text);
      while (match !== null) {
        links.push({
          url: match[1],
          text: match[2],
          id: `${config.fieldId}_${match[1]}`,
        });
        match = linkRegex.exec(text);
      }
      return links;
    },
    [config.fieldId]
  );

  const parseAttributes = useCallback((attrs: string) => {
    const attributes: Record<string, string> = {};

    // Use a more robust approach to prevent ReDoS attacks
    // Split by whitespace first, then parse each attribute individually
    const attrParts = attrs.trim().split(/\s+/);

    for (const part of attrParts) {
      // Match attribute=value pattern with strict boundaries
      // Use non-capturing groups and atomic matching to prevent backtracking
      const match = part.match(/^(\w+)=(["'])([^"']*)\2$/);
      if (match) {
        const [, key, , value] = match;
        // Additional safety: limit key and value length
        if (key.length <= 50 && value.length <= 1000) {
          attributes[key] = value;
        }
      }
    }

    return attributes;
  }, []);

  const handleLinkClick = useCallback((linkId: string) => {
    setClickedLinks((prev) => {
      const newSet = new Set(prev);
      newSet.add(linkId);
      return newSet;
    });
    setShowHint(false);
    setShowPopoverForLinks(new Set());
    setAttemptedCheck(false);
  }, []);

  const processQuestionText = useCallback(
    (text: string) => {
      return text.replace(/<a([^>]+href=["']([^"']+)["'][^>]*)>/g, (match, attrs, url) => {
        const linkId = `${config.fieldId}_${url}`;
        const escapedLinkId = linkId.replace(/\\/g, '\\\\').replace(/'/g, "\\'");
        return `<a${attrs} target="_blank" data-link-id="${escapedLinkId}">`;
      });
    },
    [config.fieldId]
  );

  const handleAttemptedCheck = useCallback(
    (links: LinkInfo[]) => {
      setAttemptedCheck(true);
      setShowHint(true);
      const unclickedLinks = links.filter((link) => !clickedLinks.has(link.id));
      if (unclickedLinks.length > 0) {
        const unclickedLinkIds = new Set(unclickedLinks.map((link) => link.id));
        setShowPopoverForLinks(unclickedLinkIds);
      }

      // Clear any existing timer
      if (attemptedCheckTimerRef.current) {
        clearTimeout(attemptedCheckTimerRef.current);
      }
      // Set new timer
      attemptedCheckTimerRef.current = setTimeout(() => {
        setAttemptedCheck(false);
      }, 200);
    },
    [clickedLinks]
  );

  // Auto-hide popover after 5 seconds
  useEffect(() => {
    // Clear any existing auto-hide timer
    if (autoHideTimerRef.current) {
      clearTimeout(autoHideTimerRef.current);
    }

    if (showHint || showPopoverForLinks.size > 0) {
      autoHideTimerRef.current = setTimeout(() => {
        setShowHint(false);
        setShowPopoverForLinks(new Set());
      }, 50000); // Julie: testing
    }

    return () => {
      if (autoHideTimerRef.current) {
        clearTimeout(autoHideTimerRef.current);
      }
    };
  }, [showHint, showPopoverForLinks.size]);

  // Close all popovers when page changes
  useEffect(() => {
    if (
      config.activePageIndex !== undefined &&
      previousPageIndexRef.current !== undefined &&
      config.activePageIndex !== previousPageIndexRef.current
    ) {
      setShowHint(false);
      setShowPopoverForLinks(new Set());
      setAttemptedCheck(false);
    }
    previousPageIndexRef.current = config.activePageIndex;
  }, [config.activePageIndex]);

  // Set up global handler for link clicks
  useEffect(() => {
    // Create a unique handler name for this instance
    const uniqueHandlerName = `${config.globalHandlerName}_${config.fieldId}`;

    (window as any)[uniqueHandlerName] = (linkId: string) => {
      handleLinkClick(linkId);
    };

    return () => {
      (window as any)[uniqueHandlerName] = undefined;
    };
  }, [handleLinkClick, config.globalHandlerName, config.fieldId]);

  // Cleanup timers on unmount
  useEffect(() => {
    return () => {
      if (attemptedCheckTimerRef.current) {
        clearTimeout(attemptedCheckTimerRef.current);
      }
      if (autoHideTimerRef.current) {
        clearTimeout(autoHideTimerRef.current);
      }
    };
  }, []);

  const getUniqueHandlerName = useCallback(() => {
    return `${config.globalHandlerName}_${config.fieldId}`;
  }, [config.globalHandlerName, config.fieldId]);

  return {
    clickedLinks,
    showHint,
    showPopoverForLinks,
    attemptedCheck,
    extractLinks,
    handleLinkClick,
    processQuestionText,
    parseAttributes,
    handleAttemptedCheck,
    setAttemptedCheck,
    setShowPopoverForLinks,
    getUniqueHandlerName,
  };
};
