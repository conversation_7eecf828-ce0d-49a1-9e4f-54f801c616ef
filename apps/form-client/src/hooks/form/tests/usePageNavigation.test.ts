/**
 * Tests for usePageNavigation Hook
 */

import type { FormSection } from '@/types/form-builder';
import { FieldType } from '@/types/form-builder';
import { __ENDING_PAGE_ID__ } from '@/types/page-logic';
import { renderHook } from '@testing-library/react';
import { describe, expect, it } from 'vitest';
import { usePageNavigation } from '../../usePageNavigation';

describe('usePageNavigation', () => {
  // Helper to create mock page
  const createMockPage = (
    partial: Partial<FormSection> & { id: string; index: number }
  ): FormSection & { index: number } => ({
    type: FieldType.Section,
    name: `Page ${partial.index + 1}`,
    layout: { type: 'image-top-with-space' as any, imageUrl: null, fieldWidth: '100%' },
    content: [],
    ...partial,
  });

  const mockPages: (FormSection & { index: number })[] = [
    createMockPage({
      id: 'page1',
      index: 0,
      logics: {
        conditionalRules: [
          {
            id: 'rule1',
            action: 'redirect_to_page',
            targetPageId: 'page3',
            conditions: {
              $or: [
                {
                  field1: { $in: ['option3'] },
                },
              ],
            },
          },
        ],
      },
    }),
    createMockPage({
      id: 'page2',
      index: 1,
      logics: {
        conditionalRules: [],
        normalRedirect: {
          targetPageId: 'page4',
        },
      },
    }),
    createMockPage({
      id: 'page3',
      index: 2,
      logics: {
        conditionalRules: [],
        normalRedirect: {
          targetPageId: 'page5',
        },
      },
    }),
    createMockPage({
      id: 'page4',
      index: 3,
      logics: {
        conditionalRules: [
          {
            id: 'rule2',
            action: 'redirect_to_page',
            targetPageId: 'page3',
            conditions: {
              $and: [
                {
                  field2: { $in: ['checkboxOption2'] },
                },
              ],
            },
          },
        ],
      },
    }),
    createMockPage({
      id: 'page5',
      index: 4,
      name: 'Page 5 (Last)',
    }),
  ];

  describe('getNextPageId', () => {
    describe('Priority 1: Conditional Redirect', () => {
      it('should redirect to target page when conditional rule is met', () => {
        const { result } = renderHook(() => usePageNavigation({ pages: mockPages }));

        const formData = {
          field1: 'option3', // Matches the condition
        };

        const nextId = result.current.getNextPageId('page1', formData);
        expect(nextId).toBe('page3');
      });

      it('should not use conditional redirect when condition is not met', () => {
        const { result } = renderHook(() => usePageNavigation({ pages: mockPages }));

        const formData = {
          field1: 'option1', // Does not match the condition
        };

        const nextId = result.current.getNextPageId('page1', formData);
        // Should fall through to sequential flow
        expect(nextId).toBe('page2');
      });

      it('should evaluate first matching conditional rule only', () => {
        const pagesWithMultipleRules: (FormSection & { index: number })[] = [
          createMockPage({
            id: 'page1',
            index: 0,
            logics: {
              conditionalRules: [
                {
                  id: 'rule1',
                  action: 'redirect_to_page',
                  targetPageId: 'page3',
                  conditions: {
                    $or: [{ field1: 'value1' }],
                  },
                },
                {
                  id: 'rule2',
                  action: 'redirect_to_page',
                  targetPageId: 'page4',
                  conditions: {
                    $or: [{ field1: 'value1' }],
                  },
                },
              ],
            },
          }),
          createMockPage({ id: 'page2', index: 1 }),
          createMockPage({ id: 'page3', index: 2 }),
          createMockPage({ id: 'page4', index: 3 }),
        ];

        const { result } = renderHook(() => usePageNavigation({ pages: pagesWithMultipleRules }));

        const nextId = result.current.getNextPageId('page1', { field1: 'value1' });
        // Should use first matching rule
        expect(nextId).toBe('page3');
      });
    });

    describe('Priority 2: Normal Redirect', () => {
      it('should use normal redirect when no conditional rules match', () => {
        const { result } = renderHook(() => usePageNavigation({ pages: mockPages }));

        const formData = {
          field1: 'option1', // Does not match conditional rule
        };

        const nextId = result.current.getNextPageId('page2', formData);
        expect(nextId).toBe('page4');
      });

      it('should use normal redirect when no conditional rules exist', () => {
        const { result } = renderHook(() => usePageNavigation({ pages: mockPages }));

        const nextId = result.current.getNextPageId('page3', {});
        expect(nextId).toBe('page5');
      });
    });

    describe('Priority 3: Sequential Flow', () => {
      it('should use sequential flow when no logic is defined', () => {
        const { result } = renderHook(() => usePageNavigation({ pages: mockPages }));

        const nextId = result.current.getNextPageId('page5', {});
        // page5 is last, so should return ending page
        expect(nextId).toBe(__ENDING_PAGE_ID__);
      });

      it('should use sequential flow when no redirects match', () => {
        const { result } = renderHook(() => usePageNavigation({ pages: mockPages }));

        const formData = {
          field1: 'option1', // Does not match conditional
          field2: 'other', // Does not match conditional
        };

        const nextId = result.current.getNextPageId('page4', formData);
        // Should go to next sequential page
        expect(nextId).toBe('page5');
      });
    });

    describe('Priority 4: End of Form', () => {
      it('should return __ENDING_PAGE_ID__ when on last page', () => {
        const { result } = renderHook(() => usePageNavigation({ pages: mockPages }));

        const nextId = result.current.getNextPageId('page5', {});
        expect(nextId).toBe(__ENDING_PAGE_ID__);
      });

      it('should return __ENDING_PAGE_ID__ when page not found', () => {
        const { result } = renderHook(() => usePageNavigation({ pages: mockPages }));

        const nextId = result.current.getNextPageId('nonexistent', {});
        expect(nextId).toBe(__ENDING_PAGE_ID__);
      });
    });

    describe('error handling', () => {
      it('should handle invalid target page in conditional rule gracefully', () => {
        const pagesWithInvalidTarget: (FormSection & { index: number })[] = [
          createMockPage({
            id: 'page1',
            index: 0,
            logics: {
              conditionalRules: [
                {
                  id: 'rule1',
                  action: 'redirect_to_page',
                  targetPageId: 'nonexistent', // Invalid target
                  conditions: {
                    $or: [{ field1: 'value1' }],
                  },
                },
              ],
            },
          }),
          createMockPage({ id: 'page2', index: 1 }),
        ];

        const { result } = renderHook(() => usePageNavigation({ pages: pagesWithInvalidTarget }));

        const nextId = result.current.getNextPageId('page1', { field1: 'value1' });
        // Should fall back to sequential flow
        expect(nextId).toBe('page2');
      });

      it('should handle invalid target page in normal redirect gracefully', () => {
        const pagesWithInvalidTarget: (FormSection & { index: number })[] = [
          createMockPage({
            id: 'page1',
            index: 0,
            logics: {
              normalRedirect: {
                targetPageId: 'nonexistent', // Invalid target
              },
            },
          }),
          createMockPage({ id: 'page2', index: 1 }),
        ];

        const { result } = renderHook(() => usePageNavigation({ pages: pagesWithInvalidTarget }));

        const nextId = result.current.getNextPageId('page1', {});
        // Should fall back to sequential flow
        expect(nextId).toBe('page2');
      });
    });
  });

  describe('getPreviousPageId', () => {
    it('should return previous page by index', () => {
      const { result } = renderHook(() => usePageNavigation({ pages: mockPages }));

      const prevId = result.current.getPreviousPageId('page3');
      expect(prevId).toBe('page2');
    });

    it('should return current page ID when on first page', () => {
      const { result } = renderHook(() => usePageNavigation({ pages: mockPages }));

      const prevId = result.current.getPreviousPageId('page1');
      expect(prevId).toBe('page1');
    });

    it('should return current page ID when page not found', () => {
      const { result } = renderHook(() => usePageNavigation({ pages: mockPages }));

      const prevId = result.current.getPreviousPageId('nonexistent');
      expect(prevId).toBe('nonexistent');
    });
  });

  describe('isLastPage', () => {
    it('should return true for last page', () => {
      const { result } = renderHook(() => usePageNavigation({ pages: mockPages }));

      expect(result.current.isLastPage('page5')).toBe(true);
    });

    it('should return false for non-last page', () => {
      const { result } = renderHook(() => usePageNavigation({ pages: mockPages }));

      expect(result.current.isLastPage('page1')).toBe(false);
      expect(result.current.isLastPage('page3')).toBe(false);
    });

    it('should return false for non-existent page', () => {
      const { result } = renderHook(() => usePageNavigation({ pages: mockPages }));

      expect(result.current.isLastPage('nonexistent')).toBe(false);
    });
  });

  describe('isFirstPage', () => {
    it('should return true for first page', () => {
      const { result } = renderHook(() => usePageNavigation({ pages: mockPages }));

      expect(result.current.isFirstPage('page1')).toBe(true);
    });

    it('should return false for non-first page', () => {
      const { result } = renderHook(() => usePageNavigation({ pages: mockPages }));

      expect(result.current.isFirstPage('page2')).toBe(false);
      expect(result.current.isFirstPage('page5')).toBe(false);
    });

    it('should return false for non-existent page', () => {
      const { result } = renderHook(() => usePageNavigation({ pages: mockPages }));

      expect(result.current.isFirstPage('nonexistent')).toBe(false);
    });
  });

  describe('getPageById', () => {
    it('should return page by ID', () => {
      const { result } = renderHook(() => usePageNavigation({ pages: mockPages }));

      const page = result.current.getPageById('page3') as FormSection & { index: number };
      expect(page).toBeDefined();
      expect(page?.id).toBe('page3');
      expect(page?.index).toBe(2);
    });

    it('should return undefined for non-existent page', () => {
      const { result } = renderHook(() => usePageNavigation({ pages: mockPages }));

      const page = result.current.getPageById('nonexistent');
      expect(page).toBeUndefined();
    });
  });

  describe('getPageByIndex', () => {
    it('should return page by index', () => {
      const { result } = renderHook(() => usePageNavigation({ pages: mockPages }));

      const page = result.current.getPageByIndex(2) as FormSection & { index: number };
      expect(page).toBeDefined();
      expect(page?.id).toBe('page3');
      expect(page?.index).toBe(2);
    });

    it('should return undefined for non-existent index', () => {
      const { result } = renderHook(() => usePageNavigation({ pages: mockPages }));

      const page = result.current.getPageByIndex(999);
      expect(page).toBeUndefined();
    });
  });

  describe('integration with real data structure', () => {
    it('should work with pagesData.ts structure', () => {
      const realPages: (FormSection & { index: number })[] = [
        {
          type: FieldType.Section,
          name: 'Page 1',
          layout: { type: 'image-top-with-space' as any, imageUrl: null, fieldWidth: '100%' },
          content: [],
          id: '01k6fgkn16gkqys15d3690w1rm',
          index: 0,
          logics: {
            conditionalRules: [
              {
                id: '01k6hwz220xwa8rx4g57g4zyg6',
                action: 'redirect_to_page',
                targetPageId: '01k6fgvpcxbbewpjqd6hz0cgqt',
                conditions: {
                  $or: [
                    {
                      '01k6fgpecghnsva5217mqd4xym': {
                        $in: ['01k6hwkxk2ednwfne4r3j0rjrf'],
                      },
                    },
                  ],
                },
              },
            ],
          },
        },
        {
          type: FieldType.Section,
          name: 'Page 2',
          layout: { type: 'image-top-with-space' as any, imageUrl: null, fieldWidth: '100%' },
          content: [],
          id: '01k6fgt6yazsbkqm0bxmanc7k7',
          index: 1,
          logics: {
            conditionalRules: [],
            normalRedirect: {
              targetPageId: '01k6fgvt4059nmhgsdwvdjagxs',
            },
          },
        },
        {
          type: FieldType.Section,
          name: 'Page 3',
          layout: { type: 'image-top-with-space' as any, imageUrl: null, fieldWidth: '100%' },
          content: [],
          id: '01k6fgvpcxbbewpjqd6hz0cgqt',
          index: 2,
          logics: {
            conditionalRules: [],
            normalRedirect: {
              targetPageId: '01k6hwmaagdm52chht92qvdn78',
            },
          },
        },
        {
          type: FieldType.Section,
          name: 'Page 4',
          layout: { type: 'image-top-with-space' as any, imageUrl: null, fieldWidth: '100%' },
          content: [],
          id: '01k6fgvt4059nmhgsdwvdjagxs',
          index: 3,
          logics: {
            conditionalRules: [
              {
                id: '01k6hww98n3sm3jwc7paaq0d0r',
                action: 'redirect_to_page',
                targetPageId: '01k6fgvpcxbbewpjqd6hz0cgqt',
                conditions: {
                  $and: [
                    {
                      '01k6fgpr7z7k67p9t762pjkd3w': {
                        $in: ['01k6hwkxnbr1gk8pa3450fvhpr'],
                      },
                    },
                  ],
                },
              },
            ],
          },
        },
        {
          type: FieldType.Section,
          name: 'Page 5',
          layout: { type: 'image-top-with-space' as any, imageUrl: null, fieldWidth: '100%' },
          content: [],
          id: '01k6hwmaagdm52chht92qvdn78',
          index: 4,
        },
      ];

      const { result } = renderHook(() => usePageNavigation({ pages: realPages }));

      // Test conditional redirect
      const formData1 = {
        '01k6fgpecghnsva5217mqd4xym': '01k6hwkxk2ednwfne4r3j0rjrf',
      };
      const nextId1 = result.current.getNextPageId('01k6fgkn16gkqys15d3690w1rm', formData1);
      expect(nextId1).toBe('01k6fgvpcxbbewpjqd6hz0cgqt');

      // Test normal redirect
      const nextId2 = result.current.getNextPageId('01k6fgt6yazsbkqm0bxmanc7k7', {});
      expect(nextId2).toBe('01k6fgvt4059nmhgsdwvdjagxs');

      // Test last page
      const nextId3 = result.current.getNextPageId('01k6hwmaagdm52chht92qvdn78', {});
      expect(nextId3).toBe(__ENDING_PAGE_ID__);
    });
  });
});
