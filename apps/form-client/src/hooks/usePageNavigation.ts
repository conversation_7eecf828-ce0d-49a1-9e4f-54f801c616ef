/**
 * usePageNavigation Hook
 *
 * Custom React hook for managing multi-page form navigation with conditional logic.
 * Implements a prioritized fallback chain:
 * 1. Conditional Redirect (highest priority) - based on form data conditions
 * 2. Normal Redirect (medium priority) - unconditional redirect
 * 3. Linear Flow (lowest priority) - sequential navigation by index
 * 4. End of Form - returns __ENDING_PAGE_ID__
 *
 * @module usePageNavigation
 */

import { FieldType, type FormField, type FormSection } from '@/types/form-builder';
import { __ENDING_PAGE_ID__ } from '@/types/page-logic';
import { evaluateConditions, resolveOptionIds } from '@/utils/condition-evaluator';
import { useCallback, useMemo } from 'react';

interface UsePageNavigationProps {
  pages: FormSection[];
}

interface UsePageNavigationReturn {
  // eslint-disable-next-line no-unused-vars
  getNextPageId: (currentPageId: string, formData: Record<string, any>) => string;
  // eslint-disable-next-line no-unused-vars
  getPreviousPageId: (currentPageId: string) => string;
  // eslint-disable-next-line no-unused-vars
  isLastPage: (pageId: string) => boolean;
  // eslint-disable-next-line no-unused-vars
  isFirstPage: (pageId: string) => boolean;
  // eslint-disable-next-line no-unused-vars
  getPageById: (pageId: string) => FormSection | undefined;
  // eslint-disable-next-line no-unused-vars
  getPageByIndex: (index: number) => FormSection | undefined;
}

/**
 * Hook for page navigation logic in multi-page forms
 *
 * @param pages - Array of form pages with logic configuration
 * @returns Navigation utility functions
 *
 * @example
 * ```tsx
 * const { getNextPageId } = usePageNavigation({ pages });
 * const nextId = getNextPageId(currentPageId, formData);
 * ```
 */
export function usePageNavigation({ pages }: UsePageNavigationProps): UsePageNavigationReturn {
  // Create lookup maps for performance
  const pageMap = useMemo(() => {
    const map = new Map<string, FormSection>();
    pages.forEach((page) => {
      map.set(page.id, page);
    });
    return map;
  }, [pages]);

  const indexMap = useMemo(() => {
    const map = new Map<number, FormSection>();
    pages.forEach((page, index) => {
      // Use array index if page.index doesn't exist
      const pageIndex = (page as any).index ?? index;
      map.set(pageIndex, page);
    });
    return map;
  }, [pages]);

  const fieldsOptionsMap = useMemo(() => {
    const map = new Map<string, FormField>();
    pages
      .flatMap((page) => page.content || [])
      .forEach((field: FormField) => {
        if (
          [
            FieldType.Checkboxes,
            FieldType.MultipleChoice,
            FieldType.Legal,
            FieldType.Radio,
            FieldType.YesNo,
            FieldType.Dropdown,
          ].includes(field.type as FieldType)
        ) {
          map.set(field.id, field);
        }
      });
    return map;
  }, [pages]);

  /**
   * Get page by ID
   */
  const getPageById = useCallback(
    (pageId: string): FormSection | undefined => {
      return pageMap.get(pageId);
    },
    [pageMap]
  );

  /**
   * Get page by index
   */
  const getPageByIndex = useCallback(
    (index: number): FormSection | undefined => {
      return indexMap.get(index);
    },
    [indexMap]
  );

  /**
   * Check if page is the last page
   */
  const isLastPage = useCallback(
    (pageId: string): boolean => {
      const page = getPageById(pageId);
      if (!page) return false;
      const pageIndex = (page as any).index ?? pages.indexOf(page);
      return pageIndex === pages.length - 1;
    },
    [getPageById, pages]
  );

  /**
   * Check if page is the first page
   */
  const isFirstPage = useCallback(
    (pageId: string): boolean => {
      const page = getPageById(pageId);
      if (!page) return false;
      const pageIndex = (page as any).index ?? pages.indexOf(page);
      return pageIndex === 0;
    },
    [getPageById, pages]
  );

  /**
   * Get sequential next page ID based on index
   *
   * @param currentPage - Current page object
   * @returns Next page ID or __ENDING_PAGE_ID__
   */
  const getSequentialNextPageId = useCallback(
    (currentPage: FormSection): string => {
      const currentIndex = (currentPage as any).index ?? pages.indexOf(currentPage);
      const nextIndex = currentIndex + 1;
      const nextPage = getPageByIndex(nextIndex);
      return nextPage ? nextPage.id : __ENDING_PAGE_ID__;
    },
    [getPageByIndex, pages]
  );

  /**
   * Get the next page ID based on current page and form data
   * Follows the prioritized fallback chain
   *
   * @param currentPageId - ID of the current page
   * @param formData - Current form data values
   * @returns Next page ID or __ENDING_PAGE_ID__
   */
  const getNextPageId = useCallback(
    (currentPageId: string, formData: Record<string, any>): string => {
      const currentPage = getPageById(currentPageId);
      const newData = { ...formData };
      Object.keys(formData).forEach((fieldId) => {
        const field = fieldsOptionsMap.get(fieldId);
        if (field) {
          newData[fieldId] = resolveOptionIds(formData[fieldId], field);
        }
      });

      // If page not found, end the form
      if (!currentPage) {
        return __ENDING_PAGE_ID__;
      }

      const logics = currentPage.logics;

      // If no logic defined, use sequential flow
      if (!logics) {
        return getSequentialNextPageId(currentPage);
      }

      // Priority 1: Check conditional rules
      if (logics.conditionalRules && logics.conditionalRules.length > 0) {
        for (const rule of logics.conditionalRules) {
          if (rule.action === 'redirect_to_page') {
            try {
              const conditionsMet = evaluateConditions(rule.conditions, newData);

              if (conditionsMet) {
                // Validate target page exists
                const targetPage = getPageById(rule.targetPageId);
                if (targetPage) {
                  return rule.targetPageId;
                }
                console.warn(
                  `Target page ${rule.targetPageId} not found for conditional rule ${rule.id}`
                );
              }
            } catch (error) {
              console.error(`Error evaluating conditional rule ${rule.id}:`, error);
            }
          }
        }
      }

      // Priority 2: Check normal redirect
      if (logics.normalRedirect?.targetPageId) {
        const targetPage = getPageById(logics.normalRedirect.targetPageId);
        if (targetPage) {
          return logics.normalRedirect.targetPageId;
        }
        console.warn(
          `Target page ${logics.normalRedirect.targetPageId} not found for normal redirect`
        );
      }

      // Priority 3: Sequential flow (fallback)
      return getSequentialNextPageId(currentPage);
    },
    [getPageById, getSequentialNextPageId, fieldsOptionsMap]
  );

  /**
   * Get the previous page ID (simple sequential navigation)
   * Note: Previous navigation doesn't consider conditional logic
   *
   * @param currentPageId - ID of the current page
   * @returns Previous page ID or current page ID if first page
   */
  const getPreviousPageId = useCallback(
    (currentPageId: string): string => {
      const currentPage = getPageById(currentPageId);
      if (!currentPage) {
        return currentPageId;
      }

      const currentIndex = (currentPage as any).index ?? pages.indexOf(currentPage);
      if (currentIndex === 0) {
        return currentPageId;
      }

      const previousIndex = currentIndex - 1;
      const previousPage = getPageByIndex(previousIndex);
      return previousPage ? previousPage.id : currentPageId;
    },
    [getPageById, getPageByIndex, pages]
  );

  return {
    getNextPageId,
    getPreviousPageId,
    isLastPage,
    isFirstPage,
    getPageById,
    getPageByIndex,
  };
}
