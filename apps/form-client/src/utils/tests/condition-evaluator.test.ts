/**
 * Tests for Condition Evaluator
 *
 * Tests the page navigation condition evaluation logic including:
 * - AND/OR logic combinations
 * - All supported operators ($eq, $ne, $in, $nin, $regex, $exists, comparison operators)
 * - Direct value comparisons
 * - Null/undefined handling
 * - Array field value handling
 * - Option ID resolution
 */

import { FieldType } from '@/types/form-builder';
import { describe, expect, it } from 'vitest';
import { evaluateConditions, extractFieldIds, resolveOptionIds } from '../condition-evaluator';

describe('condition-evaluator', () => {
  describe('evaluateConditions', () => {
    describe('AND logic', () => {
      it('should return true if all AND conditions are met', () => {
        const conditions = {
          $and: [
            {
              field1: 'value1',
            },
            {
              field2: 'value2',
            },
          ],
        };

        const formData = {
          field1: 'value1',
          field2: 'value2',
        };

        expect(evaluateConditions(conditions, formData)).toBe(true);
      });

      it('should return false if any AND condition fails', () => {
        const conditions = {
          $and: [
            {
              field1: 'value1',
            },
            {
              field2: 'value2',
            },
          ],
        };

        const formData = {
          field1: 'value1',
          field2: 'wrong_value',
        };

        expect(evaluateConditions(conditions, formData)).toBe(false);
      });
    });

    describe('OR logic', () => {
      it('should return true if at least one OR condition is met', () => {
        const conditions = {
          $or: [
            {
              field1: 'value1',
            },
            {
              field2: 'value2',
            },
          ],
        };

        const formData = {
          field1: 'wrong_value',
          field2: 'value2', // This matches
        };

        expect(evaluateConditions(conditions, formData)).toBe(true);
      });

      it('should return false if no OR conditions are met', () => {
        const conditions = {
          $or: [
            {
              field1: 'value1',
            },
            {
              field2: 'value2',
            },
          ],
        };

        const formData = {
          field1: 'wrong_value',
          field2: 'wrong_value',
        };

        expect(evaluateConditions(conditions, formData)).toBe(false);
      });

      it('should evaluate all OR conditions even if first one fails', () => {
        const conditions = {
          $or: [
            {
              field1: { $eq: 'value1' },
            },
            {
              field2: null, // Direct null comparison
            },
          ],
        };

        const formData = {
          field1: 'wrong_value',
          // field2 is missing (undefined)
        };

        expect(evaluateConditions(conditions, formData)).toBe(true);
      });
    });

    describe('nested logic', () => {
      it('should handle nested OR within AND', () => {
        const conditions = {
          $and: [
            {
              field1: 'value1',
            },
            {
              $or: [
                {
                  field2: 'value2a',
                },
                {
                  field2: 'value2b',
                },
              ],
            },
          ],
        };

        const formData = {
          field1: 'value1',
          field2: 'value2b', // Matches one of the OR conditions
        };

        expect(evaluateConditions(conditions, formData)).toBe(true);
      });

      it('should handle nested AND within OR', () => {
        const conditions = {
          $or: [
            {
              $and: [
                {
                  field1: 'value1',
                },
                {
                  field2: 'value2',
                },
              ],
            },
            {
              field3: 'value3',
            },
          ],
        };

        const formData = {
          field1: 'value1',
          field2: 'value2',
          field3: 'wrong_value',
        };

        expect(evaluateConditions(conditions, formData)).toBe(true);
      });
    });

    describe('direct value comparisons', () => {
      it('should handle direct null comparison for missing fields', () => {
        const conditions = {
          $and: [
            {
              field1: null,
            },
          ],
        };

        const formData = {}; // field1 is missing (undefined)

        expect(evaluateConditions(conditions, formData)).toBe(true);
      });

      it('should handle direct null comparison for null fields', () => {
        const conditions = {
          $and: [
            {
              field1: null,
            },
          ],
        };

        const formData = {
          field1: null,
        };

        expect(evaluateConditions(conditions, formData)).toBe(true);
      });

      it('should handle direct null comparison for empty string fields', () => {
        const conditions = {
          $and: [
            {
              field1: null,
            },
          ],
        };

        const formData = {
          field1: '',
        };

        expect(evaluateConditions(conditions, formData)).toBe(true);
      });

      it('should fail direct null comparison for non-empty fields', () => {
        const conditions = {
          $and: [
            {
              field1: null,
            },
          ],
        };

        const formData = {
          field1: 'some_value',
        };

        expect(evaluateConditions(conditions, formData)).toBe(false);
      });

      it('should handle direct string comparison', () => {
        const conditions = {
          $and: [
            {
              field1: 'expected_value',
            },
          ],
        };

        const formData = {
          field1: 'expected_value',
        };

        expect(evaluateConditions(conditions, formData)).toBe(true);
      });

      it('should handle direct number comparison', () => {
        const conditions = {
          $and: [
            {
              field1: 42,
            },
          ],
        };

        const formData = {
          field1: 42,
        };

        expect(evaluateConditions(conditions, formData)).toBe(true);
      });

      it('should handle direct boolean comparison', () => {
        const conditions = {
          $and: [
            {
              field1: true,
            },
          ],
        };

        const formData = {
          field1: true,
        };

        expect(evaluateConditions(conditions, formData)).toBe(true);
      });
    });

    describe('$ne operator', () => {
      it('should return true if values are not equal', () => {
        const conditions = {
          $and: [
            {
              field1: { $ne: 'value1' },
            },
          ],
        };

        const formData = {
          field1: 'value2',
        };

        expect(evaluateConditions(conditions, formData)).toBe(true);
      });

      it('should return false if values are equal', () => {
        const conditions = {
          $and: [
            {
              field1: { $ne: 'value1' },
            },
          ],
        };

        const formData = {
          field1: 'value1',
        };

        expect(evaluateConditions(conditions, formData)).toBe(false);
      });

      it('should handle $ne null correctly for fields with values', () => {
        const conditions = {
          $and: [
            {
              field1: { $ne: null },
            },
          ],
        };

        const formData = {
          field1: 'some_value',
        };

        expect(evaluateConditions(conditions, formData)).toBe(true);
      });

      it('should handle $ne null correctly for empty fields', () => {
        const conditions = {
          $and: [
            {
              field1: { $ne: null },
            },
          ],
        };

        const formData = {
          field1: null,
        };

        expect(evaluateConditions(conditions, formData)).toBe(false);
      });

      it('should handle $ne null correctly for undefined fields', () => {
        const conditions = {
          $and: [
            {
              field1: { $ne: null },
            },
          ],
        };

        const formData = {}; // field1 is undefined

        expect(evaluateConditions(conditions, formData)).toBe(false);
      });

      it('should handle $ne null correctly for empty string fields', () => {
        const conditions = {
          $and: [
            {
              field1: { $ne: null },
            },
          ],
        };

        const formData = {
          field1: '',
        };

        expect(evaluateConditions(conditions, formData)).toBe(false);
      });
    });

    describe('$in operator', () => {
      it('should return true if value is in array', () => {
        const conditions = {
          $and: [
            {
              field1: { $in: ['value1', 'value2', 'value3'] },
            },
          ],
        };

        const formData = {
          field1: 'value2',
        };

        expect(evaluateConditions(conditions, formData)).toBe(true);
      });

      it('should return false if value is not in array', () => {
        const conditions = {
          $and: [
            {
              field1: { $in: ['value1', 'value2', 'value3'] },
            },
          ],
        };

        const formData = {
          field1: 'value4',
        };

        expect(evaluateConditions(conditions, formData)).toBe(false);
      });

      it('should handle array field values', () => {
        const conditions = {
          $and: [
            {
              field1: { $in: ['option1', 'option2'] },
            },
          ],
        };

        const formData = {
          field1: ['option1', 'option3'],
        };

        expect(evaluateConditions(conditions, formData)).toBe(true);
      });

      it('should return false for null/undefined/empty values', () => {
        const conditions = {
          $and: [
            {
              field1: { $in: ['value1', 'value2'] },
            },
          ],
        };

        expect(evaluateConditions(conditions, { field1: null })).toBe(false);
        expect(evaluateConditions(conditions, { field1: undefined })).toBe(false);
        expect(evaluateConditions(conditions, { field1: '' })).toBe(false);
        expect(evaluateConditions(conditions, { field1: [] })).toBe(false);
      });

      it('should handle object field values with value property', () => {
        const conditions = {
          $and: [
            {
              field1: { $in: ['option1', 'option2'] },
            },
          ],
        };

        const formData = {
          field1: { value: 'option1' },
        };

        expect(evaluateConditions(conditions, formData)).toBe(true);
      });
    });

    describe('$nin operator', () => {
      it('should return true if value is not in array', () => {
        const conditions = {
          $and: [
            {
              field1: { $nin: ['value1', 'value2', 'value3'] },
            },
          ],
        };

        const formData = {
          field1: 'value4',
        };

        expect(evaluateConditions(conditions, formData)).toBe(true);
      });

      it('should return false if value is in array', () => {
        const conditions = {
          $and: [
            {
              field1: { $nin: ['value1', 'value2', 'value3'] },
            },
          ],
        };

        const formData = {
          field1: 'value2',
        };

        expect(evaluateConditions(conditions, formData)).toBe(false);
      });

      it('should return true for null/undefined/empty values when not in array', () => {
        const conditions = {
          $and: [
            {
              field1: { $nin: ['value1', 'value2'] },
            },
          ],
        };

        expect(evaluateConditions(conditions, { field1: null })).toBe(true);
        expect(evaluateConditions(conditions, { field1: undefined })).toBe(true);
        expect(evaluateConditions(conditions, { field1: '' })).toBe(true);
        expect(evaluateConditions(conditions, { field1: [] })).toBe(true);
      });
    });

    describe('$regex operator', () => {
      it('should return true if string matches regex', () => {
        const conditions = {
          $and: [
            {
              field1: { $regex: 'test' },
            },
          ],
        };

        const formData = {
          field1: 'this is a test string',
        };

        expect(evaluateConditions(conditions, formData)).toBe(true);
      });

      it('should return false if string does not match regex', () => {
        const conditions = {
          $and: [
            {
              field1: { $regex: 'test' },
            },
          ],
        };

        const formData = {
          field1: 'this is a sample string',
        };

        expect(evaluateConditions(conditions, formData)).toBe(false);
      });

      it('should handle null/undefined values', () => {
        const conditions = {
          $and: [
            {
              field1: { $regex: 'test' },
            },
          ],
        };

        expect(evaluateConditions(conditions, { field1: null })).toBe(false);
        expect(evaluateConditions(conditions, { field1: undefined })).toBe(false);
      });
    });

    describe('$exists operator', () => {
      it('should return true if field exists and $exists is true', () => {
        const conditions = {
          $and: [
            {
              field1: { $exists: true },
            },
          ],
        };

        const formData = {
          field1: 'some_value',
        };

        expect(evaluateConditions(conditions, formData)).toBe(true);
      });

      it('should return false if field does not exist and $exists is true', () => {
        const conditions = {
          $and: [
            {
              field1: { $exists: true },
            },
          ],
        };

        const formData = {}; // field1 is missing

        expect(evaluateConditions(conditions, formData)).toBe(false);
      });

      it('should return true if field does not exist and $exists is false', () => {
        const conditions = {
          $and: [
            {
              field1: { $exists: false },
            },
          ],
        };

        const formData = {}; // field1 is missing

        expect(evaluateConditions(conditions, formData)).toBe(true);
      });

      it('should treat empty string as non-existent', () => {
        const conditions = {
          $and: [
            {
              field1: { $exists: false },
            },
          ],
        };

        const formData = {
          field1: '',
        };

        expect(evaluateConditions(conditions, formData)).toBe(true);
      });
    });

    describe('comparison operators', () => {
      it('should handle $gt operator', () => {
        const conditions = {
          $and: [
            {
              field1: { $gt: 5 },
            },
          ],
        };

        expect(evaluateConditions(conditions, { field1: 10 })).toBe(true);
        expect(evaluateConditions(conditions, { field1: 5 })).toBe(false);
        expect(evaluateConditions(conditions, { field1: 3 })).toBe(false);
      });

      it('should handle $gte operator', () => {
        const conditions = {
          $and: [
            {
              field1: { $gte: 5 },
            },
          ],
        };

        expect(evaluateConditions(conditions, { field1: 10 })).toBe(true);
        expect(evaluateConditions(conditions, { field1: 5 })).toBe(true);
        expect(evaluateConditions(conditions, { field1: 3 })).toBe(false);
      });

      it('should handle $lt operator', () => {
        const conditions = {
          $and: [
            {
              field1: { $lt: 5 },
            },
          ],
        };

        expect(evaluateConditions(conditions, { field1: 3 })).toBe(true);
        expect(evaluateConditions(conditions, { field1: 5 })).toBe(false);
        expect(evaluateConditions(conditions, { field1: 10 })).toBe(false);
      });

      it('should handle $lte operator', () => {
        const conditions = {
          $and: [
            {
              field1: { $lte: 5 },
            },
          ],
        };

        expect(evaluateConditions(conditions, { field1: 3 })).toBe(true);
        expect(evaluateConditions(conditions, { field1: 5 })).toBe(true);
        expect(evaluateConditions(conditions, { field1: 10 })).toBe(false);
      });

      it('should handle non-numeric values in comparison operators', () => {
        const conditions = {
          $and: [
            {
              field1: { $gt: 5 },
            },
          ],
        };

        expect(evaluateConditions(conditions, { field1: 'not_a_number' })).toBe(false);
      });
    });

    describe('edge cases', () => {
      it('should handle empty conditions', () => {
        const conditions = {};
        const formData = { field1: 'value1' };

        expect(evaluateConditions(conditions, formData)).toBe(true);
      });

      it('should handle missing field values as undefined', () => {
        const conditions = {
          $and: [
            {
              field1: { $eq: 'value1' },
            },
          ],
        };

        const formData = {}; // field1 is missing

        expect(evaluateConditions(conditions, formData)).toBe(false);
      });

      it('should handle unknown operators gracefully', () => {
        const conditions = {
          $and: [
            {
              field1: { $unknown: 'value1' } as any,
            },
          ],
        };

        const formData = {
          field1: 'value1',
        };

        expect(evaluateConditions(conditions, formData)).toBe(false);
      });
    });

    describe('real-world scenarios', () => {
      it('should handle complex form navigation condition from pagesData', () => {
        // This is the actual condition from the user's pagesData
        const conditions = {
          $or: [
            {
              $and: [
                {
                  '01k6fgp7y8b926qpm4gsc477qj': { $ne: null },
                },
                {
                  '01k6fgpecghnsva5217mqd4xym': {
                    $nin: ['01k6m1581p2499sehs71akvn8m', 'other'],
                  },
                },
              ],
            },
            {
              $and: [
                {
                  '01k6fgp7y8b926qpm4gsc477qj': null,
                },
                {
                  '01k6fgpr7z7k67p9t762pjkd3w': {
                    $in: ['01k6m15837fw489f0h41fa7e0m', '01k6m158371dsr5b59435tv11e'],
                  },
                },
              ],
            },
          ],
        };

        // User's form data - phone field missing, checkboxes has "Ban sung"
        const formData = {
          '01k6fgpr7z7k67p9t762pjkd3w': ['01k6m15837fw489f0h41fa7e0m'], // Ban sung selected
          // Phone field is missing (undefined)
        };

        expect(evaluateConditions(conditions, formData)).toBe(true);
      });
    });
  });

  describe('extractFieldIds', () => {
    it('should extract field IDs from simple conditions', () => {
      const conditions = {
        $or: [
          {
            field1: { $eq: 'value1' },
          },
          {
            field2: { $in: ['value2', 'value3'] },
          },
        ],
      };

      const fieldIds = extractFieldIds(conditions);
      expect(fieldIds).toContain('field1');
      expect(fieldIds).toContain('field2');
      expect(fieldIds).toHaveLength(2);
    });

    it('should extract field IDs from nested conditions', () => {
      const conditions = {
        $and: [
          {
            field1: { $eq: 'value1' },
          },
          {
            $or: [
              {
                field2: { $eq: 'value2' },
              },
              {
                field3: { $in: ['value3'] },
              },
            ],
          },
        ],
      };

      const fieldIds = extractFieldIds(conditions);
      expect(fieldIds).toContain('field1');
      expect(fieldIds).toContain('field2');
      expect(fieldIds).toContain('field3');
      expect(fieldIds).toHaveLength(3);
    });

    it('should remove duplicate field IDs', () => {
      const conditions = {
        $or: [
          {
            field1: { $eq: 'value1' },
          },
          {
            field1: { $ne: 'value2' },
          },
        ],
      };

      const fieldIds = extractFieldIds(conditions);
      expect(fieldIds).toContain('field1');
      expect(fieldIds).toHaveLength(1);
    });

    it('should handle direct value conditions', () => {
      const conditions = {
        $and: [
          {
            field1: null,
          },
          {
            field2: 'direct_value',
          },
        ],
      };

      const fieldIds = extractFieldIds(conditions);
      expect(fieldIds).toContain('field1');
      expect(fieldIds).toContain('field2');
      expect(fieldIds).toHaveLength(2);
    });
  });

  describe('resolveOptionIds', () => {
    const mockField = {
      type: FieldType.MultipleChoice,
      options: [
        { id: 'option1_id', label: 'Option 1' },
        { id: 'option2_id', label: 'Option 2' },
        { id: 'option3_id', label: 'Option 3' },
      ],
    };

    describe('array values', () => {
      it('should resolve array of option objects to IDs', () => {
        const value = [
          { value: 'Option 1', is_other: false },
          { value: 'Option 3', is_other: false },
        ];

        const result = resolveOptionIds(value, mockField);
        expect(result).toEqual(['option1_id', 'option3_id']);
      });

      it('should handle other options in arrays', () => {
        const value = [
          { value: 'Option 1', is_other: false },
          { value: 'Custom Option', is_other: true },
        ];

        const result = resolveOptionIds(value, mockField);
        expect(result).toEqual(['option1_id', 'other']);
      });

      it('should filter out unmatched options', () => {
        const value = [
          { value: 'Option 1', is_other: false },
          { value: 'Non-existent Option', is_other: false },
        ];

        const result = resolveOptionIds(value, mockField);
        expect(result).toEqual(['option1_id']);
      });
    });

    describe('object values', () => {
      it('should resolve single option object to ID', () => {
        const value = { value: 'Option 2', is_other: false };

        const result = resolveOptionIds(value, mockField);
        expect(result).toBe('option2_id');
      });

      it('should handle other option objects', () => {
        const value = { value: 'Custom Option', is_other: true, id: 'custom_id' };

        const result = resolveOptionIds(value, mockField);
        expect(result).toBe('custom_id');
      });

      it('should return undefined for unmatched option objects', () => {
        const value = { value: 'Non-existent Option', is_other: false };

        const result = resolveOptionIds(value, mockField);
        expect(result).toBeUndefined();
      });
    });

    describe('string values', () => {
      it('should resolve string values to IDs', () => {
        const value = 'Option 1';

        const result = resolveOptionIds(value, mockField);
        expect(result).toBe('option1_id');
      });

      it('should return undefined for unmatched string values', () => {
        const value = 'Non-existent Option';

        const result = resolveOptionIds(value, mockField);
        expect(result).toBeUndefined();
      });
    });

    describe('legal field type', () => {
      const legalField = {
        type: FieldType.Legal,
      };

      it('should return true for non-empty arrays', () => {
        const value = [{ value: 'accepted', is_other: false }];

        const result = resolveOptionIds(value, legalField);
        expect(result).toBe(true);
      });

      it('should return false for empty arrays', () => {
        const value: any[] = [];

        const result = resolveOptionIds(value, legalField);
        expect(result).toBe(false);
      });

      it('should return true for non-empty objects', () => {
        const value = { accepted: true };

        const result = resolveOptionIds(value, legalField);
        expect(result).toBe(true);
      });

      it('should return false for empty objects', () => {
        const value = {};

        const result = resolveOptionIds(value, legalField);
        expect(result).toBe(false);
      });
    });

    describe('special characters handling', () => {
      const fieldWithSpecialChars = {
        type: FieldType.MultipleChoice,
        options: [
          { id: 'option1_id', label: 'Option\u200B 1' }, // Zero-width space
          { id: 'option2_id', label: 'Option\u2060 2' }, // Word joiner
        ],
      };

      it('should handle special characters in option labels', () => {
        const value = 'Option 1'; // Without special characters

        const result = resolveOptionIds(value, fieldWithSpecialChars);
        expect(result).toBe('option1_id');
      });

      it('should handle special characters in input values', () => {
        const value = 'Option\u200B 2'; // With zero-width space

        const result = resolveOptionIds(value, fieldWithSpecialChars);
        expect(result).toBe('option2_id');
      });
    });
  });
});
