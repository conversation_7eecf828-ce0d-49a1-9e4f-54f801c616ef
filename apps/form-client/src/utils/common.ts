export const shouldShowHeader = (settings) => {
  const { logoImage, isUsingText, text } = settings;
  return !!logoImage || (isUsingText && !!text);
};

export const postMessage = (eventType: string, data?: Record<string, unknown>) => {
  const message = {
    [eventType]: data,
  };
  window.parent.postMessage(message, '*');
};

export const getLanguageLocale = (headers: { [key: string]: string }) => {
  const handledLanguage = ['ja', 'ja-JP'];
  const language = headers?.['accept-language']?.split(',')[0] ?? '';
  if (language) {
    return handledLanguage.includes(language) ? 'ja' : 'en';
  }
  return 'en';
};

/**
 * Decodes HTML entities in a string
 * @param text The text containing HTML entities
 * @returns The decoded text
 */
export const decodeHtmlEntities = (text: string): string => {
  if (!text) return '';

  // Create a temporary DOM element to decode HTML entities
  const textarea = document.createElement('textarea');
  textarea.innerHTML = text;
  return textarea.value;
};
