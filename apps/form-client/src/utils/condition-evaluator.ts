/**
 * Condition Evaluator
 *
 * Pure utility functions for evaluating page navigation conditions.
 * Supports nested AND/OR logic and various comparison operators.
 *
 * @module page-condition-evaluator
 */

import { FieldType } from '@/types/form-builder';
import type { ConditionGroup, FieldCondition, PageConditionOperator } from '@/types/page-logic';
import dayjs from 'dayjs';
import { isArray, isDate, isEmpty, isObject } from 'lodash';

const SPECIAL_CHARACTERS = /\u200B|\u200C|\u200D|\u2060|\uFEFF/g;

/**
 * Evaluates a condition group against form data
 *
 * @param conditions - The condition group to evaluate (with 'and' or 'or')
 * @param formData - Current form data values
 * @returns true if conditions are met, false otherwise
 */
export function evaluateConditions(
  conditions: ConditionGroup,
  formData: Record<string, any>
): boolean {
  // Handle AND logic - all conditions must be true
  if (conditions.$and) {
    return conditions.$and.every((condition) => evaluateSingleCondition(condition, formData));
  }

  // Handle OR logic - at least one condition must be true
  if (conditions.$or) {
    return conditions.$or.some((condition) => evaluateSingleCondition(condition, formData));
  }

  // Empty condition group defaults to true
  return true;
}

/**
 * Evaluates a single condition (can be field condition or nested group)
 *
 * @param condition - Field condition or nested condition group
 * @param formData - Current form data values
 * @returns true if condition is met, false otherwise
 */
function evaluateSingleCondition(
  condition: FieldCondition | ConditionGroup,
  formData: Record<string, any>
): boolean {
  // Check if it's a nested condition group
  if ('$and' in condition || '$or' in condition) {
    return evaluateConditions(condition as ConditionGroup, formData);
  }

  // It's a field condition - evaluate all field checks
  const fieldCondition = condition as FieldCondition;
  for (const [fieldId, operators] of Object.entries(fieldCondition)) {
    const fieldValue = formData[fieldId];
    if (!evaluateFieldCondition(fieldValue, operators)) {
      return false;
    }
  }

  return true;
}

/**
 * Evaluates operators against a field value
 *
 * @param fieldValue - The actual field value from form data
 * @param operators - Object containing operators and their comparison values
 * @returns true if all operator conditions are met, false otherwise
 */
function evaluateFieldCondition(fieldValue: any, operators: { [operator: string]: any }): boolean {
  // Handle direct value comparison (e.g., "field": null means "field": {"$eq": null})
  if (operators === null || operators === undefined) {
    // Direct value comparison - treat as $eq
    // Special case: treat undefined as null for missing fields
    if (fieldValue === null || fieldValue === undefined || fieldValue === '') {
      return true;
    }
    return false;
  }

  // If operators is a primitive (string, number, boolean), treat as $eq
  if (
    typeof operators === 'string' ||
    typeof operators === 'number' ||
    typeof operators === 'boolean'
  ) {
    // Special case: treat undefined as null for missing fields
    if (fieldValue === undefined || fieldValue === null || fieldValue === '') {
      return operators === null || operators === '';
    }

    if (isDate(fieldValue)) {
      return dayjs(fieldValue).isSame(dayjs(operators as string));
    }
    return fieldValue === operators;
  }

  for (const [operator, compareValue] of Object.entries(operators)) {
    const result = evaluateOperator(fieldValue, operator as PageConditionOperator, compareValue);
    if (!result) {
      return false;
    }
  }
  return true;
}

/**
 * Evaluates a single operator against a field value
 *
 * @param fieldValue - The actual field value from form data
 * @param operator - The comparison operator
 * @param compareValue - The value to compare against
 * @returns true if operator condition is met, false otherwise
 */
function evaluateOperator(
  fieldValue: any,
  operator: PageConditionOperator,
  compareValue: any
): boolean {
  switch (operator) {
    case '$ne': {
      // Handle null/undefined/empty string/object checks properly
      if (compareValue === null) {
        // $ne: null should pass if field has any value (not null, undefined, or empty)
        const hasValue = isObject(fieldValue)
          ? !isEmpty(fieldValue)
          : fieldValue !== null && fieldValue !== undefined && fieldValue !== '';
        if (!hasValue) return false;
      } else {
        // Regular not-equals comparison
        // if value is a Date, convert it to a string
        if (isDate(fieldValue)) {
          return !dayjs(fieldValue).isSame(dayjs(compareValue as string));
        }
        if (fieldValue === compareValue) return false;
      }
      break;
    }
    case '$in': {
      const result = evaluateIn(fieldValue, compareValue);
      if (!result) return false;
      break;
    }
    case '$nin': {
      if (evaluateIn(fieldValue, compareValue)) return false;
      break;
    }
    case '$regex': {
      const regex = new RegExp(compareValue as string);
      if (!regex.test(String(fieldValue || ''))) return false;
      break;
    }
    case '$exists': {
      const exists = fieldValue !== undefined && fieldValue !== null && fieldValue !== '';
      if (exists !== compareValue) return false;
      break;
    }
    case '$gt': {
      const numField = Number(fieldValue);
      const numValue = Number(compareValue);
      if (Number.isNaN(numField) || Number.isNaN(numValue) || numField <= numValue) return false;
      break;
    }
    case '$gte': {
      const numField = Number(fieldValue);
      const numValue = Number(compareValue);
      if (Number.isNaN(numField) || Number.isNaN(numValue) || numField < numValue) return false;
      break;
    }
    case '$lt': {
      const numField = Number(fieldValue);
      const numValue = Number(compareValue);
      if (Number.isNaN(numField) || Number.isNaN(numValue) || numField >= numValue) return false;
      break;
    }
    case '$lte': {
      const numField = Number(fieldValue);
      const numValue = Number(compareValue);
      if (Number.isNaN(numField) || Number.isNaN(numValue) || numField > numValue) return false;
      break;
    }
    case '$not': {
      // $not negates the result of the nested condition
      if (evaluateFieldCondition(fieldValue, compareValue)) return false;
      break;
    }

    default:
      console.warn(`Unknown operator: ${operator}`);
      return false;
  }

  return true;
}

/**
 * Evaluates 'in' operator - checks if value is in array
 * Handles string/number type coercion for robust comparison
 */
function evaluateIn(fieldValue: any, compareArray: any[]): boolean {
  if (!Array.isArray(compareArray)) {
    return false;
  }

  // Handle null/undefined/empty values - they should not match any option IDs
  if (fieldValue === null || fieldValue === undefined || fieldValue === '') {
    return false;
  }

  // Convert both field value and array items to strings for comparison
  const normalizedArray = compareArray.map((v) => String(v ?? ''));

  if (isArray(fieldValue)) {
    // Handle empty arrays
    if (fieldValue.length === 0) {
      return false;
    }
    const normalizedFieldValue = fieldValue.map((v) => String(v ?? ''));
    return normalizedFieldValue.some((v) => normalizedArray.includes(v));
  }

  const normalizedFieldValue = String(fieldValue?.value ?? fieldValue);
  return normalizedArray.includes(normalizedFieldValue);
}

/**
 * Extracts all field IDs referenced in conditions
 * Useful for dependency tracking and debugging
 *
 * @param conditions - The condition group to analyze
 * @returns Array of unique field IDs
 */
export function extractFieldIds(conditions: ConditionGroup): string[] {
  const fieldIds: string[] = [];

  function traverse(condition: FieldCondition | ConditionGroup) {
    if ('$and' in condition || '$or' in condition) {
      const group = condition as ConditionGroup;
      if (group.$and) {
        group.$and.forEach(traverse);
      }
      if (group.$or) {
        group.$or.forEach(traverse);
      }
    } else {
      // Field condition - extract field IDs
      Object.keys(condition as FieldCondition).forEach((fieldId) => {
        fieldIds.push(fieldId);
      });
    }
  }

  traverse(conditions);
  return Array.from(new Set(fieldIds)); // Remove duplicates
}

export function resolveOptionIds(
  value: string | Record<string, any> | Record<string, any>[],
  field: any
): string[] | string | boolean {
  // For legal field, if value is not empty array, return true
  if (field.type === FieldType.Legal) {
    return !isEmpty(value);
  }

  const _compareValues = (value: string, target: string) =>
    value.replace(SPECIAL_CHARACTERS, '').trim() === target.replace(SPECIAL_CHARACTERS, '').trim();

  if (isArray(value)) {
    const _ids = value
      .map((v) => {
        if (v.is_other) {
          return 'other';
        }

        const _option = field?.options?.find((o) => _compareValues(o.label, v.value));
        return _option?.id;
      })
      .filter((id) => id !== undefined);
    return _ids;
  }

  if (isObject(value)) {
    if (value.is_other) {
      return value.id;
    }

    // Remove all zero-width spaces and trim the value
    const _option = field?.options?.find((o) => _compareValues(o.label, value.value));
    return _option?.id;
  }

  const _option = field?.options?.find((o) => _compareValues(o.label, value));
  return _option?.id;
}
