import {
  FieldType,
  type FormField,
  type FormSection,
  type FormSettings,
  GroupFieldType,
  type GroupFormField,
  ValidatorType,
} from '@/types/form-builder';
import { MIME_TYPES } from '@mantine/dropzone';
import { notifications } from '@mantine/notifications';
import dayjs from 'dayjs';
import { isEqual, uniq } from 'lodash';
import { i18n } from 'next-i18next';
import { type ZodString, type ZodTypeAny, z } from 'zod';

const JP_PHONENUMBER_REGEX =
  /^(\(\d{2,3}\)\s?\d{4}-\d{4}|\(\d{3,4}\)\s?\d{2,4}-\d{4}|0\d{1,4}-\d{1,4}-\d{4}|0\d{3}-\d{3}-\d{3}|0\d{9,10})$/;

const DEFAULT_MAX_LENGTH = {
  [FieldType.ShortQA]: 100,
  [FieldType.LongQA]: 10000,
};

export const isValidDateSelector = (dateString: string, isDob: boolean) => {
  const parts = dateString.split('-').map(Number);
  const year = parts[0];
  const month = parts[1];
  const day = parts[2];
  const date = new Date(year, month - 1, day);
  let isValid =
    date.getFullYear() === year && date.getMonth() === month - 1 && date.getDate() === day;
  if (isDob) {
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    if (date > today) {
      isValid = false;
    }
  }
  return isValid;
};

export const getGridColsByLayout = (layout) => {
  const gridColsMap = {
    one_column: 12,
    two_column: 6,
  };
  return gridColsMap[layout];
};

export const getGridColsByWidth = (width) => {
  return width ? (Number.parseInt(width.replace('%', '')) * 12) / 100 : 12;
};

export const getValidationSchema = (questions, visibilityState = {}) => {
  const rules = {};
  const setRules = (questions) => {
    questions.forEach((question) => {
      const isVisible = visibilityState[question.id] !== false;

      if (question.isHide || !isVisible) return;
      if ((question as GroupFormField).fields?.length) {
        setRules(question.fields);
      } else if (question.validators?.length || question.type === FieldType.Legal) {
        rules[question.id] = getQuestionSchema(question);
      }
    });
  };
  setRules(questions);

  return z.object(rules);
};

const getInitialSchema = (question, requiredRule) => {
  const requiredError = requiredRule?.message || i18n?.t('requiredError');
  const optionRequiredError = requiredRule?.message || i18n?.t('optionRequiredError');
  if ([FieldType.MultipleChoice, FieldType.YesNo].includes(question.type)) {
    return requiredRule
      ? z.object({
          value: z.string().min(1, optionRequiredError),
          is_other: z.boolean().optional(),
        })
      : z.object({
          value: z.string(),
          is_other: z.boolean().optional(),
        });
  }
  if (question.type === FieldType.Checkboxes) {
    return requiredRule
      ? z
          .array(
            z.object({
              value: z.string(),
              is_other: z.boolean().optional(),
            }),
            { required_error: optionRequiredError }
          )
          .min(1, optionRequiredError)
      : z.array(
          z.object({
            value: z.string(),
            is_other: z.boolean().optional(),
          })
        );
  }
  if (question.type === FieldType.Legal) {
    const legalRequiredError = requiredRule?.message || i18n?.t('legalRequiredError');
    return z
      .array(
        z.object({
          value: z.string(),
          is_other: z.boolean().optional(),
        }),
        { required_error: legalRequiredError }
      )
      .min(1, legalRequiredError);
  }
  if (question.type === FieldType.Checkbox) {
    const checkboxRequiredError = requiredRule?.message || i18n?.t('checkboxRequiredError');
    return requiredRule
      ? z
          .boolean({
            required_error: checkboxRequiredError,
          })
          .refine((value) => value === true, {
            message: checkboxRequiredError,
          })
      : z.boolean();
  }
  if (question.type === FieldType.Dropdown) {
    return requiredRule
      ? z
          .string({ required_error: optionRequiredError, invalid_type_error: optionRequiredError })
          .min(1, optionRequiredError)
      : z.string().nullable();
  }
  if (question.type === FieldType.DateSelector) {
    return z.string({ message: requiredError }).refine(
      (value) => {
        if (!value) return true;
        return isValidDateSelector(value, question.dobEnabled);
      },
      {
        message: i18n?.t(question.dobEnabled ? 'invalidDobError' : 'invalidDateError'),
      }
    );
  }
  if ([FieldType.Date, FieldType.DateTime].includes(question.type)) {
    return z.date({
      message: requiredError,
    });
  }
  if (question.type === FieldType.DateRange) {
    return z.array(
      z.date({
        message: requiredError,
      }),
      { required_error: requiredError }
    );
  }
  if (question.type === FieldType.PhoneNumber) {
    return z.string({ required_error: requiredError }).regex(JP_PHONENUMBER_REGEX, {
      message: i18n?.t('numbersOnlyError'),
    });
  }
  if (question.type === FieldType.OpinionScale) {
    return z.object(
      {
        value: z.number(),
        maxScale: z.number(),
      },
      {
        required_error: requiredError,
      }
    );
  }
  if (question.type === FieldType.FileUploader) {
    return requiredRule
      ? z.array(z.object({}), { required_error: requiredError }).min(1, requiredError)
      : z.array(z.object({}));
  }
  if (question.type === FieldType.Rating) {
    return z.number({ required_error: requiredError });
  }
  return requiredRule
    ? z.string({ required_error: requiredError }).min(1, requiredError)
    : z.string();
};

const addDefaultMaxLengthValidator = (question: FormField, maxLength: number) => {
  const hasMaxLengthValidator = question.validators.some(
    (validator) => validator.type === ValidatorType.MaxLength
  );
  if (!hasMaxLengthValidator) {
    question.validators.push({
      type: ValidatorType.MaxLength,
      value: maxLength,
    });
  }

  return question;
};

const getQuestionSchema = (question) => {
  const requiredRule = question.validators?.find(
    (validator) => validator.type === 'required' && validator.value === true
  );
  const isOptional = !requiredRule && question.type !== FieldType.Legal;
  let schema: ZodTypeAny = getInitialSchema(question, requiredRule);

  if (question.type === FieldType.ShortQA) {
    addDefaultMaxLengthValidator(question, DEFAULT_MAX_LENGTH[FieldType.ShortQA]);
  }
  if (question.type === FieldType.LongQA) {
    addDefaultMaxLengthValidator(question, DEFAULT_MAX_LENGTH[FieldType.LongQA]);
  }

  question.validators?.forEach((validator) => {
    switch (validator.type) {
      case ValidatorType.Email:
        schema = (schema as ZodString).email({
          message: validator.message || i18n?.t('invalidEmailError'),
        });
        break;
      case ValidatorType.MinLength:
        schema = (schema as ZodString).min(validator.value, {
          message: validator.message || i18n?.t('minLengthError', { value: validator.value }),
        });
        break;
      case ValidatorType.MaxLength:
        schema = (schema as ZodString).max(validator.value, {
          message: validator.message || i18n?.t('maxLengthError', { value: validator.value }),
        });
        break;
      case ValidatorType.MinValue:
        schema = (schema as ZodString).min(validator.value, {
          message: validator.message || i18n?.t('minValueError', { value: validator.value }),
        });
        break;
      case ValidatorType.MaxValue:
        schema = (schema as ZodString).max(validator.value, {
          message: validator.message || i18n?.t('maxValueError', { value: validator.value }),
        });
        break;
      case ValidatorType.Pattern:
        schema = (schema as ZodString).regex(new RegExp(validator.value), {
          message: validator.message || i18n?.t('regexError'),
        });
        break;
      case ValidatorType.Hiragana:
        schema = (schema as ZodString).regex(/^[\u3040-\u309F]+$/, {
          message: validator.message || i18n?.t('hiraganaError'),
        });
        break;
      case ValidatorType.Katakana:
        schema = (schema as ZodString).regex(/^[\u30A0-\u30FF]+$/, {
          message: validator.message || i18n?.t('katakanaError'),
        });
        break;
    }
  });
  if (isOptional) {
    schema = z.union([z.literal(''), schema.optional()]);
  }

  return schema;
};

export const hasRequiredRule = (field: FormField) => {
  return field.validators?.some(
    (validator) => validator.type === 'required' && validator.value === true
  );
};

export const JAPAN_PREFECTURE_MAP: Record<string, string> = {
  1: '北海道',
  2: '青森県',
  3: '岩手県',
  4: '宮城県',
  5: '秋田県',
  6: '山形県',
  7: '福島県',
  8: '茨城県',
  9: '栃木県',
  10: '群馬県',
  11: '埼玉県',
  12: '千葉県',
  13: '東京都',
  14: '神奈川県',
  15: '新潟県',
  16: '富山県',
  17: '石川県',
  18: '福井県',
  19: '山梨県',
  20: '長野県',
  21: '岐阜県',
  22: '静岡県',
  23: '愛知県',
  24: '三重県',
  25: '滋賀県',
  26: '京都府',
  27: '大阪府',
  28: '兵庫県',
  29: '奈良県',
  30: '和歌山県',
  31: '鳥取県',
  32: '島根県',
  33: '岡山県',
  34: '広島県',
  35: '山口県',
  36: '徳島県',
  37: '香川県',
  38: '愛媛県',
  39: '高知県',
  40: '福岡県',
  41: '佐賀県',
  42: '長崎県',
  43: '熊本県',
  44: '大分県',
  45: '宮崎県',
  46: '鹿児島県',
  47: '沖縄県',
};

export const getPrefecturesOptions = () => {
  const prefectures: Record<'label', string>[] = [];
  for (const key in JAPAN_PREFECTURE_MAP) {
    prefectures.push({ label: JAPAN_PREFECTURE_MAP[key] });
  }
  return prefectures;
};

export const getPostalCodeData = async (postalCode) => {
  const response = await fetch(`https://zipcloud.ibsnet.co.jp/api/search?zipcode=${postalCode}`);
  const data = await response.json();
  if (data?.results) {
    return data.results[0];
  }
  return null;
};

export const getAddressFromPostalCode = async (postalCode) => {
  try {
    const result = await getPostalCodeData(postalCode);
    if (result) {
      const prefecture = JAPAN_PREFECTURE_MAP[result.prefcode];
      return {
        prefecture: prefecture,
        city: result.address2,
        street: result.address3,
      };
    }
  } catch (err) {
    console.error(err);
  }
  return { prefecture: '', address: '' };
};

export const hasRequiredFields = (fields) => {
  return fields?.some((field) => {
    if (field.isHide) return false;
    if (field.fields?.length) {
      return hasRequiredFields(field.fields);
    }
    return (
      field.validators?.some(
        (validator) => validator.type === 'required' && validator.value === true
      ) || field.type === FieldType.Legal
    );
  });
};

export const isExpiredForm = (expiredAt: string) => {
  return expiredAt ? new Date() > new Date(expiredAt) : false;
};

export const hasReachedSubmissionLimit = (formSettings: FormSettings) => {
  return (
    formSettings.setting.submission.limitResponse &&
    formSettings.responses >= formSettings.setting.submission.limitNumber
  );
};

export const isNotOpenForm = (startAt: string) => {
  return startAt ? new Date() < new Date(startAt) : false;
};

export const showErrorToast = (errorMessage: string) => {
  notifications.show({
    message: errorMessage,
    autoClose: false,
    styles: (theme) => ({
      root: {
        borderRadius: '10px !important',
        padding: `${theme.spacing.lg} !important`,
        backgroundColor: `${theme.colors.decaRed[0]} !important`,
        borderColor: `${theme.colors.decaRed[6]} !important`,
        '&::before': { display: 'none !important' },
      },
      description: {
        color: `${theme.colors.decaRed[9]} !important`,
        fontSize: '14px !important',
      },
      closeButton: {
        color: `${theme.colors.decaRed[9]} !important`,
        '&:hover': {
          backgroundColor: 'transparent !important',
          color: `${theme.colors.decaRed[7]} !important`,
        },
      },
    }),
  });
};

export const getOtherAnswerValue = (value: string) => {
  if (!value) return i18n?.t('otherOptionLabel');
  return `${i18n?.t('otherOptionLabel')}: ${value}`;
};

export const getAcceptTypes = (supportedTypes?: string[]) => {
  const acceptMap = {
    image: [MIME_TYPES.png, MIME_TYPES.gif, MIME_TYPES.jpeg],
    document: [
      MIME_TYPES.pdf,
      MIME_TYPES.xls,
      MIME_TYPES.xlsx,
      MIME_TYPES.docx,
      MIME_TYPES.doc,
      MIME_TYPES.csv,
      MIME_TYPES.ppt,
      MIME_TYPES.pptx,
      'text/plain',
      'application/vnd.oasis.opendocument.text',
      'application/vnd.oasis.opendocument.spreadsheet',
    ],
    media: [MIME_TYPES.mp4, 'audio/mpeg', 'video/mpeg'],
  };
  return supportedTypes?.reduce((acc, cur) => {
    return acc.concat(acceptMap[cur]);
  }, []);
};

export const getDefaultValues = (pages: FormSection[], useId = false): Record<string, any> => {
  const defaultValues: Record<string, any> = {};

  pages.forEach((page) => {
    page.content?.forEach((field) => {
      const setDefaultValue = (field) => {
        if ('id' in field) {
          const isPrefectureField = field.name === 'prefecture';
          if ('options' in field && Array.isArray(field.options) && !isPrefectureField) {
            const defaultCheckedOption = field.options.find(
              (option) => option.defaultCheck === true
            );
            if (defaultCheckedOption) {
              defaultValues[field.id] =
                field.type === FieldType.Checkbox
                  ? true
                  : useId
                    ? defaultCheckedOption.id
                    : defaultCheckedOption.label;
            }
          }

          if (field.type === FieldType.DateSelector && !field.inputDirectlyEnabled) {
            defaultValues[field.id] = dayjs().format('YYYY-MM-DD');
          }
        }
        if ('fields' in field && Array.isArray(field.fields)) {
          field.fields.forEach(setDefaultValue);
        }
      };

      setDefaultValue(field);
    });
  });

  return defaultValues;
};

export const isGroupType = (fieldType: FieldType | GroupFieldType) => {
  return [
    GroupFieldType.Name,
    GroupFieldType.Address,
    GroupFieldType.DateTimeRange,
    GroupFieldType.Section,
  ].includes(fieldType as GroupFieldType);
};

export const getUpdatedKey = (oldData: Record<string, any>, newData: Record<string, any>) => {
  const data = uniq([...Object.keys(oldData), ...Object.keys(newData)]);

  for (const key of data) {
    if (!isEqual(oldData[key], newData[key])) {
      return key;
    }
  }

  return null;
};
