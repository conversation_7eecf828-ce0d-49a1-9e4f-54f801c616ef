import { FormLayoutType, type FormSection } from '@/types/form-builder';
import ImageBackgroundLayout from './ImageBackgroundLayout';
import ImageLeftLayout from './ImageLeftLayout';
import ImageRightLayout from './ImageRightLayout';
import ImageTopLayout from './ImageTopLayout';
import ImageTopWithSpaceLayout from './ImageTopWithSpaceLayout';

export type FormLayoutProps = {
  index: number;
  settings: FormSection;
  showBackButton: boolean;
  showNextButton: boolean;
  showSubmitButton: boolean;
  showSkipButton: boolean;
  isSaving: boolean;
  backgroundTransparency?: string;
  onBack: () => void;
  onNext: () => void;
  onSubmit: () => void;
  onConfirm: () => void;
  renderFooter?: () => JSX.Element;
  activePageIndex?: number;
};
const LayoutMap = {
  [FormLayoutType.ImageTop]: ImageTopLayout,
  [FormLayoutType.ImageTopWithSpace]: ImageTopWithSpaceLayout,
  [FormLayoutType.ImageLeft]: ImageLeftLayout,
  [FormLayoutType.ImageRight]: ImageRightLayout,
  [FormLayoutType.ImageBackground]: ImageBackgroundLayout,
};

const FormLayout = (props: FormLayoutProps) => {
  const Component = LayoutMap[props.settings.layout.type];

  return <Component {...props} />;
};

export default FormLayout;
