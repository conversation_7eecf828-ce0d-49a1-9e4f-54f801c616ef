import { fireEvent, screen } from '@testing-library/react';
import { beforeEach, describe, expect, it, vi } from 'vitest';
import '@testing-library/jest-dom/vitest';
import ConfirmationStep from '@/components/ConfirmationStep/ConfirmationStep';
import { renderWithProviders } from '@/test-utils';
import { FieldType } from '@/types/form-builder';

// Mock FieldType enum and type
vi.mock('@/types/form-builder', async (importOriginal) => ({
  ...(await importOriginal()),
  FieldType: {
    Date: 'date',
    DateSelector: 'dateSelector',
    DateTime: 'dateTime',
    DateRange: 'dateRange',
    Rating: 'rating',
    OpinionScale: 'opinionScale',
    Checkbox: 'checkbox',
    FileUploader: 'file_uploader',
    Legal: 'legal',
  },
  GroupFieldType: {
    Name: 'name',
    Address: 'address',
    DateTimeRange: 'datetime_range',
  },
}));

vi.mock('@mantine/emotion', async (importOriginal) => ({
  ...(await importOriginal()),
  createStyles: () => () => ({
    classes: {
      questionTitle: 'questionTitle',
    },
  }),
}));

// Mock i18n translation
vi.mock('@tolgee/react', async (importOriginal) => ({
  ...(await importOriginal()),
  useTranslate: () => ({
    t: (key: string, opts?: Record<string, any>) =>
      key === 'common:page' || key === 'page' ? `Page ${opts?.page}` : key,
  }),
}));

// Mock DecaButton as a regular button
vi.mock('@/components/DecaButton', () => ({
  DecaButton: ({ children, onClick, ...rest }: any) => (
    <button onClick={onClick} {...rest}>
      {children}
    </button>
  ),
}));

// Mock RatingAnswer so we can assert props easily
vi.mock('@/components/ConfirmationStep/RatingAnswer', () => ({
  default: ({ shape, value }: { shape: string; value: number }) => (
    <span data-testid='rating-answer' data-shape={shape} data-value={value} />
  ),
}));

// Mock FilePreview to assert asset props
vi.mock('@/components/ConfirmationStep/FilePreview', () => ({
  default: ({ asset }: { asset: any }) => (
    <div data-testid='file-preview' data-asset-id={asset.id} data-asset-name={asset.name} />
  ),
}));

// Mock FormContext with a controllable values bag
let mockValues: Record<string, any> = {};
vi.mock('@/contexts/FormContext', async (importOriginal) => ({
  ...(await importOriginal()),
  useFormContext: () => ({ values: mockValues }),
}));

// Mock FormLogicContext with controllable visibilityState
let mockVisibilityState: Record<string, boolean> = {};
vi.mock('@/contexts/FormLogicContext', async (importOriginal) => ({
  ...(await importOriginal()),
  useFormLogicContext: () => ({ visibilityState: mockVisibilityState }),
}));

// Mock FormSettingsContext with controllable contentPages
let mockContentPages: any[] = [];
vi.mock('@/contexts/FormSettingsContext', async (importOriginal) => ({
  ...(await importOriginal()),
  useFormSettings: () => ({ contentPages: mockContentPages }),
}));

describe('ConfirmationStep', () => {
  beforeEach(() => {
    mockValues = {};
    mockContentPages = [];
    mockVisibilityState = {};
  });

  it('renders all fields with proper formatting and handles actions', () => {
    // Arrange: build a page with various field types
    const dateField = {
      id: 'f-date',
      type: FieldType.Date,
      label: '<strong>Date of birth</strong>',
      name: 'dob',
    };
    const dateSelectorField = {
      id: 'f-date-selector',
      type: FieldType.DateSelector,
      label: '<i>Select a date</i>',
      name: 'ds',
    };
    const dateTimeField = {
      id: 'f-datetime',
      type: FieldType.DateTime,
      label: 'Appointment time',
      name: 'dt',
    };
    const dateRangeField = {
      id: 'f-daterange',
      type: FieldType.DateRange,
      label: 'Period',
      name: 'dr',
    };
    const ratingField = {
      id: 'f-rating',
      type: FieldType.Rating,
      label: 'Satisfaction',
      name: 'rate',
      shape: 'star',
    };
    const opinionScaleField = {
      id: 'f-opinion',
      type: FieldType.OpinionScale,
      label: 'How likely?',
      name: 'ops',
    };
    const textField = {
      id: 'f-text',
      type: 'text',
      label: 'Comments',
      name: 'comments',
    };
    const checkboxField = {
      id: 'f-checkbox',
      type: FieldType.Checkbox,
      label: 'I agree to the terms',
      name: 'agree',
    };

    mockContentPages = [
      {
        id: 'page-1',
        name: 'Intro',
        content: [
          dateField,
          dateSelectorField,
          dateTimeField,
          dateRangeField,
          ratingField,
          opinionScaleField,
          textField,
          checkboxField,
        ],
      },
    ];

    mockValues = {
      [dateField.id]: '2025-01-02',
      [dateSelectorField.id]: '2025-01-03',
      [dateTimeField.id]: '2025-01-02T10:30',
      [dateRangeField.id]: ['2025-01-02T09:00', '2025-01-05T17:00'],
      [ratingField.id]: 3,
      [opinionScaleField.id]: { value: 7 },
      [textField.id]: 'Hello world',
      [checkboxField.id]: ['Option A', 'Option B'], // label should not render, answer will render as "Option A,Option B"
    };

    const onCancel = vi.fn();
    const onSubmit = vi.fn();

    // Act
    const { container } = renderWithProviders(
      <ConfirmationStep onCancel={onCancel} onSubmit={onSubmit} navigationHistory={[0]} />
    );

    // Assert: page title translation
    expect(screen.getByText('Page 1: Intro')).toBeInTheDocument();

    // Assert: labels inserted with dangerouslySetInnerHTML (non-checkbox only)
    const questionTitles = container.querySelectorAll('.questionTitle');
    expect(questionTitles.length).toBe(8);

    // Ensure the HTML label is intact for one example
    expect(questionTitles[0].innerHTML).toBe('<strong>Date of birth</strong>');
    expect(questionTitles[1].innerHTML).toBe('<i>Select a date</i>');

    // Assert: formatted answers
    expect(screen.getByText('2025/01/02')).toBeInTheDocument(); // Date
    expect(screen.getByText('2025/01/03')).toBeInTheDocument(); // DateSelector
    expect(screen.getByText('2025/01/02 10:30')).toBeInTheDocument(); // DateTime
    expect(screen.getByText('2025/01/02 09:00 - 2025/01/05 17:00')).toBeInTheDocument(); // DateRange

    // Rating: shows "value/max" and passes through to RatingAnswer
    const ratingAnswer = screen.getByTestId('rating-answer');
    expect(ratingAnswer).toHaveAttribute('data-shape', 'star');

    // OpinionScale: renders numeric value
    expect(screen.getByText('7')).toBeInTheDocument();

    // Default (text)
    expect(screen.getByText('Hello world')).toBeInTheDocument();

    // Checkbox answer rendered as array (comma-joined by React to string)
    // Buttons call handlers
    fireEvent.click(screen.getByRole('button', { name: /back/i }));
    expect(onCancel).toHaveBeenCalledTimes(1);

    fireEvent.click(screen.getByRole('button', { name: /submit/i }));
    expect(onSubmit).toHaveBeenCalledTimes(1);
  });

  it('renders gracefully with no pages', () => {
    mockContentPages = [];
    mockValues = {};

    const { container } = renderWithProviders(
      <ConfirmationStep onCancel={() => {}} onSubmit={() => {}} navigationHistory={[0]} />
    );

    // No page titles or content
    expect(container.querySelector('.questionTitle')).toBeNull();
    expect(screen.queryByText(/Page \d+:/)).not.toBeInTheDocument();
  });

  it('handles FileUploader field type correctly', () => {
    const fileUploaderField = {
      id: 'f-file-upload',
      type: 'file_uploader',
      label: 'Upload documents',
      name: 'documents',
    };

    mockContentPages = [
      {
        id: 'page-1',
        name: 'Files',
        content: [fileUploaderField],
      },
    ];

    mockValues = {
      [fileUploaderField.id]: [
        { asset: { id: 'asset-1', name: 'document.pdf' } },
        { asset: { id: 'asset-2', name: 'image.jpg' } },
      ],
    };

    renderWithProviders(
      <ConfirmationStep onCancel={() => {}} onSubmit={() => {}} navigationHistory={[0]} />
    );

    expect(screen.getByText('Upload documents')).toBeInTheDocument();

    const filePreviews = screen.getAllByTestId('file-preview');
    expect(filePreviews).toHaveLength(2);
    expect(filePreviews[0]).toHaveAttribute('data-asset-id', 'asset-1');
    expect(filePreviews[0]).toHaveAttribute('data-asset-name', 'document.pdf');
    expect(filePreviews[1]).toHaveAttribute('data-asset-id', 'asset-2');
    expect(filePreviews[1]).toHaveAttribute('data-asset-name', 'image.jpg');
  });

  it('handles Legal field type correctly', () => {
    const legalField = {
      id: 'f-legal',
      type: 'legal',
      label: 'Terms and Conditions',
      name: 'terms',
    };

    mockContentPages = [
      {
        id: 'page-1',
        name: 'Legal',
        content: [legalField],
      },
    ];

    mockValues = {
      [legalField.id]: [{ value: 'I agree to the terms and conditions' }],
    };

    renderWithProviders(
      <ConfirmationStep onCancel={() => {}} onSubmit={() => {}} navigationHistory={[0]} />
    );

    expect(screen.getByText('Terms and Conditions')).toBeInTheDocument();
    expect(screen.getByText('I agree to the terms and conditions')).toBeInTheDocument();
  });

  it('handles rating field with custom maxScale and different shapes', () => {
    const ratingField = {
      id: 'f-rating',
      type: 'rating',
      label: 'Rate this service',
      name: 'rating',
      maxScale: 5,
      shape: 'heart',
    };

    mockContentPages = [
      {
        id: 'page-1',
        name: 'Rating',
        content: [ratingField],
      },
    ];

    mockValues = {
      [ratingField.id]: 4,
    };

    renderWithProviders(
      <ConfirmationStep onCancel={() => {}} onSubmit={() => {}} navigationHistory={[0]} />
    );

    expect(screen.getByText('Rate this service')).toBeInTheDocument();
    expect(screen.getByText('4/5')).toBeInTheDocument();

    const ratingAnswer = screen.getByTestId('rating-answer');
    expect(ratingAnswer).toHaveAttribute('data-shape', 'heart');
  });

  it('handles rating field with default maxScale when not specified', () => {
    const ratingField = {
      id: 'f-rating',
      type: 'rating',
      label: 'Rate overall',
      name: 'overall_rating',
      shape: 'star',
    };

    mockContentPages = [
      {
        id: 'page-1',
        name: 'Rating',
        content: [ratingField],
      },
    ];

    mockValues = {
      [ratingField.id]: 7,
    };

    renderWithProviders(
      <ConfirmationStep onCancel={() => {}} onSubmit={() => {}} navigationHistory={[0]} />
    );

    // Default maxScale should be 10
    expect(screen.getByText('7/10')).toBeInTheDocument();
  });

  it('handles fields with null or undefined answers by showing blank translation', () => {
    const textField = {
      id: 'f-text',
      type: 'text',
      label: 'Optional comment',
      name: 'comment',
    };

    mockContentPages = [
      {
        id: 'page-1',
        name: 'Optional',
        content: [textField],
      },
    ];

    mockValues = {
      [textField.id]: null,
    };

    renderWithProviders(
      <ConfirmationStep onCancel={() => {}} onSubmit={() => {}} navigationHistory={[0]} />
    );

    expect(screen.getByText('Optional comment')).toBeInTheDocument();
    expect(screen.getByText('blank')).toBeInTheDocument();
  });

  it('handles array answers without value property (fallback to String conversion)', () => {
    const customField = {
      id: 'f-custom',
      type: 'custom',
      label: 'Custom field',
      name: 'custom',
    };

    mockContentPages = [
      {
        id: 'page-1',
        name: 'Custom',
        content: [customField],
      },
    ];

    // Simulate an answer that's neither an object with value nor has special handling
    mockValues = {
      [customField.id]: 42,
    };

    renderWithProviders(
      <ConfirmationStep onCancel={() => {}} onSubmit={() => {}} navigationHistory={[0]} />
    );

    expect(screen.getByText('Custom field')).toBeInTheDocument();
    expect(screen.getByText('42')).toBeInTheDocument();
  });

  it('handles array answers with value property correctly', () => {
    const multiSelectField = {
      id: 'f-multiselect',
      type: 'multiselect',
      label: 'Select multiple options',
      name: 'options',
    };

    mockContentPages = [
      {
        id: 'page-1',
        name: 'Multi',
        content: [multiSelectField],
      },
    ];

    mockValues = {
      [multiSelectField.id]: [{ value: 'Option A' }, { value: 'Option B' }, { value: 'Option C' }],
    };

    renderWithProviders(
      <ConfirmationStep onCancel={() => {}} onSubmit={() => {}} navigationHistory={[0]} />
    );

    expect(screen.getByText('Select multiple options')).toBeInTheDocument();
    expect(screen.getByText('Option A, Option B, Option C')).toBeInTheDocument();
  });

  it('handles visibilityState correctly - hides fields when visibility is false', () => {
    const visibleField = {
      id: 'f-visible',
      type: 'text',
      label: 'Visible field',
      name: 'visible',
    };

    const hiddenField = {
      id: 'f-hidden',
      type: 'text',
      label: 'Hidden field',
      name: 'hidden',
    };

    mockContentPages = [
      {
        id: 'page-1',
        name: 'Visibility Test',
        content: [visibleField, hiddenField],
      },
    ];

    mockValues = {
      [visibleField.id]: 'Visible answer',
      [hiddenField.id]: 'Hidden answer',
    };

    mockVisibilityState = {
      [visibleField.id]: true,
      [hiddenField.id]: false,
    };

    renderWithProviders(
      <ConfirmationStep onCancel={() => {}} onSubmit={() => {}} navigationHistory={[0]} />
    );

    // Visible field should appear
    expect(screen.getByText('Visible field')).toBeInTheDocument();
    expect(screen.getByText('Visible answer')).toBeInTheDocument();

    // Hidden field should not appear
    expect(screen.queryByText('Hidden field')).not.toBeInTheDocument();
    expect(screen.queryByText('Hidden answer')).not.toBeInTheDocument();
  });

  it('handles GroupFieldType.Name correctly by rendering nested fields', () => {
    const nameGroupField = {
      id: 'f-name-group',
      type: 'name',
      groupType: 'name',
      label: 'Full Name',
      fields: [
        {
          id: 'f-first-name',
          type: 'text',
          label: 'First Name',
          name: 'firstName',
        },
        {
          id: 'f-last-name',
          type: 'text',
          label: 'Last Name',
          name: 'lastName',
        },
      ],
    };

    mockContentPages = [
      {
        id: 'page-1',
        name: 'Personal Info',
        content: [nameGroupField],
      },
    ];

    mockValues = {
      'f-first-name': 'John',
      'f-last-name': 'Doe',
    };

    renderWithProviders(
      <ConfirmationStep onCancel={() => {}} onSubmit={() => {}} navigationHistory={[0]} />
    );

    expect(screen.getByText('First Name')).toBeInTheDocument();
    expect(screen.getByText('John')).toBeInTheDocument();
    expect(screen.getByText('Last Name')).toBeInTheDocument();
    expect(screen.getByText('Doe')).toBeInTheDocument();
  });

  it('handles GroupFieldType.Address correctly by rendering nested fields', () => {
    const addressGroupField = {
      id: 'f-address-group',
      type: 'address',
      groupType: 'address',
      label: 'Address',
      fields: [
        {
          id: 'f-street',
          type: 'text',
          label: 'Street',
          name: 'street',
        },
        {
          id: 'f-city',
          type: 'text',
          label: 'City',
          name: 'city',
        },
      ],
    };

    mockContentPages = [
      {
        id: 'page-1',
        name: 'Location',
        content: [addressGroupField],
      },
    ];

    mockValues = {
      'f-street': '123 Main St',
      'f-city': 'Springfield',
    };

    renderWithProviders(
      <ConfirmationStep onCancel={() => {}} onSubmit={() => {}} navigationHistory={[0]} />
    );

    expect(screen.getByText('Street')).toBeInTheDocument();
    expect(screen.getByText('123 Main St')).toBeInTheDocument();
    expect(screen.getByText('City')).toBeInTheDocument();
    expect(screen.getByText('Springfield')).toBeInTheDocument();
  });

  it('handles GroupFieldType.DateTimeRange correctly by rendering nested fields', () => {
    const dateTimeRangeGroupField = {
      id: 'f-datetime-range-group',
      type: 'datetime_range',
      groupType: 'datetime_range',
      label: 'Event Duration',
      fields: [
        {
          id: 'f-start-date',
          type: 'dateTime',
          label: 'Start Date',
          name: 'startDate',
        },
        {
          id: 'f-end-date',
          type: 'dateTime',
          label: 'End Date',
          name: 'endDate',
        },
      ],
    };

    mockContentPages = [
      {
        id: 'page-1',
        name: 'Schedule',
        content: [dateTimeRangeGroupField],
      },
    ];

    mockValues = {
      'f-start-date': '2025-01-01T09:00',
      'f-end-date': '2025-01-01T17:00',
    };

    renderWithProviders(
      <ConfirmationStep onCancel={() => {}} onSubmit={() => {}} navigationHistory={[0]} />
    );

    expect(screen.getByText('Start Date')).toBeInTheDocument();
    expect(screen.getByText('2025/01/01 09:00')).toBeInTheDocument();
    expect(screen.getByText('End Date')).toBeInTheDocument();
    expect(screen.getByText('2025/01/01 17:00')).toBeInTheDocument();
  });

  it('handles mixed visible and hidden fields in group fields', () => {
    const nameGroupField = {
      id: 'f-name-group',
      type: 'name',
      groupType: 'name',
      label: 'Full Name',
      fields: [
        {
          id: 'f-first-name',
          type: 'text',
          label: 'First Name',
          name: 'firstName',
        },
        {
          id: 'f-middle-name',
          type: 'text',
          label: 'Middle Name',
          name: 'middleName',
        },
        {
          id: 'f-last-name',
          type: 'text',
          label: 'Last Name',
          name: 'lastName',
        },
      ],
    };

    mockContentPages = [
      {
        id: 'page-1',
        name: 'Name',
        content: [nameGroupField],
      },
    ];

    mockValues = {
      'f-first-name': 'John',
      'f-middle-name': 'William',
      'f-last-name': 'Doe',
    };

    mockVisibilityState = {
      'f-first-name': true,
      'f-middle-name': false, // Hidden
      'f-last-name': true,
    };

    renderWithProviders(
      <ConfirmationStep onCancel={() => {}} onSubmit={() => {}} navigationHistory={[0]} />
    );

    // Visible fields should appear
    expect(screen.getByText('First Name')).toBeInTheDocument();
    expect(screen.getByText('John')).toBeInTheDocument();
    expect(screen.getByText('Last Name')).toBeInTheDocument();
    expect(screen.getByText('Doe')).toBeInTheDocument();

    // Hidden field should not appear
    expect(screen.queryByText('Middle Name')).not.toBeInTheDocument();
    expect(screen.queryByText('William')).not.toBeInTheDocument();
  });

  it('handles multiple pages with mixed field types', () => {
    const page1Fields = [
      {
        id: 'f-name',
        type: 'text',
        label: 'Name',
        name: 'name',
      },
    ];

    const page2Fields = [
      {
        id: 'f-email',
        type: 'email',
        label: 'Email',
        name: 'email',
      },
      {
        id: 'f-rating',
        type: 'rating',
        label: 'Satisfaction',
        name: 'rating',
        shape: 'star',
        maxScale: 5,
      },
    ];

    mockContentPages = [
      {
        id: 'page-1',
        name: 'Personal Info',
        content: page1Fields,
      },
      {
        id: 'page-2',
        name: 'Feedback',
        content: page2Fields,
      },
    ];

    mockValues = {
      'f-name': 'John Doe',
      'f-email': '<EMAIL>',
      'f-rating': 5,
    };

    renderWithProviders(
      <ConfirmationStep onCancel={() => {}} onSubmit={() => {}} navigationHistory={[0, 1]} />
    );

    // Page 1 content
    expect(screen.getByText('Page 1: Personal Info')).toBeInTheDocument();
    expect(screen.getByText('Name')).toBeInTheDocument();
    expect(screen.getByText('John Doe')).toBeInTheDocument();

    // Page 2 content
    expect(screen.getByText('Page 2: Feedback')).toBeInTheDocument();
    expect(screen.getByText('Email')).toBeInTheDocument();
    expect(screen.getByText('<EMAIL>')).toBeInTheDocument();
    expect(screen.getByText('Satisfaction')).toBeInTheDocument();
    expect(screen.getByText('5/5')).toBeInTheDocument();
  });
});
