import ConfirmationStep from '@/components/ConfirmationStep/ConfirmationStep';
import SubmittingNotice from '@/components/SubmittingNotice/SubmittingNotice';
/* eslint-disable no-unused-vars */
import { AppContextProvider } from '@/contexts/AppContext';
import { useFormLogicContext } from '@/contexts/FormLogicContext';
import { FormSettingsProvider } from '@/contexts/FormSettingsContext';
import { useLiff } from '@/contexts/LiffContext';
import { useSubmitForm } from '@/hooks/form/useSubmitForm';
import useFormValidation from '@/hooks/useFormValidation';
import { usePageNavigation } from '@/hooks/usePageNavigation';
import {
  FieldType,
  type FormField,
  type FormSection,
  type FormSettings,
  type GroupFormField,
} from '@/types/form-builder';
import { __ENDING_PAGE_ID__ } from '@/types/page-logic';
import {
  camelToSnake,
  getDefaultValues,
  getUpdated<PERSON>ey,
  hasRequired<PERSON>ields,
  shouldShowHeader,
  showErrorToast,
} from '@/utils';
import { APIError } from '@/utils/error';
import { Box, Flex, ScrollArea } from '@mantine/core';
import { createStyles } from '@mantine/emotion';
import { type FormErrors, zodResolver } from '@mantine/form';
import { Notifications } from '@mantine/notifications';
import { useTranslate as useTranslation } from '@tolgee/react';
import { isEmpty } from 'lodash';
import { useRouter } from 'next/router';
import { useEffect, useRef, useState } from 'react';
import { FormProvider, useForm } from '../../contexts/FormContext';
import HiddenField from '../FormFields/HiddenField';
import Footer from '../FormHeader/Footer';
import Header from '../FormHeader/Header';
import FormLayout from '../FormLayout/FormLayout';
import Meta from '../Meta/Meta';

const useStyles = createStyles(
  (
    theme,
    {
      is2ColumnsLayout,
      backgroundTransparency = '1',
    }: { is2ColumnsLayout: boolean; backgroundTransparency?: string }
  ) => ({
    root: {
      backgroundColor: `rgba(255, 255, 255, ${backgroundTransparency});`,
      height: '100%',
      '@media (min-width: 1000px)': {
        height: is2ColumnsLayout ? '100vh' : '100%',
      },
    },
    formLayout: {
      flex: 1,
      [`@media (min-width: ${theme.breakpoints.sm})`]: {
        overflow: is2ColumnsLayout ? 'hidden' : 'initial',
      },
    },
    fullHeight: {
      height: '100%',
    },
    scrollArea: {
      '.mantine-ScrollArea-viewport > div': {
        height: '100%',
        width: '100%',
        tableLayout: 'fixed',
      },
    },
  })
);

interface FormContainerProps {
  id: string;
  formSettings: FormSettings;
  pages: FormSection[];
  preview?: boolean;
  inputWidth?: string;
  backgroundTransparency?: string;
  enableHeader?: boolean;
  enableFooter?: boolean;
  activePageIndex?: number;
  onChangePage?: (page: number) => void;
  onSizeChange?: (size: { height: number }) => void;
  onSubmit?: () => void;
}

const FormContainer = ({
  id,
  formSettings,
  pages,
  preview,
  backgroundTransparency,
  activePageIndex = 0,
  enableHeader = true,
  enableFooter = true,
  onChangePage,
  onSizeChange,
  onSubmit,
}: FormContainerProps) => {
  const { t } = useTranslation('common');
  const router = useRouter();
  const [activeIndex, setActiveIndex] = useState(activePageIndex);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isConfirming, setIsConfirming] = useState(false);
  const isSubmittingRef = useRef<boolean>(false);
  // Track navigation history for proper back navigation
  const [navigationHistory, setNavigationHistory] = useState<number[]>([activePageIndex]);
  const { submitForm } = useSubmitForm();
  const { liff } = useLiff();
  const scrollRef = useRef<HTMLDivElement>(null);
  const contentBoxRef = useRef<HTMLDivElement>(null);
  const { visibilityState, updateFieldVisibility } = useFormLogicContext();

  const headerSettings = formSettings.appearance?.headerStyle;
  const footerSettings = formSettings.appearance?.footerStyle;
  const submissionSettings = formSettings?.setting?.submission;
  const hiddenFields = pages.find((page) => page.type === FieldType.Hidden)?.content as FormField[];
  const contentPages = pages.filter((page) => page.type !== FieldType.Hidden);
  const { getNextPageId, getPreviousPageId } = usePageNavigation({ pages: contentPages });
  const currentLayout = contentPages?.[activeIndex]?.layout.type;
  const is2ColumnsLayout = ['image-left', 'image-right'].includes(currentLayout) && !isConfirming;
  const { classes } = useStyles({
    is2ColumnsLayout,
    backgroundTransparency: backgroundTransparency,
  });
  const showSkipButton =
    !hasRequiredFields(contentPages[activeIndex]?.content) && activeIndex < contentPages.length - 1;
  const showHeader = enableHeader && shouldShowHeader(headerSettings);
  const showFooter = enableFooter && shouldShowHeader(footerSettings);
  const showOutsideFooter = showFooter && !is2ColumnsLayout;
  const showInsideFooter = showFooter && is2ColumnsLayout;

  const questions = contentPages[activeIndex].content || [];
  const validationSchema = useFormValidation(questions);
  const form = useForm({
    validateInputOnChange: true,
    initialValues: getDefaultValues(pages),
    validate: (values: Record<string, any>) => {
      const schema = validationSchema;
      return zodResolver(schema)(values);
    },
    transformValues: (values) => transformToAPIPayload(values),
    onValuesChange: (values: Record<string, any>, previous: Record<string, any>) => {
      // Should update field visibility when form values change
      const updatedKey = getUpdatedKey(previous, values);
      if (updatedKey) {
        updateFieldVisibility(updatedKey, values[updatedKey]);
      }
    },
  });

  useEffect(() => {
    if (isConfirming && contentBoxRef.current) {
      contentBoxRef.current.scrollTop = 0;
    }
  }, [isConfirming]);

  useEffect(() => {
    setNavigationHistory([activePageIndex]);
  }, [activePageIndex]);

  useEffect(() => {
    const contentContainer = document.getElementById(`content_container_${activeIndex}`);
    if (contentContainer) {
      const headerHeight = document.getElementById('header')?.offsetHeight || 0;
      const footerHeight = document.getElementById('footer')?.offsetHeight || 0;
      const pageHeight = contentContainer.scrollHeight + headerHeight + footerHeight;
      onSizeChange?.({
        height: pageHeight,
      });
    }
  }, [activeIndex, onSizeChange]);

  useEffect(() => {
    scrollRef.current!.scrollTo({ top: 0, behavior: 'smooth' });
  }, [activeIndex]);

  const prevStep = () => {
    // Use navigation history for proper back navigation
    if (navigationHistory.length > 1) {
      const newHistory = [...navigationHistory];
      newHistory.pop(); // Remove current page
      const previousIndex = newHistory[newHistory.length - 1]; // Get the previous page from history

      setNavigationHistory(newHistory);
      setActiveIndex(previousIndex);
      onChangePage?.(previousIndex);
    } else if (activeIndex > 0) {
      // If no navigation history but we're not on the first page, go to previous page
      const previousIndex = activeIndex - 1;
      setActiveIndex(previousIndex);
      onChangePage?.(previousIndex);
    }
  };

  const nextStep = () => {
    const { errors, hasErrors } = form.validate();
    if (hasErrors) {
      handleValidationFailure(errors);
      return;
    }

    const currentPage = contentPages[activeIndex];
    if (!currentPage) return;

    // Use conditional navigation logic
    const nextPageId = getNextPageId(currentPage.id, form.values as Record<string, any>);

    // Check if we've reached the end of the form
    if (nextPageId === __ENDING_PAGE_ID__) {
      // End of form - stay on current page (submit button will be shown)
      return;
    }

    // Find the next page by ID and navigate to it
    const nextIndex = contentPages.findIndex((page) => page.id === nextPageId);
    if (nextIndex !== -1 && nextIndex !== activeIndex) {
      // Add the new page to navigation history
      setNavigationHistory((prev) => [...prev, nextIndex]);
      setActiveIndex(nextIndex);
      onChangePage?.(nextIndex);
    }
  };

  const handleSubmit = async (formValues) => {
    if (preview || isSubmittingRef.current) return;
    isSubmittingRef.current = true;
    setIsSubmitting(true);
    try {
      if (liff) {
        formValues.integration = {
          id: router.query.integration_id,
          data: {
            access_token: liff.getAccessToken(),
          },
        };
      }

      const invisiblePages = contentPages
        .map((page, index) => (navigationHistory.includes(index) ? null : page.id))
        .filter(Boolean);
      const response = await submitForm({ ...formValues, invisible_pages: invisiblePages });
      if (response) {
        handleSubmitSuccess(response);
      }
    } catch (error) {
      setIsSubmitting(false);
      isSubmittingRef.current = false;
      handleSubmitError(error);
    }
  };

  const transformToAPIPayload = (formValues) => {
    const questionPageIdMap = generateQuestionPageIdMap();
    const visibledPages = navigationHistory.map((index) => contentPages[index].id);

    // Should filter out the answers that are not visible
    const answers: Record<string, any>[] = Object.keys(visibilityState).reduce((acc, key) => {
      if (
        questionPageIdMap[key] &&
        visibledPages.includes(questionPageIdMap[key]) &&
        !visibilityState[key]
      ) {
        const _answer: Record<string, any> = {
          question_id: key,
          page_id: questionPageIdMap[key],
          is_visible: visibilityState[key],
          values: [],
        };
        return [...acc, _answer];
      }
      return acc;
    }, []);

    // Should add the answers that are visible
    Object.keys(formValues).forEach((key) => {
      if (!visibledPages.includes(questionPageIdMap[key])) return;
      const _answer: Record<string, any> = {
        question_id: key,
        page_id: questionPageIdMap[key],
        is_visible: visibilityState[key] !== false,
        values: [],
      };

      if (!(Array.isArray(formValues[key]) && isEmpty(formValues[key]))) {
        _answer.values = (Array.isArray(formValues[key]) ? formValues[key] : [formValues[key]]).map(
          (rawValue) => transformValue(rawValue, key)
        );
      }
      answers.push({
        ..._answer,
      });
    });

    return {
      form_id: id,
      answers: answers,
    };
  };

  const generateQuestionPageIdMap = () => {
    const questionPageIdMap = {};
    const setQuestionValidation = (questions, pageId) => {
      questions.forEach((question) => {
        if ((question as GroupFormField).fields?.length) {
          setQuestionValidation(question.fields, pageId);
        } else {
          questionPageIdMap[question.id] = pageId;
        }
      });
    };

    pages.forEach((section) => {
      setQuestionValidation(section.content, section.id);
    });

    return questionPageIdMap;
  };

  const transformValue = (rawValue, questionId: string) => {
    const isOther = rawValue?.is_other ?? false;
    let value = rawValue?.value ?? rawValue;
    if (value instanceof Date) {
      value = value.toISOString();
    }
    const question = contentPages[activeIndex].content.find(
      (question) => question.id === questionId
    );
    if (question && question.type === FieldType.DateSelector && value) {
      const parts = value.split('-').map(Number);
      const year = parts[0];
      const month = parts[1];
      const day = parts[2];
      const date = new Date(year, month - 1, day);
      value = new Date(date).toISOString();
    }

    if (value?.asset) {
      return {
        value: value.asset.name,
        asset: camelToSnake(value.asset),
      };
    }
    const resultValue: any = {
      value: value,
      is_other: isOther,
    };
    if (rawValue?.maxScale) {
      resultValue.maxScale = rawValue.maxScale;
    }
    return resultValue;
  };

  const handleSubmitSuccess = (response) => {
    if (submissionSettings.mode === 'redirect') {
      window.location.href = submissionSettings.redirectUrl;
      return;
    }
    if (['message', 'message_redirect'].includes(submissionSettings.mode)) {
      localStorage.setItem('response_id', response.id);
      router.push(`/${id}/success`);
      router.events.on('routeChangeComplete', () => {
        onSubmit?.();
      });
    } else {
      onSubmit?.();
    }
  };

  const handleSubmitError = (error: Error) => {
    if (error instanceof APIError) {
      const { msg, type } = error;
      let errorMessage: string;

      switch (type) {
        case 'form:question_response_max_length_exceeded':
          errorMessage = t('questionResponseMaxLengthExceededErrorMessage');
          break;
        default:
          errorMessage = msg?.includes('limit reached')
            ? t('limitErrorMessage')
            : t('submitErrorMessage');
      }

      showErrorToast(errorMessage);
    }
  };

  const handleValidationFailure = (errors: FormErrors) => {
    const firstErrorPath = Object.keys(errors)[0];
    const inputElm = form.getInputNode(firstErrorPath);
    if (inputElm) {
      inputElm.scrollIntoView();
      inputElm.focus();
    }
  };

  return (
    <>
      <Meta
        url=''
        title={formSettings.name}
        description={formSettings.description}
        image={formSettings.screenshot.preview || formSettings.screenshot.thumbnail}
      />
      <Notifications position='top-right' />
      <ScrollArea h={'100%'} type='scroll' viewportRef={scrollRef} className={classes.scrollArea}>
        <AppContextProvider>
          <FormSettingsProvider
            contentPages={contentPages}
            formSettings={formSettings}
            hiddenFields={hiddenFields}
          >
            {isSubmitting ? (
              <SubmittingNotice />
            ) : (
              <Flex direction='column' className={classes.root}>
                {showHeader && <Header {...headerSettings} />}
                <Box className={classes.formLayout} ref={contentBoxRef}>
                  <FormProvider form={form}>
                    <form
                      className={classes.fullHeight}
                      onKeyDown={(e) => {
                        if (e.key === 'Enter' && !(e.target as HTMLElement).closest('textarea')) {
                          e.preventDefault();
                        }
                      }}
                    >
                      {hiddenFields?.map((field) => (
                        <HiddenField key={field.id} field={field as FormField} />
                      ))}
                      {isConfirming ? (
                        <ConfirmationStep
                          onCancel={() => {
                            setIsConfirming(false);
                          }}
                          onSubmit={form.onSubmit(handleSubmit, handleValidationFailure)}
                          navigationHistory={navigationHistory}
                        />
                      ) : (
                        contentPages.map((page, index) => (
                          <Box hidden={index !== activeIndex} key={index} h='100%'>
                            <FormLayout
                              index={index}
                              settings={page}
                              isSaving={isSubmitting}
                              backgroundTransparency={backgroundTransparency}
                              onNext={nextStep}
                              onBack={prevStep}
                              onSubmit={form.onSubmit(handleSubmit, handleValidationFailure)}
                              showBackButton={index > 0}
                              showNextButton={index < contentPages.length - 1}
                              showSubmitButton={index === contentPages.length - 1}
                              showSkipButton={showSkipButton}
                              renderFooter={() =>
                                showInsideFooter && <Footer {...footerSettings} />
                              }
                              onConfirm={() => setIsConfirming(true)}
                              activePageIndex={activeIndex}
                            />
                          </Box>
                        ))
                      )}
                    </form>
                  </FormProvider>
                </Box>
                {showOutsideFooter && <Footer {...footerSettings} />}
              </Flex>
            )}
          </FormSettingsProvider>
        </AppContextProvider>
      </ScrollArea>
    </>
  );
};

export default FormContainer;
