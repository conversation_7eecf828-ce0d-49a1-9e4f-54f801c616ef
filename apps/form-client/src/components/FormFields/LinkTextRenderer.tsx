import { Popover, Text, useMantineTheme } from '@mantine/core';
import { IconX } from '@tabler/icons-react';
import { useTranslate as useTranslation } from '@tolgee/react';
import { useCallback } from 'react';
import { decodeHtmlEntities } from '../../utils/common';

interface LinkTextRendererProps {
  text: string;
  fieldId: string;
  clickedLinks: Set<string>;
  showPopoverForLinks: Set<string>;
  processQuestionText: (text: string) => string;
  parseAttributes: (attrs: string) => Record<string, string>;
  handleLinkClick: (linkId: string) => void;
  setShowPopoverForLinks: (updater: (prev: Set<string>) => Set<string>) => void;
  questionTextClass: string;
  popoverClasses: {
    errorPopover: string;
    popoverArrow: string;
    popoverContent: string;
    popoverText: string;
  };
}

const LinkTextRenderer = ({
  text,
  fieldId,
  clickedLinks,
  showPopoverForLinks,
  processQuestionText,
  parseAttributes,
  handleLinkClick,
  setShowPopoverForLinks,
  questionTextClass,
  popoverClasses,
}: LinkTextRendererProps) => {
  const { t } = useTranslation('common');
  const theme = useMantineTheme();

  const renderLinkWithPopover = useCallback(
    (linkId: string, linkElement: JSX.Element) => {
      const isLinkClicked = clickedLinks.has(linkId);
      const shouldShowPopover = showPopoverForLinks.has(linkId) && !isLinkClicked;

      return (
        <Popover
          key={linkId}
          opened={shouldShowPopover}
          position='top'
          withArrow
          shadow='md'
          radius='md'
          withinPortal
          classNames={{
            dropdown: popoverClasses.errorPopover,
            arrow: popoverClasses.popoverArrow,
          }}
          onClose={() => {
            setShowPopoverForLinks((prev) => {
              const newSet = new Set(prev);
              newSet.delete(linkId);
              return newSet;
            });
          }}
        >
          <Popover.Target>
            <span style={{ display: 'inline' }}>{linkElement}</span>
          </Popover.Target>
          <Popover.Dropdown>
            <div className={popoverClasses.popoverContent}>
              <Text size='sm' c='decaRed.6' className={popoverClasses.popoverText}>
                {t('pleaseReviewLinkedDocumentsToEnableConsent', { ns: 'form_builder' })}
              </Text>
              <IconX
                size={16}
                cursor='pointer'
                color={theme.colors.decaRed[6]}
                onClick={() => {
                  setShowPopoverForLinks((prev) => {
                    const newSet = new Set(prev);
                    newSet.delete(linkId);
                    return newSet;
                  });
                }}
              />
            </div>
          </Popover.Dropdown>
        </Popover>
      );
    },
    [
      clickedLinks,
      showPopoverForLinks,
      popoverClasses,
      t,
      theme.colors.decaRed,
      setShowPopoverForLinks,
    ]
  );

  const renderTextWithPopovers = useCallback(() => {
    const processedText = processQuestionText(decodeHtmlEntities(text));
    const elements: JSX.Element[] = [];
    let lastIndex = 0;

    const linkRegex = /<a([^>]+href=["']([^"']+)["'][^>]*)>([^<]+)<\/a>/g;
    let match: RegExpExecArray | null;

    match = linkRegex.exec(processedText);
    while (match !== null) {
      const [fullMatch, attrs, url, linkText] = match;
      const linkId = `${fieldId}_${url}`;

      if (match.index > lastIndex) {
        const textBefore = processedText.slice(lastIndex, match.index);
        if (textBefore) {
          elements.push(
            <span key={`text-${lastIndex}`}>{textBefore.replace(/<[^>]*>/g, '')}</span>
          );
        }
      }

      const linkElement = (
        <a
          key={linkId}
          {...parseAttributes(attrs)}
          // biome-ignore lint/a11y/useValidAnchor: This is an external link, not a button
          onClick={() => handleLinkClick(linkId)}
          target='_blank'
          rel='noopener noreferrer'
          data-link-id={linkId}
          className='legal-link'
        >
          {linkText}
        </a>
      );

      elements.push(renderLinkWithPopover(linkId, linkElement));

      lastIndex = match.index + fullMatch.length;
      match = linkRegex.exec(processedText);
    }

    if (lastIndex < processedText.length) {
      const remainingText = processedText.slice(lastIndex);
      if (remainingText) {
        elements.push(
          <span key={`text-${lastIndex}`}>{remainingText.replace(/<[^>]*>/g, '')}</span>
        );
      }
    }

    return <div className={questionTextClass}>{elements}</div>;
  }, [
    text,
    fieldId,
    processQuestionText,
    handleLinkClick,
    renderLinkWithPopover,
    parseAttributes,
    questionTextClass,
  ]);

  return renderTextWithPopovers();
};

export default LinkTextRenderer;
