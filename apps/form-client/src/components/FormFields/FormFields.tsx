import { useFormLogicContext } from '@/contexts/FormLogicContext';
import type { FormDataContent, FormField, GroupFormField } from '@/types/form-builder';
import { getGridColsByWidth, isGroupType } from '@/utils';
import { Grid } from '@mantine/core';
import FormFieldWithLogic from './FormFieldWithLogic';
import GroupFieldWithLogic from './GroupFieldWithLogic';

interface FormFieldsProps {
  fields: FormDataContent[];
  activePageIndex?: number;
}

const FormFields = ({ fields, activePageIndex }: FormFieldsProps) => {
  const { isFieldVisible } = useFormLogicContext();

  return (
    <Grid gutter={'xl'}>
      {fields.map((field) => {
        const isGroup = isGroupType(field.type);
        const gridCols = getGridColsByWidth((field as FormField).style?.width);
        return (
          <Grid.Col
            key={field.id}
            span={{ base: 12, sm: gridCols }}
            hidden={!isFieldVisible(field.id)}
          >
            {isGroup ? (
              <GroupFieldWithLogic
                group={field as GroupFormField}
                activePageIndex={activePageIndex}
              />
            ) : (
              <FormFieldWithLogic field={field as FormField} activePageIndex={activePageIndex} />
            )}
          </Grid.Col>
        );
      })}
    </Grid>
  );
};

export default FormFields;
