import { GroupFieldType, type <PERSON>Form<PERSON>ield } from '@/types/form-builder';
import AddressField from './AddressFields';
import GroupFormFieldBase from './GroupFormFieldBase';
import NameField from './NameField';

interface GroupFieldProps {
  group: GroupFormField;
  activePageIndex?: number;
}

const GroupField = ({ group, activePageIndex }: GroupFieldProps) => {
  const renderGroupField = () => {
    switch (group.type) {
      case GroupFieldType.Address:
        return <AddressField group={group} />;
      case GroupFieldType.Name:
        return <NameField group={group} />;
      // Section
      default:
        return <GroupFormFieldBase group={group} activePageIndex={activePageIndex} />;
    }
  };

  return <>{renderGroupField()}</>;
};

export default GroupField;
