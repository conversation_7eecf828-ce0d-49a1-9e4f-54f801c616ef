import { useFormContext } from '@/contexts/FormContext';
import { useLinkClickHandler, useLinkClickStyles } from '@/hooks/useLinkClickHandler';
import type { FormField } from '@/types/form-builder';
import { hasRequiredRule } from '@/utils';
import { Checkbox } from '@mantine/core';
import { createStyles } from '@mantine/emotion';
import { useTranslate as useTranslation } from '@tolgee/react';
import LinkTextRenderer from './LinkTextRenderer';

interface SingleCheckboxWithLinkClickProps {
  field: FormField;
  inputProps: any;
  fieldStyles: any;
  activePageIndex?: number;
}

const useSingleCheckboxStyles = createStyles((theme) => ({
  questionText: {
    '& a': {
      color: theme.colors.blue[6],
      textDecoration: 'underline',
      cursor: 'pointer',
      '&:hover': {
        color: theme.colors.blue[7],
      },
    },
  },
}));

// Global handler for single checkbox link clicks
declare global {
  interface Window {
    handleSingleCheckboxLinkClick?: (linkId: string) => void;
  }
}

const SingleCheckboxWithLinkClick = ({
  field,
  inputProps,
  fieldStyles,
  activePageIndex,
}: SingleCheckboxWithLinkClickProps) => {
  const { t } = useTranslation('common');
  const { classes } = useSingleCheckboxStyles();
  const { classes: linkClasses } = useLinkClickStyles();
  const form = useFormContext();

  const linkClickHandler = useLinkClickHandler({
    fieldId: field.id,
    globalHandlerName: 'handleSingleCheckboxLinkClick',
    popoverClasses: linkClasses,
    activePageIndex,
  });

  const requireLinkClick = field.requireLinkClick ?? false;
  const links = linkClickHandler.extractLinks(field.options?.[0]?.label || '');
  const allLinksClicked = links.every((link) => linkClickHandler.clickedLinks.has(link.id));
  const isCheckboxEnabled = !requireLinkClick || allLinksClicked;

  // Get current checkbox value
  const currentValue = form.values?.[field.id] || false;
  const displayValue = linkClickHandler.attemptedCheck || currentValue;

  return (
    <div>
      <Checkbox
        {...inputProps}
        {...fieldStyles}
        description=''
        label={
          <LinkTextRenderer
            text={field.options?.[0]?.label || ''}
            fieldId={field.id}
            clickedLinks={linkClickHandler.clickedLinks}
            showPopoverForLinks={linkClickHandler.showPopoverForLinks}
            processQuestionText={linkClickHandler.processQuestionText}
            parseAttributes={linkClickHandler.parseAttributes}
            handleLinkClick={linkClickHandler.handleLinkClick}
            setShowPopoverForLinks={linkClickHandler.setShowPopoverForLinks}
            questionTextClass={classes.questionText}
            popoverClasses={linkClasses}
          />
        }
        checked={displayValue}
        onChange={(event) => {
          const isChecked = event.target.checked;

          if (isChecked && !isCheckboxEnabled) {
            linkClickHandler.handleAttemptedCheck(links);
          } else if (isChecked && isCheckboxEnabled) {
            linkClickHandler.setAttemptedCheck(false);
            form.setFieldValue(field.id, true);
            linkClickHandler.setShowPopoverForLinks(new Set());
          } else if (!isChecked) {
            linkClickHandler.setAttemptedCheck(false);
            form.setFieldValue(field.id, false);
            linkClickHandler.setShowPopoverForLinks(new Set());
          }
        }}
        sx={{
          '.mantine-Checkbox-error': {
            transform: 'translateX(-30px)',
          },
          '.mantine-Checkbox-inner': {
            svg: {
              color: 'white',
            },
          },
        }}
        required={hasRequiredRule(field)}
        error={inputProps.error}
      />
    </div>
  );
};

export default SingleCheckboxWithLinkClick;
