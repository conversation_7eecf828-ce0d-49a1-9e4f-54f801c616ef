import type { FormField } from '@/types/form-builder';
import { useCallback } from 'react';
import { useFormLogicContext } from '../../contexts/FormLogicContext';
import FormFieldBase from './FormFieldBase';

interface FormFieldWithLogicProps {
  field: FormField;
  // eslint-disable-next-line no-unused-vars
  onChange?: (field: FormField, value: any) => void;
  activePageIndex?: number;
}

const FormFieldWithLogic = ({ field, onChange, activePageIndex }: FormFieldWithLogicProps) => {
  const { isFieldVisible } = useFormLogicContext();

  const handleChange = useCallback(
    (field: FormField, value: any) => {
      onChange?.(field, value);
    },
    [onChange]
  );

  // Don't render if field is hidden by logic
  if (!isFieldVisible(field.id)) {
    return null;
  }

  return <FormFieldBase field={field} onChange={handleChange} activePageIndex={activePageIndex} />;
};

export default FormFieldWithLogic;
