import type { GroupFormField } from '@/types/form-builder';
import { useFormLogicContext } from '../../contexts/FormLogicContext';
import GroupField from './GroupField';

interface GroupFieldWithLogicProps {
  group: GroupFormField;
  activePageIndex?: number;
}

const GroupFieldWithLogic = ({ group, activePageIndex }: GroupFieldWithLogicProps) => {
  const { isFieldVisible } = useFormLogicContext();

  // Don't render if group is hidden by logic
  if (!isFieldVisible(group.id)) {
    return null;
  }

  return <GroupField group={group} activePageIndex={activePageIndex} />;
};

export default GroupFieldWithLogic;
