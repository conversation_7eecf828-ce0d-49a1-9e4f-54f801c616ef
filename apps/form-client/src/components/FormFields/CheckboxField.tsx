import { useFormContext } from '@/contexts/FormContext';
import { useFormSettings } from '@/contexts/FormSettingsContext';
import type { FormField } from '@/types/form-builder';
import { getGridColsByLayout, getOtherAnswerValue } from '@/utils';
import {
  Checkbox,
  type CheckboxGroupProps,
  Flex,
  Grid,
  Input,
  type InputWrapperProps,
  Text,
  TextInput,
  rem,
} from '@mantine/core';
import { createStyles } from '@mantine/emotion';
import { useTranslate as useTranslation } from '@tolgee/react';
import type React from 'react';
import { useCallback, useEffect, useRef, useState } from 'react';

interface CheckboxFieldProps extends Omit<CheckboxGroupProps, 'children'> {
  field: FormField;
  showHelperText?: boolean;
  activePageIndex?: number;
}

export const useCheckboxStyles = createStyles((theme, params: Record<string, any>) => ({
  checkboxGroup: {
    '& .mantine-CheckboxGroup-error, .mantine-RadioGroup-error': {
      marginTop: '10px',
    },
  },
  checkboxContainer: {
    display: 'flex',
    alignItems: 'center',
    border:
      params?.inputStyle === 'line'
        ? 'none'
        : params?.inputStyle.includes('classic')
          ? `1px solid ${theme.colors.decaLight[3]}`
          : '',
    borderRadius: params?.inputStyle.includes('rounded') ? theme.radius.xl : theme.radius.sm,
    padding: '8px 12px',
  },
  checkbox: {
    svg: {
      color: 'white',
    },
    '& label': {
      whiteSpace: 'nowrap',
      paddingBottom: 0,
    },
  },
  error: {
    color: theme.colors.red[6],
  },
  questionText: {
    marginBottom: rem(12),
    '& a': {
      color: theme.colors.blue[6],
      textDecoration: 'underline',
      '&:hover': {
        color: theme.colors.blue[7],
      },
    },
  },
  hintText: {
    fontStyle: 'italic',
    color: theme.colors.orange[6],
  },
}));

const CheckboxField = ({
  field,
  showHelperText = true,
  activePageIndex,
  ...inputProps
}: CheckboxFieldProps) => {
  const gridCols = getGridColsByLayout(field.layout);
  const { appearance } = useFormSettings();
  const { t } = useTranslation('common');
  const { classes } = useCheckboxStyles({
    inputStyle: appearance.defaultSettings.inputStyle,
  });
  const form = useFormContext();
  const otherInputRef = useRef<HTMLInputElement>(null);
  const [otherAnswer, setOtherAnswer] = useState('');
  const [clickedLinks, setClickedLinks] = useState<Set<string>>(new Set());
  const [showHint, setShowHint] = useState(false);
  const previousPageIndexRef = useRef<number | undefined>(activePageIndex);
  const checkboxContainerClass = `${(inputProps?.classNames as Record<string, string>)?.input} ${classes.checkboxContainer}`;
  const answers = form.values?.[field.id] ?? [];

  // Get the field-level setting
  const requireLinkClick = field.requireLinkClick ?? false;

  // Extract links from question text
  const extractLinks = useCallback(
    (text: string) => {
      const linkRegex = /<a[^>]+href=["']([^"']+)["'][^>]*>([^<]+)<\/a>/gi;
      const links: Array<{ url: string; text: string; id: string }> = [];
      let match: RegExpExecArray | null = linkRegex.exec(text);
      while (match !== null) {
        links.push({
          url: match[1],
          text: match[2],
          id: `${field.id}_${match[1]}`, // Unique per question + URL
        });
        match = linkRegex.exec(text);
      }
      return links;
    },
    [field.id]
  );

  const links = extractLinks(field.label);
  const allLinksClicked = links.every((link) => clickedLinks.has(link.id));
  const isCheckboxEnabled = !requireLinkClick || allLinksClicked;

  const handleLinkClick = useCallback((linkId: string) => {
    setClickedLinks((prev) => {
      const newSet = new Set(prev);
      newSet.add(linkId);
      return newSet;
    });
    setShowHint(false); // Hide hint when a link is clicked
  }, []);

  // Set up global handler for link clicks
  useEffect(() => {
    window.handleSingleCheckboxLinkClick = (linkId: string) => {
      handleLinkClick(linkId);
    };

    return () => {
      window.handleSingleCheckboxLinkClick = undefined;
    };
  }, [handleLinkClick]);

  // Close all popovers when page changes
  useEffect(() => {
    if (
      activePageIndex !== undefined &&
      previousPageIndexRef.current !== undefined &&
      activePageIndex !== previousPageIndexRef.current
    ) {
      setShowHint(false);
      setClickedLinks(new Set());
    }
    previousPageIndexRef.current = activePageIndex;
  }, [activePageIndex]);

  // Process question text to make links clickable
  const processQuestionText = useCallback(
    (text: string) => {
      return text.replace(/<a([^>]+href=["']([^"']+)["'][^>]*)>/g, (match, attrs, url) => {
        const linkId = `${field.id}_${url}`;
        return `<a${attrs} onclick="window.handleSingleCheckboxLinkClick && window.handleSingleCheckboxLinkClick('${linkId}')" target="_blank">`;
      });
    },
    [field.id]
  );

  const handleCheckboxChange = (checked: boolean, value) => {
    let newAnswers = [...answers];
    if (checked) {
      newAnswers.push({ value: value, is_other: false });
    } else {
      newAnswers = newAnswers.filter((answer) => answer.value !== value);
    }
    form.setFieldValue(field.id, newAnswers);
  };

  const handleOtherCheckboxChange = (checked: boolean) => {
    let newAnswers = [...answers];
    if (checked) {
      otherInputRef.current?.focus();
      newAnswers.push({ value: getOtherAnswerValue(otherAnswer), is_other: true });
    } else {
      newAnswers = newAnswers.filter((answer) => !answer.is_other);
    }
    form.setFieldValue(field.id, newAnswers);
  };

  const handleOtherInputChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    setOtherAnswer(event.target.value);
    const otherAnswer = answers.find((answer) => answer.is_other);
    if (otherAnswer) {
      otherAnswer.value = getOtherAnswerValue(event.target.value);
    }
    form.setFieldValue(field.id, answers);
  };

  // Render question text with clickable links
  const renderQuestionText = () => {
    if (!requireLinkClick || links.length === 0) {
      return null;
    }

    const processedText = processQuestionText(field.label);
    return (
      <div
        className={classes.questionText}
        dangerouslySetInnerHTML={{
          __html: processedText,
        }}
      />
    );
  };

  return (
    <Input.Wrapper
      {...(inputProps as InputWrapperProps)}
      error={showHint ? t('pleaseReviewLinkedDocumentsToEnableConsent') : inputProps.error}
    >
      {/* Question text with clickable links */}
      {renderQuestionText()}

      {showHelperText && (
        <Text fz={'12px'} c={appearance.defaultSettings.color}>
          {t('canSelectMultipleOptions')}
        </Text>
      )}
      <Grid my={rem(4)}>
        {field.options?.map((option) => {
          const isChecked = answers.some((answer) => answer.value === option.label);

          return (
            <Grid.Col key={option.label} span={gridCols} py={'6px'}>
              <Flex className={checkboxContainerClass}>
                <Checkbox
                  value={option.label}
                  label={option.label}
                  classNames={inputProps.classNames as Record<string, string>}
                  className={classes.checkbox}
                  checked={answers.find((answer) => answer.value === option.label)}
                  onChange={(event) => handleCheckboxChange(event.target.checked, option.label)}
                />
              </Flex>
            </Grid.Col>
          );
        })}
        {field.isOther && (
          <Grid.Col span={gridCols} py='6px'>
            <Flex align={'center'} gap='sm' className={checkboxContainerClass} py='3px'>
              <Checkbox
                value='other'
                label={t('otherOptionLabel')}
                classNames={inputProps.classNames as Record<string, string>}
                className={classes.checkbox}
                onChange={(event) => handleOtherCheckboxChange(event.target.checked)}
                checked={answers.find((answer) => answer.is_other)}
              />
              <TextInput
                variant='unstyled'
                size='xs'
                w='100%'
                ref={otherInputRef}
                classNames={inputProps.classNames}
                onChange={handleOtherInputChange}
              />
            </Flex>
          </Grid.Col>
        )}
      </Grid>
    </Input.Wrapper>
  );
};

export default CheckboxField;
