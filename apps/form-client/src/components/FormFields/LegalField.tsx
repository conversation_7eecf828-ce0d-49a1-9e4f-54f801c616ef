import { useFormContext } from '@/contexts/FormContext';
import { useLink<PERSON>lickHandler, useLinkClickStyles } from '@/hooks/useLinkClickHandler';
import type { FormField } from '@/types/form-builder';
import { hasRequiredRule } from '@/utils';
import { Checkbox, type InputWrapperProps, rem } from '@mantine/core';
import { createStyles } from '@mantine/emotion';
import { useTranslate as useTranslation } from '@tolgee/react';
import { useCallback } from 'react';
import LinkTextRenderer from './LinkTextRenderer';

interface LegalFieldProps extends InputWrapperProps {
  field: FormField;
  activePageIndex?: number;
}

export const useLegalStyles = createStyles((theme) => ({
  legalContainer: {
    display: 'flex',
    flexDirection: 'column',
    gap: rem(8),
  },
  questionText: {
    '& a': {
      color: theme.colors.blue[6],
      textDecoration: 'underline',
      cursor: 'pointer',
      '&:hover': {
        color: theme.colors.blue[7],
      },
    },
  },
  description: {
    fontSize: theme.fontSizes.sm,
    lineHeight: 1.4,
    color: theme.colors.decaGrey[6],
    marginBottom: rem(4),
  },
  checkbox: {
    svg: {
      color: 'white',
    },
  },
}));

// Global handler for link clicks
declare global {
  interface Window {
    handleLegalLinkClick?: (linkId: string) => void;
  }
}

const LegalField = ({ field, activePageIndex, ...inputProps }: LegalFieldProps) => {
  const { t } = useTranslation('common');
  const { classes } = useLegalStyles();
  const { classes: linkClasses } = useLinkClickStyles();
  const form = useFormContext();

  const linkClickHandler = useLinkClickHandler({
    fieldId: field.id,
    globalHandlerName: 'handleLegalLinkClick',
    popoverClasses: linkClasses,
    activePageIndex,
  });

  const requireLinkClick = field.requireLinkClick ?? false;
  const links = linkClickHandler.extractLinks(field.label);
  const allLinksClicked =
    links.length === 0 || links.every((link) => linkClickHandler.clickedLinks.has(link.id));
  const isCheckboxEnabled = !requireLinkClick || allLinksClicked;

  const handleCheckboxChange = useCallback(
    (checked: boolean) => {
      if (isCheckboxEnabled) {
        // Legal fields expect array structure like CheckboxField
        const newValue = checked ? [{ value: t('formField.legal.yes'), is_other: false }] : [];
        form.setFieldValue(field.id, newValue);
      }
    },
    [isCheckboxEnabled, t, form, field.id]
  );

  // Legal fields use array structure, check if the legal agreement is selected
  const currentValue = Array.isArray(form.values?.[field.id])
    ? form.values[field.id].some((item: any) => item.value === t('formField.legal.yes'))
    : false;

  const displayValue = linkClickHandler.attemptedCheck || currentValue;

  return (
    <div className={classes.legalContainer}>
      <LinkTextRenderer
        text={field.label}
        fieldId={field.id}
        clickedLinks={linkClickHandler.clickedLinks}
        showPopoverForLinks={linkClickHandler.showPopoverForLinks}
        processQuestionText={linkClickHandler.processQuestionText}
        parseAttributes={linkClickHandler.parseAttributes}
        handleLinkClick={linkClickHandler.handleLinkClick}
        setShowPopoverForLinks={linkClickHandler.setShowPopoverForLinks}
        questionTextClass={classes.questionText}
        popoverClasses={linkClasses}
      />

      {inputProps.description && (
        <div className={classes.description}>{inputProps.description}</div>
      )}

      <Checkbox
        checked={displayValue}
        onChange={(event) => {
          const isChecked = event.target.checked;

          if (isChecked && !isCheckboxEnabled) {
            linkClickHandler.handleAttemptedCheck(links);
          } else if (isChecked && isCheckboxEnabled) {
            linkClickHandler.setAttemptedCheck(false);
            handleCheckboxChange(true);
          } else if (!isChecked) {
            linkClickHandler.setAttemptedCheck(false);
            handleCheckboxChange(false);
          }
        }}
        label={t('formField.legal.yes')}
        className={`${classes.checkbox} `}
        required={hasRequiredRule(field)}
        error={inputProps.error}
      />
    </div>
  );
};

export default LegalField;
