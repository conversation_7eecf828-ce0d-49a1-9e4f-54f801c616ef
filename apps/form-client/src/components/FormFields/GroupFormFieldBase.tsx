import { useFormSettings } from '@/contexts/FormSettingsContext';
import useFieldStyles from '@/hooks/form/useFieldStyles';
import type { FormField, GroupFormField } from '@/types/form-builder';
import { getGridColsByWidth, isGroupType } from '@/utils';
import { Box, Grid } from '@mantine/core';
import FieldLabel from './FieldLabel';
import FormFieldBase from './FormFieldBase';
import GroupField from './GroupField';

interface GroupFormFieldProps {
  group: GroupFormField;
  // eslint-disable-next-line no-unused-vars
  onChange?: (field: FormField, value: any) => void;
  activePageIndex?: number;
}

const GroupFormFieldBase = ({ group, onChange, activePageIndex }: GroupFormFieldProps) => {
  const { appearance } = useFormSettings();
  const { classNames } = useFieldStyles({
    customize: appearance.customize,
    inputStyle: appearance?.defaultSettings?.inputStyle,
    formFieldStyle: appearance?.formFieldStyle,
    defaultSettings: appearance?.defaultSettings,
  });

  return (
    <Box w='100%'>
      {!!group.label && !group.hideLabel && (
        <FieldLabel className={classNames.label} label={group.label} />
      )}
      {group.descriptionEnabled && <FieldLabel mb='xs' label={group.description || ''} />}
      <Grid gutter={'xl'}>
        {(group.fields ?? [])
          .filter((field) => !field.isHide)
          .map((field) => {
            const gridCols = getGridColsByWidth(field.style?.width);
            return (
              <Grid.Col key={field.id} span={{ base: 12, sm: gridCols }}>
                {isGroupType(field.type) ? (
                  <GroupField
                    group={field as unknown as GroupFormField}
                    activePageIndex={activePageIndex}
                  />
                ) : (
                  <FormFieldBase
                    field={field}
                    onChange={onChange}
                    activePageIndex={activePageIndex}
                  />
                )}
              </Grid.Col>
            );
          })}
      </Grid>
    </Box>
  );
};

export default GroupFormFieldBase;
